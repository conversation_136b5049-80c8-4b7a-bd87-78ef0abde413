export default function SIEMDashboardPage() {
  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">SIEM Dashboard</h1>
        <p className="text-muted-foreground">
          Unified SIEM dashboard with Wazuh integration
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Wazuh Status</h3>
          <p className="text-2xl font-bold text-green-600">Active</p>
          <p className="text-sm text-muted-foreground">SIEM integration online</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Events/Hour</h3>
          <p className="text-2xl font-bold text-blue-600">1,247</p>
          <p className="text-sm text-muted-foreground">Security events processed</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Alerts</h3>
          <p className="text-2xl font-bold text-orange-600">12</p>
          <p className="text-sm text-muted-foreground">Active security alerts</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Agents</h3>
          <p className="text-2xl font-bold text-green-600">156</p>
          <p className="text-sm text-muted-foreground">Connected Wazuh agents</p>
        </div>
      </div>
    </div>
  )
}
