export type AssetCategory = 
  | "it"
  | "ot"
  | "iot"
  | "identity"
  | "application"
  | "vendor"
  | "process"

export type AssetType = 
  // IT Assets
  | "server"
  | "workstation"
  | "laptop"
  | "mobile-device"
  | "network-device"
  | "storage-device"
  | "printer"
  | "virtual-machine"
  | "container"
  // OT Assets
  | "plc"
  | "scada"
  | "hmi"
  | "dcs"
  | "safety-system"
  | "industrial-network"
  | "sensor"
  | "actuator"
  // IoT Devices
  | "smart-sensor"
  | "edge-device"
  | "gateway"
  | "smart-camera"
  | "environmental-monitor"
  | "access-control"
  // Identities
  | "user-account"
  | "service-account"
  | "privileged-account"
  | "shared-account"
  | "system-account"
  // Applications
  | "web-application"
  | "mobile-app"
  | "desktop-app"
  | "cloud-service"
  | "saas-platform"
  | "database"
  | "middleware"
  // Vendors
  | "technology-vendor"
  | "service-provider"
  | "consultant"
  | "contractor"
  | "supplier"
  // Processes
  | "business-process"
  | "it-process"
  | "security-process"
  | "compliance-process"

export type CriticalityLevel = "critical" | "high" | "medium" | "low"

export type SecurityStatus = "compliant" | "non-compliant" | "unknown" | "pending"

export type AssetStatus = "active" | "inactive" | "maintenance" | "decommissioned" | "planned"

export type DiscoveryMethod = "automated" | "manual" | "import" | "api" | "agent"

export type RiskLevel = "critical" | "high" | "medium" | "low"

export interface BaseAsset {
  id: string
  name: string
  description?: string
  category: AssetCategory
  assetType: AssetType
  status: AssetStatus
  criticalityLevel: CriticalityLevel
  securityStatus: SecurityStatus
  lastSeen: Date
  lastUpdated: Date
  riskScore: number // 0-100
  complianceStatus: {
    [framework: string]: {
      status: SecurityStatus
      lastAssessment?: Date
      gaps: number
    }
  }
  owner: string
  responsibleParty: string
  businessFunction?: string
  location?: string
  entityId: string
  discoveryMethod: DiscoveryMethod
  tags: string[]
  customFields?: Record<string, any>
  createdAt: Date
  updatedAt: Date
  createdBy: string
}

export interface ITAsset extends BaseAsset {
  category: "it"
  hardware?: {
    manufacturer?: string
    model?: string
    serialNumber?: string
    assetTag?: string
    warrantyExpiration?: Date
    purchaseDate?: Date
    cost?: number
  }
  software?: {
    operatingSystem?: string
    osVersion?: string
    installedSoftware: string[]
    licenses: string[]
    patches: string[]
    lastPatchDate?: Date
  }
  network?: {
    ipAddress?: string
    macAddress?: string
    hostname?: string
    domain?: string
    networkSegment?: string
    ports?: number[]
  }
  specifications?: {
    cpu?: string
    memory?: string
    storage?: string
    networkInterfaces?: string[]
  }
}

export interface OTAsset extends BaseAsset {
  category: "ot"
  industrial?: {
    protocol?: string
    firmwareVersion?: string
    vendor?: string
    model?: string
    serialNumber?: string
    installationDate?: Date
    maintenanceSchedule?: string
    safetyRating?: string
    operatingConditions?: string
  }
  connectivity?: {
    networkType?: string
    communicationProtocol?: string
    remoteAccess?: boolean
    encryptionEnabled?: boolean
  }
  operational?: {
    productionLine?: string
    facility?: string
    operationalHours?: string
    maintenanceWindow?: string
  }
}

export interface IoTAsset extends BaseAsset {
  category: "iot"
  device?: {
    manufacturer?: string
    model?: string
    firmwareVersion?: string
    hardwareVersion?: string
    deviceId?: string
    batteryLevel?: number
    lastCommunication?: Date
  }
  connectivity?: {
    connectionType?: string // WiFi, Cellular, LoRaWAN, etc.
    signalStrength?: number
    dataUsage?: number
    encryptionEnabled?: boolean
  }
  sensors?: {
    sensorTypes: string[]
    dataCollectionFrequency?: string
    dataRetention?: string
  }
}

export interface IdentityAsset extends BaseAsset {
  category: "identity"
  account?: {
    username?: string
    accountType?: string
    domain?: string
    lastLogin?: Date
    passwordLastChanged?: Date
    mfaEnabled?: boolean
    accountLocked?: boolean
    expirationDate?: Date
  }
  permissions?: {
    roles: string[]
    groups: string[]
    privileges: string[]
    accessLevel?: string
    dataAccess: string[]
  }
  activity?: {
    loginHistory: Date[]
    failedLogins?: number
    suspiciousActivity?: boolean
    lastPasswordChange?: Date
  }
}

export interface ApplicationAsset extends BaseAsset {
  category: "application"
  application?: {
    version?: string
    vendor?: string
    licenseType?: string
    licenseExpiration?: Date
    deploymentType?: string // on-premise, cloud, hybrid
    url?: string
    apiEndpoints?: string[]
  }
  security?: {
    authenticationMethod?: string
    encryptionInTransit?: boolean
    encryptionAtRest?: boolean
    vulnerabilities: string[]
    lastSecurityScan?: Date
    securityRating?: string
  }
  dependencies?: {
    databases: string[]
    services: string[]
    integrations: string[]
    thirdPartyComponents: string[]
  }
  performance?: {
    availability?: number
    responseTime?: number
    throughput?: number
    errorRate?: number
  }
}

export interface VendorAsset extends BaseAsset {
  category: "vendor"
  vendor?: {
    companyName?: string
    contactPerson?: string
    email?: string
    phone?: string
    address?: string
    website?: string
    vendorType?: string
  }
  contract?: {
    contractNumber?: string
    startDate?: Date
    endDate?: Date
    renewalDate?: Date
    contractValue?: number
    paymentTerms?: string
    slaRequirements?: string[]
  }
  risk?: {
    riskAssessmentDate?: Date
    riskLevel?: RiskLevel
    riskFactors: string[]
    mitigationMeasures: string[]
    insuranceCoverage?: boolean
    backgroundCheckCompleted?: boolean
  }
  compliance?: {
    certifications: string[]
    auditDate?: Date
    complianceGaps: string[]
    remediation: string[]
  }
}

export interface ProcessAsset extends BaseAsset {
  category: "process"
  process?: {
    processType?: string
    processOwner?: string
    processDescription?: string
    inputs: string[]
    outputs: string[]
    controls: string[]
    risks: string[]
  }
  workflow?: {
    steps: ProcessStep[]
    approvals: string[]
    automation?: boolean
    slaRequirements?: string[]
  }
  compliance?: {
    applicableFrameworks: string[]
    controlMappings: string[]
    auditFrequency?: string
    lastAudit?: Date
  }
  dependencies?: {
    upstreamProcesses: string[]
    downstreamProcesses: string[]
    supportingAssets: string[]
    criticalDependencies: string[]
  }
}

export interface ProcessStep {
  id: string
  name: string
  description?: string
  order: number
  responsible?: string
  duration?: number
  automated?: boolean
  controls: string[]
}

export type Asset = ITAsset | OTAsset | IoTAsset | IdentityAsset | ApplicationAsset | VendorAsset | ProcessAsset

export interface AssetRelationship {
  id: string
  sourceAssetId: string
  targetAssetId: string
  relationshipType: "depends-on" | "connects-to" | "manages" | "owns" | "uses" | "supports"
  description?: string
  strength: number // 1-10
  bidirectional: boolean
  createdAt: Date
  createdBy: string
}

export interface AssetDiscovery {
  id: string
  discoveryType: "network-scan" | "agent-based" | "api-integration" | "manual-entry"
  status: "running" | "completed" | "failed" | "scheduled"
  startTime: Date
  endTime?: Date
  assetsDiscovered: number
  assetsUpdated: number
  errors: string[]
  configuration: Record<string, any>
  nextRun?: Date
}

export interface AssetFilter {
  category?: AssetCategory[]
  assetType?: AssetType[]
  status?: AssetStatus[]
  criticalityLevel?: CriticalityLevel[]
  securityStatus?: SecurityStatus[]
  riskLevel?: RiskLevel[]
  owner?: string[]
  location?: string[]
  tags?: string[]
  lastSeenRange?: {
    startDate: Date
    endDate: Date
  }
  riskScoreRange?: {
    min: number
    max: number
  }
  searchQuery?: string
  entityId?: string
}

export interface AssetSortOptions {
  field: "name" | "assetType" | "criticalityLevel" | "securityStatus" | "lastSeen" | "riskScore" | "owner"
  direction: "asc" | "desc"
}
