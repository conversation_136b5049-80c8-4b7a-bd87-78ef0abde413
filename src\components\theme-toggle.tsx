"use client"

import * as React from "react"
import { <PERSON>, Sun, Monitor } from "lucide-react"
import { useTheme } from "next-themes"
import { useThemeToast } from "@/components/ui/custom-toast"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  <PERSON><PERSON>ip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

export function ThemeToggle() {
  const { theme, setTheme, systemTheme } = useTheme()
  const { showThemeToast } = useThemeToast()
  const [mounted, setMounted] = React.useState(false)

  // Ensure component is mounted to avoid hydration mismatch
  React.useEffect(() => {
    setMounted(true)
  }, [])

  // Cycle through themes: light -> dark -> system -> light
  const cycleTheme = React.useCallback(() => {
    const currentTheme = theme || "system"
    let nextTheme: string
    let toastMessage: string

    switch (currentTheme) {
      case "light":
        nextTheme = "dark"
        toastMessage = "Dark"
        break
      case "dark":
        nextTheme = "system"
        toastMessage = "System"
        break
      case "system":
      default:
        nextTheme = "light"
        toastMessage = "Light"
        break
    }

    setTheme(nextTheme)
    showThemeToast(toastMessage)
  }, [theme, setTheme, showThemeToast])

  // Global keyboard shortcut handler
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (
        event.key.toLowerCase() === "m" &&
        event.altKey &&
        !event.ctrlKey &&
        !event.metaKey &&
        !event.shiftKey
      ) {
        event.preventDefault()
        cycleTheme()
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [cycleTheme])

  // Get the appropriate icon based on current theme
  const getThemeIcon = () => {
    if (!mounted) return <Sun className="h-4 w-4" />

    const currentTheme = theme || "system"
    const resolvedTheme = currentTheme === "system" ? systemTheme : currentTheme

    switch (currentTheme) {
      case "light":
        return <Sun className="h-4 w-4 transition-all" />
      case "dark":
        return <Moon className="h-4 w-4 transition-all" />
      case "system":
        return <Monitor className="h-4 w-4 transition-all" />
      default:
        return <Sun className="h-4 w-4 transition-all" />
    }
  }

  return (
    <TooltipProvider delayDuration={300}>
      <Tooltip>
        <TooltipTrigger asChild>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={cycleTheme}
                className="h-8 px-2 gap-1.5"
              >
                {getThemeIcon()}
                <span className="text-xs text-muted-foreground font-mono">Alt+M</span>
                <span className="sr-only">Toggle theme (Alt + M)</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setTheme("light")}>
                <Sun className="mr-2 h-4 w-4" />
                Light
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme("dark")}>
                <Moon className="mr-2 h-4 w-4" />
                Dark
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme("system")}>
                <Monitor className="mr-2 h-4 w-4" />
                System
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </TooltipTrigger>
        <TooltipContent side="bottom">
          <p>Toggle theme (Alt + M)</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
