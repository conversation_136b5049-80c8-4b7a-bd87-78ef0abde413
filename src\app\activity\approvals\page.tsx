"use client"

import React from "react"
import { Use<PERSON><PERSON><PERSON><PERSON>, <PERSON>, Filter } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { ApprovalWorkflowInterface } from "@/components/activity/approval-workflow-interface"

import { 
  sampleApprovals,
  sampleActivityDashboard
} from "@/lib/activity-data"

export default function ActivityApprovalsPage() {
  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Approvals</h1>
            <p className="text-muted-foreground">
              Approval & review workflows interface with delegation capabilities and approval chain tracking
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Filter Approvals
            </Button>
            <Button variant="outline" size="sm">
              <Users className="mr-2 h-4 w-4" />
              Delegation Settings
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1">
        <ApprovalWorkflowInterface 
          approvals={sampleApprovals}
          pendingCount={sampleActivityDashboard.pendingApprovals}
        />
      </div>
    </div>
  )
}
