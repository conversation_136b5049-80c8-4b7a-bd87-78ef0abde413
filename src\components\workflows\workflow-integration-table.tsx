"use client"

import React from "react"
import { 
  MoreHorizontal, 
  CheckCircle, 
  XCircle, 
  Settings, 
  RefreshCw, 
  Search,
  Filter,
  Plus
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import { WorkflowIntegration, IntegrationType, IntegrationStatus } from "@/types/workflows"

interface WorkflowIntegrationTableProps {
  integrations: WorkflowIntegration[]
}

const statusConfig = {
  connected: { label: "Connected", variant: "default" as const, icon: CheckCircle },
  disconnected: { label: "Disconnected", variant: "secondary" as const, icon: XCircle },
  error: { label: "Error", variant: "destructive" as const, icon: XCircle },
  pending: { label: "Pending", variant: "secondary" as const, icon: RefreshCw },
}

const typeConfig = {
  "rest-api": { label: "REST API", color: "text-blue-600" },
  webhook: { label: "Webhook", color: "text-green-600" },
  database: { label: "Database", color: "text-purple-600" },
  email: { label: "Email", color: "text-orange-600" },
  slack: { label: "Slack", color: "text-pink-600" },
  teams: { label: "Teams", color: "text-blue-600" },
  jira: { label: "JIRA", color: "text-blue-600" },
  servicenow: { label: "ServiceNow", color: "text-red-600" },
  custom: { label: "Custom", color: "text-gray-600" },
}

export function WorkflowIntegrationTable({ integrations }: WorkflowIntegrationTableProps) {
  const [searchTerm, setSearchTerm] = React.useState("")
  const [typeFilter, setTypeFilter] = React.useState<string>("all")
  const [statusFilter, setStatusFilter] = React.useState<string>("all")
  const [sortBy, setSortBy] = React.useState<string>("name")

  const filteredIntegrations = React.useMemo(() => {
    return integrations.filter((integration) => {
      const matchesSearch = integration.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           integration.description.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesType = typeFilter === "all" || integration.type === typeFilter
      const matchesStatus = statusFilter === "all" || integration.status === statusFilter

      return matchesSearch && matchesType && matchesStatus
    }).sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name)
        case "type":
          return a.type.localeCompare(b.type)
        case "status":
          return a.status.localeCompare(b.status)
        case "lastSync":
          if (!a.lastSync && !b.lastSync) return 0
          if (!a.lastSync) return 1
          if (!b.lastSync) return -1
          return new Date(b.lastSync).getTime() - new Date(a.lastSync).getTime()
        default:
          return a.name.localeCompare(b.name)
      }
    })
  }, [integrations, searchTerm, typeFilter, statusFilter, sortBy])

  const formatDate = (dateString?: string) => {
    if (!dateString) return "Never"
    return new Date(dateString).toLocaleDateString()
  }

  return (
    <div className="space-y-4">
      {/* Filters and Search */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search integrations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 w-64"
            />
          </div>
          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="rest-api">REST API</SelectItem>
              <SelectItem value="webhook">Webhook</SelectItem>
              <SelectItem value="database">Database</SelectItem>
              <SelectItem value="email">Email</SelectItem>
              <SelectItem value="slack">Slack</SelectItem>
              <SelectItem value="teams">Teams</SelectItem>
              <SelectItem value="jira">JIRA</SelectItem>
              <SelectItem value="servicenow">ServiceNow</SelectItem>
              <SelectItem value="custom">Custom</SelectItem>
            </SelectContent>
          </Select>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="connected">Connected</SelectItem>
              <SelectItem value="disconnected">Disconnected</SelectItem>
              <SelectItem value="error">Error</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
            </SelectContent>
          </Select>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name">Name</SelectItem>
              <SelectItem value="type">Type</SelectItem>
              <SelectItem value="status">Status</SelectItem>
              <SelectItem value="lastSync">Last Sync</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            More Filters
          </Button>
          <Button size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Add Integration
          </Button>
        </div>
      </div>

      {/* Results Summary */}
      <div className="text-sm text-muted-foreground">
        Showing {filteredIntegrations.length} of {integrations.length} integrations
      </div>

      {/* Integrations Table */}
      <div className="border-0 rounded-none -mx-4">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Integration Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Authentication</TableHead>
              <TableHead>Last Sync</TableHead>
              <TableHead>Active</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredIntegrations.map((integration) => {
              const statusInfo = statusConfig[integration.status]
              const typeInfo = typeConfig[integration.type]
              const StatusIcon = statusInfo.icon
              
              return (
                <TableRow key={integration.id}>
                  <TableCell>
                    <div className="space-y-1">
                      <p className="font-medium">{integration.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {integration.description}
                      </p>
                      <p className="text-xs text-muted-foreground font-mono">
                        {integration.endpoint}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className={typeInfo.color}>
                      {typeInfo.label}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={statusInfo.variant}>
                      <StatusIcon className="mr-1 h-3 w-3" />
                      {statusInfo.label}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm capitalize">{integration.authentication}</span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">{formatDate(integration.lastSync)}</span>
                  </TableCell>
                  <TableCell>
                    <Badge variant={integration.isActive ? "default" : "secondary"}>
                      {integration.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem>
                          <RefreshCw className="mr-2 h-4 w-4" />
                          Test Connection
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Settings className="mr-2 h-4 w-4" />
                          Configure
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          {integration.isActive ? "Disable" : "Enable"}
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive">
                          Delete Integration
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
