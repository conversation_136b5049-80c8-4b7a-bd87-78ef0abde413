"use client"

import React from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { 
  AlertTriangle, 
  Clock, 
  Users, 
  Activity,
  Shield,
  TrendingUp,
  CheckCircle,
  XCircle,
  Play,
  Pause
} from "lucide-react"

import { sampleIncidents, sampleTeamMembers, remediationMetrics } from "@/lib/remediation-data"

const severityConfig = {
  low: { label: "Low", variant: "outline" as const, color: "text-green-600" },
  medium: { label: "Medium", variant: "secondary" as const, color: "text-yellow-600" },
  high: { label: "High", variant: "default" as const, color: "text-orange-600" },
  critical: { label: "Critical", variant: "destructive" as const, color: "text-red-600" },
}

const statusConfig = {
  new: { label: "New", variant: "secondary" as const, icon: Al<PERSON><PERSON>riangle },
  acknowledged: { label: "Acknowledged", variant: "default" as const, icon: Clock },
  investigating: { label: "Investigating", variant: "default" as const, icon: Activity },
  contained: { label: "Contained", variant: "outline" as const, icon: Shield },
  resolved: { label: "Resolved", variant: "outline" as const, icon: CheckCircle },
  closed: { label: "Closed", variant: "outline" as const, icon: CheckCircle },
}

export function IncidentCommandCenter() {
  const activeIncidents = sampleIncidents.filter(i => i.status !== "closed" && i.status !== "resolved")
  const criticalIncidents = activeIncidents.filter(i => i.severity === "critical")
  const highPriorityIncidents = activeIncidents.filter(i => i.severity === "high")

  return (
    <div className="space-y-6">
      {/* Critical Incidents Alert */}
      {criticalIncidents.length > 0 && (
        <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-700 dark:text-red-300">
              <AlertTriangle className="h-5 w-5" />
              Critical Incidents Requiring Immediate Attention
            </CardTitle>
            <CardDescription className="text-red-600 dark:text-red-400">
              {criticalIncidents.length} critical incident{criticalIncidents.length > 1 ? 's' : ''} active
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {criticalIncidents.map((incident) => (
                <div key={incident.id} className="flex items-center justify-between p-3 bg-white dark:bg-gray-900 rounded-lg border">
                  <div className="space-y-1">
                    <h4 className="font-medium">{incident.title}</h4>
                    <p className="text-sm text-muted-foreground">{incident.id} • {incident.assignedTeam}</p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>Detected: {new Date(incident.detectedAt).toLocaleString()}</span>
                      <span>•</span>
                      <span>Affected: {incident.affectedUsers || 0} users</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="destructive">Critical</Badge>
                    <Button size="sm">
                      <Activity className="mr-2 h-3 w-3" />
                      Manage
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Command Center Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Active Incidents
            </CardTitle>
            <CardDescription>Real-time incident status board</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              {Object.entries(
                activeIncidents.reduce((acc, incident) => {
                  acc[incident.status] = (acc[incident.status] || 0) + 1
                  return acc
                }, {} as Record<string, number>)
              ).map(([status, count]) => {
                const statusInfo = statusConfig[status as keyof typeof statusConfig]
                const StatusIcon = statusInfo?.icon || Activity
                
                return (
                  <div key={status} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <StatusIcon className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm capitalize">{status.replace('-', ' ')}</span>
                    </div>
                    <Badge variant="outline">{count}</Badge>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Team Status
            </CardTitle>
            <CardDescription>Analyst availability and workload</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              {sampleTeamMembers.slice(0, 4).map((member) => (
                <div key={member.id} className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">{member.name}</p>
                    <p className="text-xs text-muted-foreground">{member.role}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={member.availability === "available" ? "default" : 
                                  member.availability === "busy" ? "secondary" : "outline"}>
                      {member.availability}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {member.currentCases}/{member.maxCaseLoad}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Performance Metrics
            </CardTitle>
            <CardDescription>Key response indicators</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Response Time</span>
                  <span className="font-medium">15m avg</span>
                </div>
                <Progress value={85} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Resolution Rate</span>
                  <span className="font-medium">94.2%</span>
                </div>
                <Progress value={94} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>SLA Compliance</span>
                  <span className="font-medium">98.1%</span>
                </div>
                <Progress value={98} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent High Priority Incidents */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            High Priority Incidents
          </CardTitle>
          <CardDescription>Recent high and critical severity incidents requiring attention</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {highPriorityIncidents.concat(criticalIncidents).slice(0, 5).map((incident) => {
              const severityInfo = severityConfig[incident.severity]
              const statusInfo = statusConfig[incident.status]
              const StatusIcon = statusInfo.icon
              
              return (
                <div key={incident.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{incident.title}</h4>
                      <Badge variant={severityInfo.variant} className={severityInfo.color}>
                        {severityInfo.label}
                      </Badge>
                      <Badge variant={statusInfo.variant}>
                        <StatusIcon className="mr-1 h-3 w-3" />
                        {statusInfo.label}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{incident.description}</p>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>ID: {incident.id}</span>
                      <span>Team: {incident.assignedTeam}</span>
                      <span>Detected: {new Date(incident.detectedAt).toLocaleDateString()}</span>
                      {incident.affectedUsers && <span>Users: {incident.affectedUsers}</span>}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm">
                      <Activity className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      View Details
                    </Button>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Escalation Queue */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Escalation Queue
            </CardTitle>
            <CardDescription>Incidents approaching SLA deadlines</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {activeIncidents.filter(i => i.severity === "high" || i.severity === "critical").slice(0, 3).map((incident) => (
                <div key={incident.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">{incident.title}</p>
                    <p className="text-xs text-muted-foreground">{incident.id}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-orange-600">2h 15m</p>
                    <p className="text-xs text-muted-foreground">until SLA breach</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Containment Status
            </CardTitle>
            <CardDescription>Current containment operations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {activeIncidents.filter(i => i.containmentStatus !== "none").map((incident) => (
                <div key={incident.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">{incident.title}</p>
                    <p className="text-xs text-muted-foreground">{incident.id}</p>
                  </div>
                  <Badge variant={incident.containmentStatus === "full" ? "default" : 
                                incident.containmentStatus === "partial" ? "secondary" : "outline"}>
                    {incident.containmentStatus}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
