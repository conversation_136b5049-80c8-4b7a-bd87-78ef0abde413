"use client"

import React from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Filter, Play, Pause, Square } from "lucide-react"
import { PlaybookExecution } from "@/types/remediation"

interface PlaybookExecutionTableProps {
  executions: PlaybookExecution[]
}

export function PlaybookExecutionTable({ executions }: PlaybookExecutionTableProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input placeholder="Search executions..." className="pl-8 w-64" />
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
        </div>
      </div>
      
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            {executions.map((execution) => (
              <div key={execution.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">Execution {execution.id}</h4>
                    <Badge variant={
                      execution.status === "completed" ? "outline" :
                      execution.status === "running" ? "default" :
                      execution.status === "failed" ? "destructive" : "secondary"
                    }>
                      {execution.status}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Playbook: {execution.playbookId}
                  </p>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span>Started: {new Date(execution.startedAt).toLocaleString()}</span>
                    <span>By: {execution.executedBy}</span>
                    {execution.incidentId && <span>Incident: {execution.incidentId}</span>}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {execution.status === "running" && (
                    <>
                      <Button variant="outline" size="sm">
                        <Pause className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Square className="h-4 w-4" />
                      </Button>
                    </>
                  )}
                  <Button variant="outline" size="sm">View Details</Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
