"use client"

import { usePathname } from "next/navigation"
import { AppSidebar } from "@/components/app-sidebar"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { SystemTopBar } from "@/components/system-top-bar"
import { SubNavigation } from "@/components/top-navigation"
import { UnifiedStickySearch } from "@/components/unified-sticky-search"

interface ConditionalLayoutProps {
  children: React.ReactNode
}

export function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname()

  // Pages that should not have the sidebar layout
  const excludedPaths = ["/login"]

  // Check if we should hide sub-navigation (for artifacts, reports, frameworks, and portals routes)
  // Note: Activity, Workflows, Overview, Monitor, Assessments, Remediation, Policies, and Controls show sub-navigation like Assets
  // Reports, Frameworks, Artifacts, and Portals use sticky search instead of sub-navigation
  const shouldHideSubNavigation = pathname.startsWith("/artifacts") || pathname.startsWith("/reports") || pathname.startsWith("/frameworks") || pathname.startsWith("/portals")

  // Check if we should show sticky search (for modules that use it instead of sub-navigation)
  const shouldShowStickySearch = pathname.startsWith("/artifacts") || pathname.startsWith("/reports") || pathname.startsWith("/frameworks") || pathname.startsWith("/portals")

  // Check if this is a portal configurator page that needs full layout control
  const isPortalConfigurator = pathname.includes("/portals/configure/")

  // Check if this is a page that needs full height layout for sticky headers and proper overflow handling

  const shouldExcludeSidebar = excludedPaths.includes(pathname)

  if (shouldExcludeSidebar) {
    return <>{children}</>
  }

  return (
    <div className="pt-12"> {/* Add top padding for fixed system bar */}
      <SidebarProvider>
        {/* System Top Bar - Fixed at top */}
        <SystemTopBar />

        {/* Main Layout with Sidebar */}
        <AppSidebar />
        <SidebarInset className="flex flex-col h-[calc(100vh-3rem)]">
          {/* Sub-Navigation Bar - sticky positioning below system top bar */}
          {!shouldHideSubNavigation && (
            <header className="sticky top-0 z-40 flex h-12 shrink-0 items-center border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-4">
              <SubNavigation />
            </header>
          )}

          {/* Sticky Search Bar - positioned below sub-navigation (or at top if no sub-nav) */}
          {shouldShowStickySearch && (
            <UnifiedStickySearch />
          )}

          {/* Main Content - simplified layout */}
          {isPortalConfigurator ? (
            <div className="flex flex-1 flex-col overflow-hidden">
              {children}
            </div>
          ) : (
            <div className="flex flex-1 flex-col min-h-0">
              <div className="flex-1 overflow-y-auto px-4 py-4">
                {children}
              </div>
            </div>
          )}
        </SidebarInset>
      </SidebarProvider>
    </div>
  )
}
