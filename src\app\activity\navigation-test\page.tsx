"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { CheckCircle, ExternalLink, Navigation } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { navigationData } from "@/lib/navigation-data"

export default function ActivityNavigationTestPage() {
  const pathname = usePathname()
  
  // Find the Activity navigation item
  const activityNavItem = navigationData.navMain.find(item => item.title === "Activity")
  
  const testPages = [
    { title: "Tasks", url: "/activity/tasks", description: "Personal task management dashboard" },
    { title: "Notifications", url: "/activity/notifications", description: "Role-based notification center" },
    { title: "Approvals", url: "/activity/approvals", description: "Approval & review workflows interface" },
    { title: "Communications", url: "/activity/communications", description: "Communication hub for team collaboration" },
    { title: "Analytics", url: "/activity/analytics", description: "Performance analytics dashboard" },
    { title: "Knowledge", url: "/activity/knowledge", description: "Knowledge context panel" },
    { title: "Navigation Test", url: "/activity/navigation-test", description: "This navigation test page" },
  ]

  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Activity Navigation Test</h1>
            <p className="text-muted-foreground">
              Test page to verify sub-navigation persistence across all Activity module pages
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 space-y-6">
        {/* Current Navigation State */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Navigation className="h-5 w-5" />
              Current Navigation State
            </CardTitle>
            <CardDescription>
              Current pathname and detected navigation configuration
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">Current Pathname</div>
                <div className="font-mono text-sm bg-muted p-2 rounded">{pathname}</div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">Activity Base URL</div>
                <div className="font-mono text-sm bg-muted p-2 rounded">{activityNavItem?.url}</div>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">Sub-Navigation Items</div>
              <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
                {activityNavItem?.subModules?.map((subModule) => {
                  const isActive = pathname === subModule.url || 
                    (subModule.url !== "/activity" && pathname.startsWith(subModule.url + "/"))
                  
                  return (
                    <div
                      key={subModule.url}
                      className={`p-3 border rounded-lg ${
                        isActive 
                          ? "border-primary bg-primary/5" 
                          : "border-muted"
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="font-medium text-sm">{subModule.title}</div>
                        {isActive && <CheckCircle className="h-4 w-4 text-primary" />}
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {subModule.url}
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Test Navigation Links */}
        <Card>
          <CardHeader>
            <CardTitle>Test Navigation Links</CardTitle>
            <CardDescription>
              Click these links to test sub-navigation persistence across different activity pages
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
              {testPages.map((page) => {
                const isCurrentPage = pathname === page.url
                
                return (
                  <Card key={page.url} className={`transition-all ${isCurrentPage ? 'ring-2 ring-primary' : ''}`}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1 flex-1">
                          <div className="font-medium text-sm">{page.title}</div>
                          <div className="text-xs text-muted-foreground">{page.description}</div>
                          <div className="font-mono text-xs text-muted-foreground">{page.url}</div>
                        </div>
                        <div className="flex items-center gap-2 ml-2">
                          {isCurrentPage && <Badge variant="secondary" className="text-xs">Current</Badge>}
                          <Button asChild size="sm" variant={isCurrentPage ? "secondary" : "outline"}>
                            <Link href={page.url}>
                              <ExternalLink className="h-3 w-3" />
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Sub-Navigation Requirements */}
        <Card>
          <CardHeader>
            <CardTitle>Sub-Navigation Requirements</CardTitle>
            <CardDescription>
              Verification checklist for sub-navigation functionality
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <div className="font-medium">Sub-navigation appears on all activity pages</div>
                  <div className="text-sm text-muted-foreground">
                    The sub-navigation bar should be visible at the top of every activity-related page
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <div className="font-medium">Correct tab highlighted for each page</div>
                  <div className="text-sm text-muted-foreground">
                    When on /activity/tasks, the "Tasks" tab should be highlighted
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <div className="font-medium">Navigation persists across all sub-pages</div>
                  <div className="text-sm text-muted-foreground">
                    Sub-navigation remains visible and functional on all activity-related pages
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <div className="font-medium">All tabs are clickable and functional</div>
                  <div className="text-sm text-muted-foreground">
                    Each tab in the sub-navigation should navigate to the correct page
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Testing Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div>
                <strong>1. Visual Verification:</strong> Confirm that the sub-navigation bar appears at the top of this page and all other activity pages.
              </div>
              <div>
                <strong>2. Navigation Testing:</strong> Click through the test links above and verify that:
                <ul className="list-disc list-inside ml-4 mt-2 space-y-1">
                  <li>The sub-navigation remains visible on every page</li>
                  <li>The correct tab is highlighted for each page</li>
                  <li>All tabs are clickable and functional</li>
                  <li>Navigation works seamlessly between all Activity sub-sections</li>
                </ul>
              </div>
              <div>
                <strong>3. Persistence Testing:</strong> Navigate to different Activity pages using both the sub-navigation tabs and the test links to ensure consistent behavior.
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
