"use client"

import React from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, Save, AlertTriangle, Calendar, Users, Shield } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

export default function CreateIncidentPage() {
  const router = useRouter()
  const [formData, setFormData] = React.useState({
    title: "",
    description: "",
    severity: "",
    category: "",
    source: "",
    assignedTeam: "",
    assignedTo: "",
    affectedSystems: [] as string[],
    affectedUsers: "",
    businessImpact: "",
    technicalImpact: "",
    tags: [] as string[]
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Here you would typically submit the form data to your API
    console.log("Creating incident:", formData)
    // Redirect back to incidents list
    router.push("/remediation/incidents")
  }

  const handleCancel = () => {
    router.back()
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Create New Incident</h1>
              <p className="text-muted-foreground">
                Report and initialize a new security incident
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button onClick={handleSubmit}>
              <Save className="mr-2 h-4 w-4" />
              Create Incident
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 space-y-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Incident Details
              </CardTitle>
              <CardDescription>
                Provide the basic information about this security incident
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Incident Title *</Label>
                <Input
                  id="title"
                  placeholder="Brief description of the incident"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  placeholder="Detailed description of what happened, when it was discovered, and initial observations"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={4}
                  required
                />
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="severity">Severity *</Label>
                  <Select value={formData.severity} onValueChange={(value) => setFormData({ ...formData, severity: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select severity" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="critical">Critical</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="security">Security</SelectItem>
                      <SelectItem value="data-breach">Data Breach</SelectItem>
                      <SelectItem value="malware">Malware</SelectItem>
                      <SelectItem value="phishing">Phishing</SelectItem>
                      <SelectItem value="ddos">DDoS</SelectItem>
                      <SelectItem value="insider-threat">Insider Threat</SelectItem>
                      <SelectItem value="availability">Availability</SelectItem>
                      <SelectItem value="performance">Performance</SelectItem>
                      <SelectItem value="compliance">Compliance</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="source">Detection Source *</Label>
                  <Select value={formData.source} onValueChange={(value) => setFormData({ ...formData, source: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="How was it detected?" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="automated">Automated Detection</SelectItem>
                      <SelectItem value="user-report">User Report</SelectItem>
                      <SelectItem value="monitoring">System Monitoring</SelectItem>
                      <SelectItem value="external">External Report</SelectItem>
                      <SelectItem value="audit">Security Audit</SelectItem>
                      <SelectItem value="threat-intel">Threat Intelligence</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Assignment and Impact */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Assignment & Impact
              </CardTitle>
              <CardDescription>
                Assign the incident and assess its impact
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="assignedTeam">Assigned Team *</Label>
                  <Select value={formData.assignedTeam} onValueChange={(value) => setFormData({ ...formData, assignedTeam: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select response team" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="SOC Team Alpha">SOC Team Alpha</SelectItem>
                      <SelectItem value="SOC Team Beta">SOC Team Beta</SelectItem>
                      <SelectItem value="Network Security">Network Security</SelectItem>
                      <SelectItem value="Digital Forensics">Digital Forensics</SelectItem>
                      <SelectItem value="Incident Response">Incident Response</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="assignedTo">Assigned Analyst</Label>
                  <Select value={formData.assignedTo} onValueChange={(value) => setFormData({ ...formData, assignedTo: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Assign to analyst (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="<EMAIL>">Alice Chen</SelectItem>
                      <SelectItem value="<EMAIL>">Bob Martinez</SelectItem>
                      <SelectItem value="<EMAIL>">Diana Foster</SelectItem>
                      <SelectItem value="<EMAIL>">Charlie Wong</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="businessImpact">Business Impact</Label>
                  <Select value={formData.businessImpact} onValueChange={(value) => setFormData({ ...formData, businessImpact: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select impact" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="minimal">Minimal</SelectItem>
                      <SelectItem value="moderate">Moderate</SelectItem>
                      <SelectItem value="significant">Significant</SelectItem>
                      <SelectItem value="severe">Severe</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="technicalImpact">Technical Impact</Label>
                  <Select value={formData.technicalImpact} onValueChange={(value) => setFormData({ ...formData, technicalImpact: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select impact" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="minimal">Minimal</SelectItem>
                      <SelectItem value="moderate">Moderate</SelectItem>
                      <SelectItem value="significant">Significant</SelectItem>
                      <SelectItem value="severe">Severe</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="affectedUsers">Affected Users</Label>
                  <Input
                    id="affectedUsers"
                    type="number"
                    placeholder="Number of affected users"
                    value={formData.affectedUsers}
                    onChange={(e) => setFormData({ ...formData, affectedUsers: e.target.value })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Affected Systems and Tags */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Affected Systems & Classification
              </CardTitle>
              <CardDescription>
                Identify affected systems and add relevant tags
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Affected Systems</Label>
                <div className="flex flex-wrap gap-2">
                  {["file-server-01", "database-prod", "email-server", "vpn-gateway", "web-app-prod", "admin-workstation", "load-balancer", "backup-server"].map((system) => (
                    <Badge
                      key={system}
                      variant={formData.affectedSystems.includes(system) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        const systems = formData.affectedSystems.includes(system)
                          ? formData.affectedSystems.filter(s => s !== system)
                          : [...formData.affectedSystems, system]
                        setFormData({ ...formData, affectedSystems: systems })
                      }}
                    >
                      {system}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Tags</Label>
                <div className="flex flex-wrap gap-2">
                  {["high-priority", "customer-impact", "data-loss", "system-compromise", "external-threat", "insider-threat", "compliance-issue", "financial-impact"].map((tag) => (
                    <Badge
                      key={tag}
                      variant={formData.tags.includes(tag) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        const tags = formData.tags.includes(tag)
                          ? formData.tags.filter(t => t !== tag)
                          : [...formData.tags, tag]
                        setFormData({ ...formData, tags })
                      }}
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </form>
      </div>
    </div>
  )
}
