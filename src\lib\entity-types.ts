import { LucideIcon } from "lucide-react"

export type EntityType = 
  | "headquarters"
  | "subsidiary" 
  | "department"
  | "branch"
  | "division"
  | "business-unit"
  | "region"
  | "project"

export type EntityStatus = 
  | "active"
  | "inactive" 
  | "setup"
  | "archived"

export type EntityPlan = 
  | "enterprise"
  | "professional"
  | "standard"
  | "basic"

export interface Entity {
  id: string
  name: string
  type: EntityType
  status: EntityStatus
  plan: EntityPlan
  logo?: LucideIcon
  description?: string
  parentEntityId?: string
  createdAt: Date
  updatedAt: Date
  settings: {
    timezone: string
    currency: string
    locale: string
    complianceFrameworks: string[]
    riskTolerance: "low" | "medium" | "high"
  }
  stats: {
    totalAssets: number
    complianceScore: number
    activeRisks: number
    lastAssessment?: Date
  }
}

export interface EntityHierarchy {
  entity: Entity
  children: EntityHierarchy[]
  level: number
}

export const ENTITY_TYPE_LABELS: Record<EntityType, string> = {
  "headquarters": "Headquarters",
  "subsidiary": "Subsidiary",
  "department": "Department", 
  "branch": "Branch",
  "division": "Division",
  "business-unit": "Business Unit",
  "region": "Region",
  "project": "Project"
}

export const ENTITY_STATUS_LABELS: Record<EntityStatus, string> = {
  "active": "Active",
  "inactive": "Inactive",
  "setup": "Setup Required",
  "archived": "Archived"
}

export const ENTITY_PLAN_LABELS: Record<EntityPlan, string> = {
  "enterprise": "Enterprise",
  "professional": "Professional", 
  "standard": "Standard",
  "basic": "Basic"
}
