"use client"

import React from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, Save, FileText, Calendar, Users, AlertTriangle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

export default function CreateInvestigationPage() {
  const router = useRouter()
  const [formData, setFormData] = React.useState({
    title: "",
    description: "",
    incidentId: "",
    priority: "",
    investigator: "",
    team: "",
    methodology: [] as string[],
    scope: {
      systems: [] as string[],
      timeRange: {
        start: "",
        end: ""
      },
      dataTypes: [] as string[],
      locations: [] as string[]
    }
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Here you would typically submit the form data to your API
    console.log("Creating investigation:", formData)
    // Redirect back to investigations list
    router.push("/remediation/investigation")
  }

  const handleCancel = () => {
    router.back()
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Create New Investigation</h1>
              <p className="text-muted-foreground">
                Initialize a new DFIR investigation case
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button onClick={handleSubmit}>
              <Save className="mr-2 h-4 w-4" />
              Create Investigation
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 space-y-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Basic Information
              </CardTitle>
              <CardDescription>
                Provide the fundamental details for this investigation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="title">Investigation Title *</Label>
                  <Input
                    id="title"
                    placeholder="Enter investigation title"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="incidentId">Related Incident ID</Label>
                  <Select value={formData.incidentId} onValueChange={(value) => setFormData({ ...formData, incidentId: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select incident (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="INC-2024-001">INC-2024-001 - Data Exfiltration</SelectItem>
                      <SelectItem value="INC-2024-002">INC-2024-002 - Phishing Campaign</SelectItem>
                      <SelectItem value="INC-2024-003">INC-2024-003 - DDoS Attack</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  placeholder="Describe the investigation objectives and scope"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  required
                />
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="priority">Priority *</Label>
                  <Select value={formData.priority} onValueChange={(value) => setFormData({ ...formData, priority: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="critical">Critical</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="investigator">Lead Investigator *</Label>
                  <Select value={formData.investigator} onValueChange={(value) => setFormData({ ...formData, investigator: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Assign investigator" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="<EMAIL>">Diana Foster</SelectItem>
                      <SelectItem value="<EMAIL>">Alice Chen</SelectItem>
                      <SelectItem value="<EMAIL>">Bob Martinez</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="team">Investigation Team</Label>
                  <Select value={formData.team} onValueChange={(value) => setFormData({ ...formData, team: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select team" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Digital Forensics">Digital Forensics</SelectItem>
                      <SelectItem value="SOC Team Alpha">SOC Team Alpha</SelectItem>
                      <SelectItem value="SOC Team Beta">SOC Team Beta</SelectItem>
                      <SelectItem value="Network Security">Network Security</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Investigation Scope */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Investigation Scope
              </CardTitle>
              <CardDescription>
                Define the scope and boundaries of this investigation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Investigation Start Date</Label>
                  <Input
                    id="startDate"
                    type="datetime-local"
                    value={formData.scope.timeRange.start}
                    onChange={(e) => setFormData({
                      ...formData,
                      scope: {
                        ...formData.scope,
                        timeRange: { ...formData.scope.timeRange, start: e.target.value }
                      }
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endDate">Investigation End Date</Label>
                  <Input
                    id="endDate"
                    type="datetime-local"
                    value={formData.scope.timeRange.end}
                    onChange={(e) => setFormData({
                      ...formData,
                      scope: {
                        ...formData.scope,
                        timeRange: { ...formData.scope.timeRange, end: e.target.value }
                      }
                    })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Affected Systems</Label>
                <div className="flex flex-wrap gap-2">
                  {["file-server-01", "database-prod", "admin-workstation", "vpn-gateway", "email-server"].map((system) => (
                    <Badge
                      key={system}
                      variant={formData.scope.systems.includes(system) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        const systems = formData.scope.systems.includes(system)
                          ? formData.scope.systems.filter(s => s !== system)
                          : [...formData.scope.systems, system]
                        setFormData({
                          ...formData,
                          scope: { ...formData.scope, systems }
                        })
                      }}
                    >
                      {system}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Data Types to Investigate</Label>
                <div className="flex flex-wrap gap-2">
                  {["file-access-logs", "network-traffic", "system-logs", "database-logs", "email-logs", "authentication-logs"].map((dataType) => (
                    <Badge
                      key={dataType}
                      variant={formData.scope.dataTypes.includes(dataType) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        const dataTypes = formData.scope.dataTypes.includes(dataType)
                          ? formData.scope.dataTypes.filter(d => d !== dataType)
                          : [...formData.scope.dataTypes, dataType]
                        setFormData({
                          ...formData,
                          scope: { ...formData.scope, dataTypes }
                        })
                      }}
                    >
                      {dataType}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Investigation Locations</Label>
                <div className="flex flex-wrap gap-2">
                  {["on-premises", "cloud-storage", "remote-endpoints", "mobile-devices", "third-party-services"].map((location) => (
                    <Badge
                      key={location}
                      variant={formData.scope.locations.includes(location) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        const locations = formData.scope.locations.includes(location)
                          ? formData.scope.locations.filter(l => l !== location)
                          : [...formData.scope.locations, location]
                        setFormData({
                          ...formData,
                          scope: { ...formData.scope, locations }
                        })
                      }}
                    >
                      {location}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Methodology */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Investigation Methodology
              </CardTitle>
              <CardDescription>
                Select the forensic methodologies and frameworks to follow
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label>Forensic Frameworks</Label>
                <div className="flex flex-wrap gap-2">
                  {["NIST SP 800-86", "ISO 27037", "ACPO Guidelines", "RFC 3227", "SANS DFIR", "Custom Methodology"].map((methodology) => (
                    <Badge
                      key={methodology}
                      variant={formData.methodology.includes(methodology) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        const methodologies = formData.methodology.includes(methodology)
                          ? formData.methodology.filter(m => m !== methodology)
                          : [...formData.methodology, methodology]
                        setFormData({ ...formData, methodology: methodologies })
                      }}
                    >
                      {methodology}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </form>
      </div>
    </div>
  )
}
