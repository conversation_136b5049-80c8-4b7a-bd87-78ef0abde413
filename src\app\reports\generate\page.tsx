"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { 
  ArrowLeft, 
  FileText, 
  Calendar, 
  Clock,
  Download,
  Settings,
  Users,
  Database
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"

import { sampleReportTemplates } from "@/lib/reports-data"
import { ReportType, ReportFramework, TargetAudience, ReportFormat } from "@/types/reports"

export default function GenerateReportPage() {
  const router = useRouter()
  const [selectedTemplate, setSelectedTemplate] = useState<string>("")
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    reportType: "" as ReportType,
    framework: "" as ReportFramework,
    targetAudience: "" as TargetAudience,
    format: "pdf" as ReportFormat,
    startDate: "",
    endDate: "",
    sections: [] as string[],
    scheduledGeneration: false,
    frequency: "monthly" as "daily" | "weekly" | "monthly" | "quarterly",
    emailDelivery: "",
    shareWithStakeholders: false
  })

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId)
    const template = sampleReportTemplates.find(t => t.id === templateId)
    if (template) {
      setFormData(prev => ({
        ...prev,
        name: template.name,
        description: template.description,
        reportType: template.reportType,
        framework: template.framework,
        targetAudience: template.targetAudience,
        sections: template.sections.map(s => s.id)
      }))
    }
  }

  const handleGenerate = () => {
    // In a real implementation, this would submit the form data
    console.log("Generating report with data:", formData)
    router.push("/reports")
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Generate Report</h1>
          <p className="text-muted-foreground">
            Create a new report using templates or custom configuration
          </p>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Template Library */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Template Library
              </CardTitle>
              <CardDescription>
                Choose from pre-built templates or start from scratch
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Button
                  variant={selectedTemplate === "" ? "default" : "outline"}
                  className="w-full justify-start"
                  onClick={() => setSelectedTemplate("")}
                >
                  <Settings className="mr-2 h-4 w-4" />
                  Custom Report
                </Button>
              </div>
              
              <div className="space-y-2">
                <Label className="text-sm font-medium">Framework Templates</Label>
                {sampleReportTemplates.map((template) => (
                  <Button
                    key={template.id}
                    variant={selectedTemplate === template.id ? "default" : "outline"}
                    className="w-full justify-start text-left"
                    onClick={() => handleTemplateSelect(template.id)}
                  >
                    <div className="space-y-1">
                      <div className="font-medium">{template.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {template.framework} • {template.targetAudience}
                      </div>
                    </div>
                  </Button>
                ))}
              </div>

              <div className="pt-4 border-t">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Quick Stats</Label>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <div>• {sampleReportTemplates.length} templates available</div>
                    <div>• Most used: SOC 2 Type II</div>
                    <div>• Avg generation time: 5-10 min</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Configuration Form */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Report Configuration</CardTitle>
              <CardDescription>
                Configure your report settings and data sources
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="general" className="space-y-6">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="general">General</TabsTrigger>
                  <TabsTrigger value="data">Data & Sources</TabsTrigger>
                  <TabsTrigger value="delivery">Delivery</TabsTrigger>
                  <TabsTrigger value="schedule">Schedule</TabsTrigger>
                </TabsList>

                <TabsContent value="general" className="space-y-4">
                  <div className="grid gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Report Name</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter report name"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Enter report description"
                        rows={3}
                      />
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label>Report Type</Label>
                        <Select
                          value={formData.reportType}
                          onValueChange={(value: ReportType) => 
                            setFormData(prev => ({ ...prev, reportType: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="compliance">Compliance</SelectItem>
                            <SelectItem value="executive">Executive</SelectItem>
                            <SelectItem value="operational">Operational</SelectItem>
                            <SelectItem value="stakeholder">Stakeholder</SelectItem>
                            <SelectItem value="custom">Custom</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Framework</Label>
                        <Select
                          value={formData.framework}
                          onValueChange={(value: ReportFramework) => 
                            setFormData(prev => ({ ...prev, framework: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select framework" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="NIST CSF 2.0">NIST CSF 2.0</SelectItem>
                            <SelectItem value="ISO 27001">ISO 27001</SelectItem>
                            <SelectItem value="SOC 2">SOC 2</SelectItem>
                            <SelectItem value="PCI DSS">PCI DSS</SelectItem>
                            <SelectItem value="HIPAA">HIPAA</SelectItem>
                            <SelectItem value="Custom">Custom</SelectItem>
                            <SelectItem value="N/A">N/A</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label>Target Audience</Label>
                        <Select
                          value={formData.targetAudience}
                          onValueChange={(value: TargetAudience) => 
                            setFormData(prev => ({ ...prev, targetAudience: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select audience" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="internal">Internal</SelectItem>
                            <SelectItem value="auditor">Auditor</SelectItem>
                            <SelectItem value="customer">Customer</SelectItem>
                            <SelectItem value="regulator">Regulator</SelectItem>
                            <SelectItem value="board">Board</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Output Format</Label>
                        <Select
                          value={formData.format}
                          onValueChange={(value: ReportFormat) => 
                            setFormData(prev => ({ ...prev, format: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select format" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="pdf">PDF</SelectItem>
                            <SelectItem value="html">HTML</SelectItem>
                            <SelectItem value="excel">Excel</SelectItem>
                            <SelectItem value="csv">CSV</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="data" className="space-y-4">
                  <div className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor="startDate">Start Date</Label>
                        <Input
                          id="startDate"
                          type="date"
                          value={formData.startDate}
                          onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="endDate">End Date</Label>
                        <Input
                          id="endDate"
                          type="date"
                          value={formData.endDate}
                          onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                        />
                      </div>
                    </div>

                    <div className="space-y-3">
                      <Label>Data Sources</Label>
                      <div className="grid gap-2">
                        {[
                          "Control Testing Results",
                          "Risk Assessment Data", 
                          "Incident Reports",
                          "Asset Inventory",
                          "Policy Compliance",
                          "Audit Evidence"
                        ].map((source) => (
                          <div key={source} className="flex items-center space-x-2">
                            <Checkbox id={source} />
                            <Label htmlFor={source} className="text-sm">
                              {source}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="delivery" className="space-y-4">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Delivery (Optional)</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.emailDelivery}
                        onChange={(e) => setFormData(prev => ({ ...prev, emailDelivery: e.target.value }))}
                        placeholder="Enter email addresses (comma separated)"
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="shareStakeholders"
                        checked={formData.shareWithStakeholders}
                        onCheckedChange={(checked) => 
                          setFormData(prev => ({ ...prev, shareWithStakeholders: !!checked }))
                        }
                      />
                      <Label htmlFor="shareStakeholders">
                        Share with relevant stakeholders automatically
                      </Label>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="schedule" className="space-y-4">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="scheduled"
                        checked={formData.scheduledGeneration}
                        onCheckedChange={(checked) => 
                          setFormData(prev => ({ ...prev, scheduledGeneration: !!checked }))
                        }
                      />
                      <Label htmlFor="scheduled">
                        Enable scheduled generation
                      </Label>
                    </div>

                    {formData.scheduledGeneration && (
                      <div className="space-y-2">
                        <Label>Frequency</Label>
                        <Select
                          value={formData.frequency}
                          onValueChange={(value: "daily" | "weekly" | "monthly" | "quarterly") => 
                            setFormData(prev => ({ ...prev, frequency: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="daily">Daily</SelectItem>
                            <SelectItem value="weekly">Weekly</SelectItem>
                            <SelectItem value="monthly">Monthly</SelectItem>
                            <SelectItem value="quarterly">Quarterly</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>

              <div className="flex justify-end gap-4 pt-6 border-t">
                <Button variant="outline" onClick={() => router.back()}>
                  Cancel
                </Button>
                <Button onClick={handleGenerate}>
                  <Download className="mr-2 h-4 w-4" />
                  Generate Report
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
