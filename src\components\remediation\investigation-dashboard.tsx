"use client"

import React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { 
  FileText, 
  Database, 
  Shield, 
  Search,
  Clock,
  Activity,
  CheckCircle,
  AlertTriangle
} from "lucide-react"

import { sampleInvestigations, sampleEvidence, sampleIOCs, remediationMetrics } from "@/lib/remediation-data"

export function InvestigationDashboard() {
  const activeInvestigations = sampleInvestigations.filter(i => i.status === "active")
  const recentEvidence = sampleEvidence.slice(0, 3)
  const criticalIOCs = sampleIOCs.filter(i => i.severity === "critical")

  return (
    <div className="space-y-6">
      {/* Investigation Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Active Cases
            </CardTitle>
            <CardDescription>DFIR-IRIS case management</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-2xl font-bold">{remediationMetrics.investigations.active}</div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Case Progress</span>
                <span className="font-medium">68%</span>
              </div>
              <Progress value={68} className="h-2" />
            </div>
            <div className="text-xs text-muted-foreground">
              {remediationMetrics.investigations.total} total cases
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Evidence Repository
            </CardTitle>
            <CardDescription>Digital evidence management</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-2xl font-bold">{remediationMetrics.investigations.evidenceCollected}</div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Storage Used</span>
                <span className="font-medium">2.4TB</span>
              </div>
              <Progress value={45} className="h-2" />
            </div>
            <div className="text-xs text-muted-foreground">
              +12 items this week
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              IOC Database
            </CardTitle>
            <CardDescription>Threat intelligence indicators</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-2xl font-bold">{remediationMetrics.investigations.iocsIdentified}</div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Blocked IOCs</span>
                <span className="font-medium">{sampleIOCs.filter(i => i.blocked).length}</span>
              </div>
              <Progress value={75} className="h-2" />
            </div>
            <div className="text-xs text-muted-foreground">
              +5 new indicators
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Active Investigations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Active Investigations
          </CardTitle>
          <CardDescription>Currently ongoing DFIR cases</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {activeInvestigations.map((investigation) => (
              <div key={investigation.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{investigation.title}</h4>
                    <Badge variant={investigation.priority === "critical" ? "destructive" : "default"}>
                      {investigation.priority}
                    </Badge>
                    <Badge variant="outline">
                      {investigation.status}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{investigation.description}</p>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span>ID: {investigation.id}</span>
                    <span>Investigator: {investigation.investigator}</span>
                    <span>Started: {new Date(investigation.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="sm">
                    <Search className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    View Case
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Evidence and Critical IOCs */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Recent Evidence
            </CardTitle>
            <CardDescription>Latest collected digital evidence</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentEvidence.map((evidence) => (
                <div key={evidence.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">{evidence.name}</p>
                    <p className="text-xs text-muted-foreground">{evidence.type}</p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>Size: {(evidence.size / 1024 / 1024 / 1024).toFixed(1)}GB</span>
                      <span>•</span>
                      <span>Collected: {new Date(evidence.collectedAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                  <Badge variant="outline">
                    {evidence.type}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Critical IOCs
            </CardTitle>
            <CardDescription>High-severity threat indicators</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {criticalIOCs.map((ioc) => (
                <div key={ioc.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">{ioc.value}</p>
                    <p className="text-xs text-muted-foreground">{ioc.description}</p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>Type: {ioc.type}</span>
                      <span>•</span>
                      <span>Confidence: {ioc.confidence}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="destructive">
                      {ioc.severity}
                    </Badge>
                    {ioc.blocked && (
                      <Badge variant="outline" className="text-green-600">
                        Blocked
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Investigation Metrics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Avg Case Duration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2.1d</div>
            <p className="text-xs text-muted-foreground">-0.3d from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Evidence Chain Integrity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">100%</div>
            <p className="text-xs text-muted-foreground">All chains verified</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">IOC Hit Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">87%</div>
            <p className="text-xs text-muted-foreground">+5% from last week</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Reports Generated</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">23</div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
