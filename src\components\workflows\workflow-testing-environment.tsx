"use client"

import React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { 
  Play, 
  Square, 
  RotateCcw, 
  CheckCircle, 
  XCircle, 
  Clock,
  Bug,
  TestTube,
  FileText,
  Settings,
  Download,
  Upload
} from "lucide-react"

interface TestCase {
  id: string
  name: string
  description: string
  status: "pending" | "running" | "passed" | "failed"
  duration?: number
  lastRun?: string
  variables: Record<string, any>
  expectedOutcome: string
  actualOutcome?: string
  errorMessage?: string
}

const sampleTestCases: TestCase[] = [
  {
    id: "test-001",
    name: "Happy Path - Complete Workflow",
    description: "Test the complete workflow execution with valid inputs",
    status: "passed",
    duration: 2340,
    lastRun: "2024-01-16T15:30:00Z",
    variables: {
      requestType: "compliance-assessment",
      priority: "high",
      assignee: "<EMAIL>"
    },
    expectedOutcome: "Workflow completes successfully with approval",
    actualOutcome: "Workflow completed successfully"
  },
  {
    id: "test-002",
    name: "Invalid Input Validation",
    description: "Test workflow behavior with invalid input parameters",
    status: "failed",
    duration: 1200,
    lastRun: "2024-01-16T14:20:00Z",
    variables: {
      requestType: "",
      priority: "invalid",
      assignee: null
    },
    expectedOutcome: "Workflow should fail with validation error",
    actualOutcome: "Workflow failed",
    errorMessage: "Invalid priority value: 'invalid'"
  },
  {
    id: "test-003",
    name: "Timeout Scenario",
    description: "Test workflow behavior when tasks exceed timeout limits",
    status: "running",
    variables: {
      requestType: "risk-assessment",
      priority: "low",
      timeout: 30
    },
    expectedOutcome: "Workflow should timeout and escalate"
  },
  {
    id: "test-004",
    name: "Approval Rejection Path",
    description: "Test workflow when approval is rejected",
    status: "pending",
    variables: {
      requestType: "policy-update",
      priority: "normal",
      approver: "<EMAIL>"
    },
    expectedOutcome: "Workflow should handle rejection and notify requester"
  }
]

const statusConfig = {
  pending: { label: "Pending", variant: "secondary" as const, icon: Clock },
  running: { label: "Running", variant: "default" as const, icon: Play },
  passed: { label: "Passed", variant: "outline" as const, icon: CheckCircle },
  failed: { label: "Failed", variant: "destructive" as const, icon: XCircle },
}

export function WorkflowTestingEnvironment() {
  const [activeTab, setActiveTab] = React.useState("test-cases")
  const [selectedTest, setSelectedTest] = React.useState<TestCase | null>(null)
  const [testVariables, setTestVariables] = React.useState("{}")

  const formatDuration = (duration?: number) => {
    if (!duration) return "N/A"
    const seconds = Math.floor(duration / 1000)
    const minutes = Math.floor(seconds / 60)
    return `${minutes}m ${seconds % 60}s`
  }

  const runTest = (testCase: TestCase) => {
    console.log("Running test:", testCase.name)
    // Simulate test execution
  }

  const runAllTests = () => {
    console.log("Running all tests")
    // Simulate running all tests
  }

  return (
    <div className="space-y-6">
      {/* Testing Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            Workflow Testing Environment
          </CardTitle>
          <CardDescription>
            Test your workflows with different scenarios and validate behavior
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Button onClick={runAllTests}>
              <Play className="mr-2 h-4 w-4" />
              Run All Tests
            </Button>
            <Button variant="outline">
              <RotateCcw className="mr-2 h-4 w-4" />
              Reset Environment
            </Button>
            <Button variant="outline">
              <Upload className="mr-2 h-4 w-4" />
              Import Test Cases
            </Button>
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export Results
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Testing Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="test-cases">Test Cases</TabsTrigger>
          <TabsTrigger value="manual-test">Manual Testing</TabsTrigger>
          <TabsTrigger value="results">Test Results</TabsTrigger>
          <TabsTrigger value="coverage">Coverage Report</TabsTrigger>
        </TabsList>

        <TabsContent value="test-cases" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Test Cases List */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Test Cases</CardTitle>
                <CardDescription>Automated test scenarios</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {sampleTestCases.map((testCase) => {
                  const statusInfo = statusConfig[testCase.status]
                  const StatusIcon = statusInfo.icon
                  
                  return (
                    <div
                      key={testCase.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedTest?.id === testCase.id ? "border-primary bg-primary/5" : "hover:bg-accent"
                      }`}
                      onClick={() => setSelectedTest(testCase)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-sm">{testCase.name}</h4>
                        <Badge variant={statusInfo.variant}>
                          <StatusIcon className="mr-1 h-3 w-3" />
                          {statusInfo.label}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground mb-2">
                        {testCase.description}
                      </p>
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>Duration: {formatDuration(testCase.duration)}</span>
                        {testCase.lastRun && (
                          <span>Last run: {new Date(testCase.lastRun).toLocaleDateString()}</span>
                        )}
                      </div>
                    </div>
                  )
                })}
              </CardContent>
            </Card>

            {/* Test Details */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Test Details</CardTitle>
                <CardDescription>
                  {selectedTest ? `Configure and run ${selectedTest.name}` : "Select a test case to view details"}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedTest ? (
                  <>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Description</label>
                      <p className="text-sm text-muted-foreground">{selectedTest.description}</p>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Expected Outcome</label>
                      <p className="text-sm text-muted-foreground">{selectedTest.expectedOutcome}</p>
                    </div>

                    {selectedTest.actualOutcome && (
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Actual Outcome</label>
                        <p className="text-sm text-muted-foreground">{selectedTest.actualOutcome}</p>
                      </div>
                    )}

                    {selectedTest.errorMessage && (
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Error Message</label>
                        <p className="text-sm text-red-600">{selectedTest.errorMessage}</p>
                      </div>
                    )}

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Test Variables</label>
                      <Textarea
                        value={JSON.stringify(selectedTest.variables, null, 2)}
                        onChange={(e) => {
                          try {
                            const parsed = JSON.parse(e.target.value)
                            setSelectedTest({
                              ...selectedTest,
                              variables: parsed
                            })
                          } catch {
                            // Invalid JSON, ignore
                          }
                        }}
                        className="font-mono text-xs"
                        rows={6}
                      />
                    </div>

                    <div className="flex gap-2">
                      <Button 
                        onClick={() => runTest(selectedTest)}
                        disabled={selectedTest.status === "running"}
                      >
                        <Play className="mr-2 h-4 w-4" />
                        Run Test
                      </Button>
                      <Button variant="outline">
                        <Settings className="mr-2 h-4 w-4" />
                        Edit
                      </Button>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8">
                    <Bug className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">Select a test case to view details</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="manual-test" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Manual Testing</CardTitle>
              <CardDescription>Test your workflow manually with custom inputs</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Test Variables (JSON)</label>
                <Textarea
                  value={testVariables}
                  onChange={(e) => setTestVariables(e.target.value)}
                  placeholder='{"requestType": "compliance-assessment", "priority": "high"}'
                  className="font-mono"
                  rows={8}
                />
              </div>
              
              <div className="flex gap-2">
                <Button>
                  <Play className="mr-2 h-4 w-4" />
                  Start Manual Test
                </Button>
                <Button variant="outline">
                  <FileText className="mr-2 h-4 w-4" />
                  Load Template
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Test Results Summary</CardTitle>
              <CardDescription>Overview of test execution results</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">1</div>
                  <div className="text-sm text-muted-foreground">Passed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">1</div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">1</div>
                  <div className="text-sm text-muted-foreground">Running</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-600">1</div>
                  <div className="text-sm text-muted-foreground">Pending</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="coverage" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Test Coverage Report</CardTitle>
              <CardDescription>Workflow path and decision coverage analysis</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Path Coverage</span>
                    <span className="font-medium">75%</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-primary h-2 rounded-full" style={{ width: "75%" }} />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Decision Coverage</span>
                    <span className="font-medium">60%</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-primary h-2 rounded-full" style={{ width: "60%" }} />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Error Handling</span>
                    <span className="font-medium">50%</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-primary h-2 rounded-full" style={{ width: "50%" }} />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
