export interface Incident {
  id: string
  title: string
  description: string
  severity: IncidentSeverity
  status: IncidentStatus
  category: IncidentCategory
  source: IncidentSource
  createdAt: string
  updatedAt: string
  detectedAt: string
  acknowledgedAt?: string
  resolvedAt?: string
  closedAt?: string
  assignedTo?: string
  assignedTeam: string
  reporter: string
  affectedSystems: string[]
  affectedUsers?: number
  businessImpact: BusinessImpact
  technicalImpact: TechnicalImpact
  containmentStatus: ContainmentStatus
  timeline: IncidentTimelineEntry[]
  tags: string[]
  artifacts: string[]
  relatedIncidents: string[]
  playbooks: string[]
  estimatedCost?: number
  actualCost?: number
  lessons?: string
  postMortemRequired: boolean
  postMortemCompleted: boolean
  complianceImpact: ComplianceImpact
}

export interface IncidentTimelineEntry {
  id: string
  timestamp: string
  actor: string
  action: string
  description: string
  type: TimelineEntryType
  evidence?: string[]
  automated: boolean
}

export interface Investigation {
  id: string
  incidentId: string
  title: string
  description: string
  status: InvestigationStatus
  priority: InvestigationPriority
  investigator: string
  team: string
  createdAt: string
  updatedAt: string
  startedAt?: string
  completedAt?: string
  methodology: string[]
  scope: InvestigationScope
  findings: InvestigationFinding[]
  evidence: Evidence[]
  iocs: IOC[]
  timeline: InvestigationTimelineEntry[]
  recommendations: string[]
  reportGenerated: boolean
  reportPath?: string
  chainOfCustody: ChainOfCustodyEntry[]
}

export interface Evidence {
  id: string
  investigationId: string
  name: string
  description: string
  type: EvidenceType
  source: string
  collectedAt: string
  collectedBy: string
  hash: string
  size: number
  location: string
  tags: string[]
  metadata: Record<string, any>
  chainOfCustody: ChainOfCustodyEntry[]
  analysisResults?: AnalysisResult[]
}

export interface IOC {
  id: string
  type: IOCType
  value: string
  description: string
  confidence: IOCConfidence
  severity: IOCSeverity
  source: string
  firstSeen: string
  lastSeen: string
  tags: string[]
  relatedIOCs: string[]
  investigations: string[]
  blocked: boolean
  falsePositive: boolean
  context: Record<string, any>
}

export interface RemediationPlaybook {
  id: string
  name: string
  description: string
  category: PlaybookCategory
  version: string
  author: string
  createdAt: string
  updatedAt: string
  isActive: boolean
  automated: boolean
  approvalRequired: boolean
  estimatedDuration: number
  steps: PlaybookStep[]
  triggers: PlaybookTrigger[]
  variables: PlaybookVariable[]
  integrations: string[]
  usageCount: number
  successRate: number
  tags: string[]
}

export interface PlaybookExecution {
  id: string
  playbookId: string
  incidentId?: string
  status: ExecutionStatus
  startedAt: string
  completedAt?: string
  executedBy: string
  approvedBy?: string
  variables: Record<string, any>
  steps: ExecutionStep[]
  logs: ExecutionLog[]
  errors: ExecutionError[]
  metrics: ExecutionMetrics
}

export interface RemediationMetrics {
  incidents: {
    total: number
    open: number
    inProgress: number
    resolved: number
    closed: number
    avgResolutionTime: number
    avgResponseTime: number
    slaBreaches: number
  }
  investigations: {
    total: number
    active: number
    completed: number
    avgDuration: number
    evidenceCollected: number
    iocsIdentified: number
  }
  playbooks: {
    total: number
    automated: number
    executed: number
    successRate: number
    avgExecutionTime: number
  }
  team: {
    activeAnalysts: number
    workload: number
    avgCaseLoad: number
    certifications: number
  }
}

export interface TeamMember {
  id: string
  name: string
  email: string
  role: TeamRole
  skills: string[]
  certifications: string[]
  availability: AvailabilityStatus
  currentCases: number
  maxCaseLoad: number
  shiftSchedule: ShiftSchedule
  contactMethods: ContactMethod[]
  lastActive: string
}

// Enums and Types
export type IncidentSeverity = "low" | "medium" | "high" | "critical"
export type IncidentStatus = "new" | "acknowledged" | "investigating" | "contained" | "resolved" | "closed"
export type IncidentCategory = "security" | "availability" | "performance" | "data-breach" | "malware" | "phishing" | "ddos" | "insider-threat" | "compliance" | "other"
export type IncidentSource = "automated" | "user-report" | "monitoring" | "external" | "audit" | "threat-intel"

export type BusinessImpact = "none" | "minimal" | "moderate" | "significant" | "severe"
export type TechnicalImpact = "none" | "minimal" | "moderate" | "significant" | "severe"
export type ContainmentStatus = "none" | "partial" | "full" | "eradicated"

export type TimelineEntryType = "detection" | "acknowledgment" | "investigation" | "containment" | "eradication" | "recovery" | "communication" | "escalation"

export type InvestigationStatus = "planned" | "active" | "suspended" | "completed" | "cancelled"
export type InvestigationPriority = "low" | "medium" | "high" | "critical"

export type EvidenceType = "disk-image" | "memory-dump" | "network-capture" | "log-file" | "document" | "screenshot" | "configuration" | "database-export" | "mobile-backup" | "cloud-data"

export type IOCType = "ip" | "domain" | "url" | "hash" | "email" | "file-path" | "registry-key" | "user-agent" | "certificate" | "mutex"
export type IOCConfidence = "low" | "medium" | "high" | "confirmed"
export type IOCSeverity = "info" | "low" | "medium" | "high" | "critical"

export type PlaybookCategory = "incident-response" | "containment" | "eradication" | "recovery" | "communication" | "forensics" | "threat-hunting" | "compliance"
export type ExecutionStatus = "pending" | "running" | "paused" | "completed" | "failed" | "cancelled"

export type TeamRole = "analyst" | "senior-analyst" | "lead" | "manager" | "director" | "specialist"
export type AvailabilityStatus = "available" | "busy" | "away" | "offline"

// Supporting interfaces
export interface InvestigationScope {
  systems: string[]
  timeRange: {
    start: string
    end: string
  }
  dataTypes: string[]
  locations: string[]
}

export interface InvestigationFinding {
  id: string
  title: string
  description: string
  severity: string
  confidence: string
  evidence: string[]
  recommendations: string[]
}

export interface InvestigationTimelineEntry {
  id: string
  timestamp: string
  investigator: string
  action: string
  description: string
  evidence?: string[]
}

export interface ChainOfCustodyEntry {
  timestamp: string
  actor: string
  action: string
  location: string
  notes?: string
}

export interface AnalysisResult {
  tool: string
  timestamp: string
  results: Record<string, any>
  analyst: string
}

export interface PlaybookStep {
  id: string
  name: string
  description: string
  type: "manual" | "automated" | "approval"
  order: number
  required: boolean
  estimatedDuration: number
  instructions?: string
  automation?: {
    tool: string
    command: string
    parameters: Record<string, any>
  }
}

export interface PlaybookTrigger {
  type: "manual" | "incident-severity" | "incident-category" | "ioc-match" | "time-based"
  conditions: Record<string, any>
}

export interface PlaybookVariable {
  name: string
  type: string
  required: boolean
  defaultValue?: any
  description: string
}

export interface ExecutionStep {
  stepId: string
  status: "pending" | "running" | "completed" | "failed" | "skipped"
  startedAt?: string
  completedAt?: string
  output?: any
  error?: string
}

export interface ExecutionLog {
  timestamp: string
  level: "info" | "warn" | "error" | "debug"
  message: string
  context?: Record<string, any>
}

export interface ExecutionError {
  timestamp: string
  stepId: string
  error: string
  stackTrace?: string
}

export interface ExecutionMetrics {
  totalDuration: number
  stepDurations: Record<string, number>
  resourceUsage: Record<string, any>
}

export interface ComplianceImpact {
  frameworks: string[]
  reportingRequired: boolean
  notificationDeadlines: string[]
  regulatoryBodies: string[]
}

export interface ShiftSchedule {
  timezone: string
  shifts: {
    day: string
    start: string
    end: string
  }[]
}

export interface ContactMethod {
  type: "email" | "phone" | "slack" | "teams" | "pager"
  value: string
  priority: number
}
