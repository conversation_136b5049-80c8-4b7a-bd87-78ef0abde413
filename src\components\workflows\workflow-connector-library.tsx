"use client"

import React from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Puzzle, 
  Plus, 
  Settings, 
  CheckCircle, 
  Search,
  Filter,
  Download,
  ExternalLink
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface Connector {
  id: string
  name: string
  description: string
  category: string
  type: string
  version: string
  provider: string
  isInstalled: boolean
  isConfigured: boolean
  icon: string
  documentation?: string
  configuration?: {
    endpoint?: string
    authentication?: string
    parameters?: Record<string, any>
  }
}

const sampleConnectors: Connector[] = [
  {
    id: "slack-connector",
    name: "Slack Notifications",
    description: "Send notifications and messages to Slack channels and users",
    category: "communication",
    type: "webhook",
    version: "2.1.0",
    provider: "Slack Technologies",
    isInstalled: true,
    isConfigured: true,
    icon: "💬",
    documentation: "https://docs.slack.com/workflows"
  },
  {
    id: "jira-connector",
    name: "JIRA Integration",
    description: "Create, update, and manage JIRA issues from workflows",
    category: "project-management",
    type: "rest-api",
    version: "3.2.1",
    provider: "Atlassian",
    isInstalled: true,
    isConfigured: false,
    icon: "🎫",
    documentation: "https://developer.atlassian.com/cloud/jira/"
  },
  {
    id: "email-connector",
    name: "Email Service",
    description: "Send emails with templates and attachments",
    category: "communication",
    type: "smtp",
    version: "1.5.0",
    provider: "GRCOS",
    isInstalled: true,
    isConfigured: true,
    icon: "📧"
  },
  {
    id: "servicenow-connector",
    name: "ServiceNow",
    description: "Integrate with ServiceNow for incident and change management",
    category: "itsm",
    type: "rest-api",
    version: "4.0.2",
    provider: "ServiceNow",
    isInstalled: false,
    isConfigured: false,
    icon: "🔧",
    documentation: "https://docs.servicenow.com/bundle/tokyo-application-development/page/integrate/inbound-rest/concept/c_RESTAPI.html"
  },
  {
    id: "teams-connector",
    name: "Microsoft Teams",
    description: "Send messages and notifications to Teams channels",
    category: "communication",
    type: "webhook",
    version: "1.8.0",
    provider: "Microsoft",
    isInstalled: false,
    isConfigured: false,
    icon: "👥",
    documentation: "https://docs.microsoft.com/en-us/microsoftteams/platform/webhooks-and-connectors/"
  },
  {
    id: "database-connector",
    name: "Database Query",
    description: "Execute SQL queries and stored procedures",
    category: "data",
    type: "database",
    version: "2.3.0",
    provider: "GRCOS",
    isInstalled: true,
    isConfigured: true,
    icon: "🗄️"
  },
  {
    id: "aws-s3-connector",
    name: "AWS S3",
    description: "Upload, download, and manage files in Amazon S3",
    category: "storage",
    type: "rest-api",
    version: "3.1.5",
    provider: "Amazon Web Services",
    isInstalled: false,
    isConfigured: false,
    icon: "☁️",
    documentation: "https://docs.aws.amazon.com/s3/latest/API/"
  },
  {
    id: "ldap-connector",
    name: "LDAP/Active Directory",
    description: "Query and manage user accounts in LDAP/AD",
    category: "identity",
    type: "ldap",
    version: "1.9.0",
    provider: "GRCOS",
    isInstalled: true,
    isConfigured: false,
    icon: "👤"
  }
]

const categoryConfig = {
  communication: { label: "Communication", color: "bg-blue-100 text-blue-800" },
  "project-management": { label: "Project Management", color: "bg-purple-100 text-purple-800" },
  itsm: { label: "IT Service Management", color: "bg-green-100 text-green-800" },
  data: { label: "Data & Analytics", color: "bg-orange-100 text-orange-800" },
  storage: { label: "Storage", color: "bg-yellow-100 text-yellow-800" },
  identity: { label: "Identity & Access", color: "bg-red-100 text-red-800" },
  security: { label: "Security", color: "bg-red-100 text-red-800" },
  monitoring: { label: "Monitoring", color: "bg-indigo-100 text-indigo-800" },
}

export function WorkflowConnectorLibrary() {
  const [searchTerm, setSearchTerm] = React.useState("")
  const [categoryFilter, setCategoryFilter] = React.useState<string>("all")
  const [statusFilter, setStatusFilter] = React.useState<string>("all")
  const [sortBy, setSortBy] = React.useState<string>("name")

  const filteredConnectors = React.useMemo(() => {
    return sampleConnectors.filter((connector) => {
      const matchesSearch = connector.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           connector.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           connector.provider.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesCategory = categoryFilter === "all" || connector.category === categoryFilter
      const matchesStatus = statusFilter === "all" || 
                           (statusFilter === "installed" && connector.isInstalled) ||
                           (statusFilter === "configured" && connector.isConfigured) ||
                           (statusFilter === "available" && !connector.isInstalled)

      return matchesSearch && matchesCategory && matchesStatus
    }).sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name)
        case "category":
          return a.category.localeCompare(b.category)
        case "provider":
          return a.provider.localeCompare(b.provider)
        default:
          return a.name.localeCompare(b.name)
      }
    })
  }, [searchTerm, categoryFilter, statusFilter, sortBy])

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search connectors..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 w-80"
            />
          </div>
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="communication">Communication</SelectItem>
              <SelectItem value="project-management">Project Management</SelectItem>
              <SelectItem value="itsm">IT Service Management</SelectItem>
              <SelectItem value="data">Data & Analytics</SelectItem>
              <SelectItem value="storage">Storage</SelectItem>
              <SelectItem value="identity">Identity & Access</SelectItem>
              <SelectItem value="security">Security</SelectItem>
              <SelectItem value="monitoring">Monitoring</SelectItem>
            </SelectContent>
          </Select>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="installed">Installed</SelectItem>
              <SelectItem value="configured">Configured</SelectItem>
              <SelectItem value="available">Available</SelectItem>
            </SelectContent>
          </Select>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name">Name</SelectItem>
              <SelectItem value="category">Category</SelectItem>
              <SelectItem value="provider">Provider</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button variant="outline">
          <Filter className="mr-2 h-4 w-4" />
          More Filters
        </Button>
      </div>

      {/* Results Summary */}
      <div className="text-sm text-muted-foreground">
        Showing {filteredConnectors.length} of {sampleConnectors.length} connectors
      </div>

      {/* Connector Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredConnectors.map((connector) => {
          const categoryInfo = categoryConfig[connector.category as keyof typeof categoryConfig]
          
          return (
            <Card key={connector.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">{connector.icon}</div>
                    <div className="space-y-1">
                      <CardTitle className="text-lg">{connector.name}</CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge className={categoryInfo?.color || "bg-gray-100 text-gray-800"}>
                          {categoryInfo?.label || connector.category}
                        </Badge>
                        <span className="text-xs text-muted-foreground">v{connector.version}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    {connector.isInstalled && (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    )}
                    {connector.isConfigured && (
                      <Settings className="h-4 w-4 text-blue-600" />
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <CardDescription className="text-sm">
                  {connector.description}
                </CardDescription>

                {/* Provider and Type */}
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>by {connector.provider}</span>
                  <Badge variant="outline" className="text-xs">
                    {connector.type}
                  </Badge>
                </div>

                {/* Status Indicators */}
                <div className="flex items-center gap-2">
                  <Badge variant={connector.isInstalled ? "default" : "secondary"}>
                    {connector.isInstalled ? "Installed" : "Available"}
                  </Badge>
                  {connector.isInstalled && (
                    <Badge variant={connector.isConfigured ? "default" : "outline"}>
                      {connector.isConfigured ? "Configured" : "Needs Setup"}
                    </Badge>
                  )}
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2 pt-2">
                  {!connector.isInstalled ? (
                    <Button size="sm" className="flex-1">
                      <Plus className="mr-2 h-3 w-3" />
                      Install
                    </Button>
                  ) : !connector.isConfigured ? (
                    <Button size="sm" className="flex-1">
                      <Settings className="mr-2 h-3 w-3" />
                      Configure
                    </Button>
                  ) : (
                    <Button variant="outline" size="sm" className="flex-1">
                      <Settings className="mr-2 h-3 w-3" />
                      Manage
                    </Button>
                  )}
                  
                  {connector.documentation && (
                    <Button variant="outline" size="sm">
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Empty State */}
      {filteredConnectors.length === 0 && (
        <div className="text-center py-12">
          <Puzzle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No connectors found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your search criteria or browse all categories
          </p>
          <Button variant="outline" onClick={() => {
            setSearchTerm("")
            setCategoryFilter("all")
            setStatusFilter("all")
          }}>
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  )
}
