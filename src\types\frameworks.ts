export type FrameworkType = 
  | "regulatory"
  | "industry"
  | "internal"
  | "hybrid"
  | "custom"

export type FrameworkStandard = 
  | "NIST CSF 2.0"
  | "ISO 27001"
  | "SOC 2"
  | "PCI DSS"
  | "HIPAA"
  | "GDPR"
  | "CCPA"
  | "FedRAMP"
  | "COBIT"
  | "Custom"

export type ImplementationStatus = 
  | "not-started"
  | "in-progress"
  | "implemented"
  | "certified"
  | "maintenance"

export type CertificationStatus = 
  | "not-applicable"
  | "planning"
  | "in-process"
  | "certified"
  | "expired"
  | "suspended"

export type RiskLevel = "low" | "medium" | "high" | "critical"

export type GapSeverity = "critical" | "high" | "medium" | "low"

export interface Framework {
  id: string
  name: string
  description?: string
  frameworkType: FrameworkType
  standard: FrameworkStandard
  version: string
  implementationStatus: ImplementationStatus
  compliancePercentage: number
  gapCount: number
  criticalGaps: number
  highGaps: number
  mediumGaps: number
  lowGaps: number
  certificationStatus: CertificationStatus
  certificationDate?: Date
  expirationDate?: Date
  nextMilestone?: Date
  milestoneDescription?: string
  riskLevel: RiskLevel
  totalControls: number
  implementedControls: number
  entityId: string
  createdBy: string
  createdAt: Date
  updatedAt: Date
  lastAssessment?: Date
  isActive: boolean
  tags: string[]
  customFields?: Record<string, any>
}

export interface FrameworkControl {
  id: string
  frameworkId: string
  controlId: string
  controlFamily: string
  controlTitle: string
  controlDescription: string
  implementationStatus: "not-implemented" | "partially-implemented" | "implemented" | "not-applicable"
  implementationNotes?: string
  evidenceLinks: string[]
  responsibleParty?: string
  implementationDate?: Date
  testingDate?: Date
  testingStatus?: "not-tested" | "passed" | "failed" | "pending"
  riskRating: RiskLevel
  priority: number
  customParameters?: Record<string, any>
}

export interface FrameworkMapping {
  id: string
  sourceFrameworkId: string
  targetFrameworkId: string
  sourceControlId: string
  targetControlId: string
  mappingType: "equivalent" | "partial" | "related" | "none"
  mappingStrength: number // 0-100
  notes?: string
  createdBy: string
  createdAt: Date
  isVerified: boolean
}

export interface FrameworkTemplate {
  id: string
  name: string
  description: string
  standard: FrameworkStandard
  version: string
  frameworkType: FrameworkType
  controlCount: number
  isOfficial: boolean
  source: string
  oscalProfile?: string
  createdAt: Date
  updatedAt: Date
  usageCount: number
  tags: string[]
}

export interface FrameworkAssessment {
  id: string
  frameworkId: string
  assessmentType: "gap-analysis" | "compliance-check" | "certification-prep" | "continuous"
  status: "planned" | "in-progress" | "completed" | "cancelled"
  startDate: Date
  endDate?: Date
  assessor: string
  scope: string[]
  findings: AssessmentFinding[]
  overallScore: number
  recommendations: string[]
  nextAssessmentDate?: Date
  reportUrl?: string
}

export interface AssessmentFinding {
  id: string
  controlId: string
  severity: GapSeverity
  finding: string
  recommendation: string
  status: "open" | "in-progress" | "resolved" | "accepted"
  dueDate?: Date
  assignedTo?: string
  evidenceRequired: string[]
}

export interface FrameworkCertification {
  id: string
  frameworkId: string
  certificationType: string
  certifyingBody: string
  certificationDate: Date
  expirationDate: Date
  certificateNumber?: string
  scope: string
  status: CertificationStatus
  renewalDate?: Date
  auditDate?: Date
  auditor?: string
  certificateUrl?: string
  maintenanceRequirements: string[]
}

export interface ControlHarmonization {
  id: string
  commonControlId: string
  commonControlTitle: string
  description: string
  mappedFrameworks: {
    frameworkId: string
    controlId: string
    mappingStrength: number
  }[]
  implementationGuidance: string
  evidenceRequirements: string[]
  testingProcedures: string[]
  automationCapable: boolean
  riskReduction: number
}

export interface FrameworkComparison {
  frameworks: Framework[]
  commonControls: ControlHarmonization[]
  uniqueControls: {
    frameworkId: string
    controls: FrameworkControl[]
  }[]
  overlapPercentage: number
  implementationEffort: {
    frameworkId: string
    effort: "low" | "medium" | "high"
    estimatedHours: number
  }[]
}

export interface FrameworkConfiguration {
  id: string
  frameworkId: string
  implementationPlan: {
    phases: ImplementationPhase[]
    timeline: number // days
    resources: string[]
    budget?: number
  }
  controlMappings: {
    sourceControlId: string
    targetControlId?: string
    implementationApproach: string
  }[]
  customizations: {
    additionalControls: FrameworkControl[]
    excludedControls: string[]
    parameterValues: Record<string, any>
  }
  stakeholders: {
    owner: string
    implementationTeam: string[]
    approvers: string[]
  }
}

export interface ImplementationPhase {
  id: string
  name: string
  description: string
  startDate: Date
  endDate: Date
  controls: string[]
  dependencies: string[]
  deliverables: string[]
  status: "not-started" | "in-progress" | "completed" | "delayed"
}

export interface FrameworkFilter {
  frameworkType?: FrameworkType[]
  standard?: FrameworkStandard[]
  implementationStatus?: ImplementationStatus[]
  certificationStatus?: CertificationStatus[]
  riskLevel?: RiskLevel[]
  complianceRange?: {
    min: number
    max: number
  }
  lastAssessmentRange?: {
    startDate: Date
    endDate: Date
  }
  tags?: string[]
  searchQuery?: string
  entityId?: string
}

export interface FrameworkSortOptions {
  field: "name" | "standard" | "implementationStatus" | "compliancePercentage" | "gapCount" | "riskLevel" | "lastAssessment"
  direction: "asc" | "desc"
}
