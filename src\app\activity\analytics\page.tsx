"use client"

import React from "react"
import { Bar<PERSON>hart3, TrendingUp, Target, Download } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

import { 
  sampleActivityMetrics,
  sampleActivityDashboard
} from "@/lib/activity-data"

export default function ActivityAnalyticsPage() {
  const metrics = sampleActivityMetrics
  const trends = sampleActivityDashboard.monthlyTrends
  const recommendations = sampleActivityDashboard.recommendations

  const getTrendIcon = (value: number, threshold: number = 75) => {
    return value >= threshold ? 
      <TrendingUp className="h-4 w-4 text-green-500" /> : 
      <TrendingUp className="h-4 w-4 text-red-500" />
  }

  const getTrendColor = (value: number, threshold: number = 75) => {
    return value >= threshold ? "text-green-600" : "text-red-600"
  }

  const getPerformanceLevel = (score: number) => {
    if (score >= 90) return { label: "Excellent", color: "default" }
    if (score >= 80) return { label: "Good", color: "secondary" }
    if (score >= 70) return { label: "Average", color: "outline" }
    return { label: "Needs Improvement", color: "destructive" }
  }

  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
            <p className="text-muted-foreground">
              Performance analytics dashboard with productivity metrics and trend analysis
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export Report
            </Button>
            <Button variant="outline" size="sm">
              <Target className="mr-2 h-4 w-4" />
              Set Goals
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 space-y-6">
        {/* Key Performance Indicators */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Performance Overview
            </CardTitle>
            <CardDescription>
              Key productivity and quality metrics for your work performance
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Completion Rate</span>
                  <div className="flex items-center gap-1">
                    {getTrendIcon(metrics.completionRate)}
                    <span className={`text-sm ${getTrendColor(metrics.completionRate)}`}>
                      {metrics.completionRate.toFixed(1)}%
                    </span>
                  </div>
                </div>
                <Progress value={metrics.completionRate} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Quality Score</span>
                  <div className="flex items-center gap-1">
                    {getTrendIcon(metrics.qualityScore * 10, 80)}
                    <span className={`text-sm ${getTrendColor(metrics.qualityScore * 10, 80)}`}>
                      {metrics.qualityScore.toFixed(1)}/10
                    </span>
                  </div>
                </div>
                <Progress value={metrics.qualityScore * 10} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Compliance Adherence</span>
                  <div className="flex items-center gap-1">
                    {getTrendIcon(metrics.complianceAdherence, 95)}
                    <span className={`text-sm ${getTrendColor(metrics.complianceAdherence, 95)}`}>
                      {metrics.complianceAdherence.toFixed(1)}%
                    </span>
                  </div>
                </div>
                <Progress value={metrics.complianceAdherence} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Stakeholder Satisfaction</span>
                  <div className="flex items-center gap-1">
                    {getTrendIcon(metrics.stakeholderSatisfaction * 10, 85)}
                    <span className={`text-sm ${getTrendColor(metrics.stakeholderSatisfaction * 10, 85)}`}>
                      {metrics.stakeholderSatisfaction.toFixed(1)}/10
                    </span>
                  </div>
                </div>
                <Progress value={metrics.stakeholderSatisfaction * 10} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Monthly Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Monthly Trends</CardTitle>
            <CardDescription>Performance trends and improvement areas</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
              {Object.entries(trends).map(([key, value]) => {
                const performance = getPerformanceLevel(value)
                return (
                  <div key={key} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium capitalize">{key}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-bold">{value}%</span>
                      <Badge variant={performance.color as any}>
                        {performance.label}
                      </Badge>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Time Management */}
        <Card>
          <CardHeader>
            <CardTitle>Time Management Analytics</CardTitle>
            <CardDescription>Workload distribution and time allocation insights</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold">{metrics.totalHoursWorked}</div>
                <p className="text-sm text-muted-foreground">Hours Worked This Week</p>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold">{metrics.averageHoursPerTask.toFixed(1)}</div>
                <p className="text-sm text-muted-foreground">Average Hours per Task</p>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold">{metrics.averageCompletionTime.toFixed(1)}</div>
                <p className="text-sm text-muted-foreground">Average Completion Time (hours)</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recommendations */}
        {recommendations.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Performance Recommendations</CardTitle>
              <CardDescription>AI-powered suggestions to improve productivity and collaboration</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recommendations.map((recommendation, index) => (
                  <div key={index} className="flex items-start justify-between p-4 border rounded-lg">
                    <div className="space-y-1 flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{recommendation.title}</h4>
                        <Badge variant="outline">
                          {recommendation.type}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {recommendation.description}
                      </p>
                    </div>
                    {recommendation.actionUrl && (
                      <Button variant="outline" size="sm">
                        View
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
