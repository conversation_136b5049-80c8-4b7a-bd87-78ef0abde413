"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { toast as sonnerToast } from "sonner"

interface CustomToastProps {
  message: string
}

export function CustomToast({ message }: CustomToastProps) {
  return (
    <motion.div
      initial={{ y: -50, opacity: 0, scale: 0.95 }}
      animate={{ y: 0, opacity: 1, scale: 1 }}
      exit={{ y: -20, opacity: 0, scale: 0.95 }}
      transition={{
        type: "spring",
        damping: 20,
        stiffness: 300,
        duration: 0.4
      }}
      className="bg-background border border-border rounded-md shadow-lg px-3 py-2 text-sm font-medium text-foreground"
    >
      {message}
    </motion.div>
  )
}

export function showThemeToast(message: string) {
  sonnerToast(message, {
    duration: 1500,
    position: "top-center",
  })
}
