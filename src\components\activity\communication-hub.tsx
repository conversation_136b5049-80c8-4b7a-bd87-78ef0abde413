"use client"

import React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  MessageSquare, 
  Users, 
  Search, 
  Plus,
  Clock,
  Pin,
  AlertTriangle,
  CheckCircle,
  Archive
} from "lucide-react"
import { CommunicationThread } from "@/types/activity"

interface CommunicationHubProps {
  threads: CommunicationThread[]
  recentActivity: CommunicationThread[]
}

export function CommunicationHub({ threads, recentActivity }: CommunicationHubProps) {
  const [filter, setFilter] = React.useState<"all" | "active" | "collaboration" | "escalation">("active")
  const [searchQuery, setSearchQuery] = React.useState("")

  const filteredThreads = threads.filter(thread => {
    const matchesFilter = filter === "all" || 
                         (filter === "active" && thread.status === "active") ||
                         (filter === "collaboration" && thread.type === "collaboration") ||
                         (filter === "escalation" && thread.type === "escalation")
    const matchesSearch = thread.title.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesFilter && matchesSearch
  })

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "discussion": return <MessageSquare className="h-4 w-4" />
      case "announcement": return <AlertTriangle className="h-4 w-4" />
      case "question": return <MessageSquare className="h-4 w-4" />
      case "collaboration": return <Users className="h-4 w-4" />
      case "escalation": return <AlertTriangle className="h-4 w-4" />
      default: return <MessageSquare className="h-4 w-4" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent": return "destructive"
      case "high": return "default"
      case "medium": return "secondary"
      case "low": return "outline"
      default: return "outline"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active": return <MessageSquare className="h-4 w-4 text-green-500" />
      case "resolved": return <CheckCircle className="h-4 w-4 text-blue-500" />
      case "archived": return <Archive className="h-4 w-4 text-gray-500" />
      case "escalated": return <AlertTriangle className="h-4 w-4 text-red-500" />
      default: return <MessageSquare className="h-4 w-4" />
    }
  }

  const getTimeAgo = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMins < 1) return "Just now"
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    return `${diffDays}d ago`
  }

  const getUnreadCount = (thread: CommunicationThread) => {
    // Mock implementation - would calculate actual unread messages
    return Math.floor(Math.random() * 5)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Communication Hub
        </CardTitle>
        <CardDescription>
          Team collaboration, knowledge sharing, and cross-functional communication
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search and Filters */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input 
                placeholder="Search conversations..." 
                className="pl-8 w-64"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-1">
              {["active", "all", "collaboration", "escalation"].map((filterType) => (
                <Button
                  key={filterType}
                  variant={filter === filterType ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilter(filterType as any)}
                >
                  {filterType === "all" ? "All" : filterType.charAt(0).toUpperCase() + filterType.slice(1)}
                </Button>
              ))}
            </div>
          </div>
          <Button size="sm">
            <Plus className="mr-2 h-4 w-4" />
            New Thread
          </Button>
        </div>

        {/* Recent Activity Summary */}
        {recentActivity.length > 0 && (
          <div className="p-3 bg-muted rounded-lg">
            <h4 className="text-sm font-medium mb-2">Recent Activity</h4>
            <div className="space-y-1">
              {recentActivity.slice(0, 3).map((thread) => (
                <div key={thread.id} className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    {getTypeIcon(thread.type)}
                    <span className="truncate">{thread.title}</span>
                  </div>
                  <span className="text-muted-foreground">{getTimeAgo(thread.lastActivity)}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Communication Threads */}
        <div className="space-y-3">
          {filteredThreads.map((thread) => {
            const unreadCount = getUnreadCount(thread)
            
            return (
              <div key={thread.id} className="p-4 border rounded-lg hover:bg-muted/50">
                <div className="flex items-start justify-between">
                  <div className="space-y-2 flex-1">
                    <div className="flex items-center gap-2">
                      {getTypeIcon(thread.type)}
                      <h4 className="font-medium">{thread.title}</h4>
                      <Badge variant={getPriorityColor(thread.priority)}>
                        {thread.priority}
                      </Badge>
                      <Badge variant="outline">
                        {thread.type}
                      </Badge>
                      {thread.isConfidential && (
                        <Badge variant="destructive" className="text-xs">
                          Confidential
                        </Badge>
                      )}
                      {unreadCount > 0 && (
                        <Badge variant="default" className="text-xs">
                          {unreadCount} new
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        <span>{thread.participants.length} participants</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>Last activity: {getTimeAgo(thread.lastActivity)}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(thread.status)}
                        <span>{thread.status}</span>
                      </div>
                      {thread.sourceModule && (
                        <span>From: {thread.sourceModule}</span>
                      )}
                    </div>

                    {/* Related Items */}
                    {(thread.relatedTasks.length > 0 || thread.relatedApprovals.length > 0) && (
                      <div className="flex items-center gap-2 text-xs">
                        <span className="text-muted-foreground">Related:</span>
                        {thread.relatedTasks.length > 0 && (
                          <Badge variant="outline" className="text-xs">
                            {thread.relatedTasks.length} tasks
                          </Badge>
                        )}
                        {thread.relatedApprovals.length > 0 && (
                          <Badge variant="outline" className="text-xs">
                            {thread.relatedApprovals.length} approvals
                          </Badge>
                        )}
                      </div>
                    )}

                    {/* Tags */}
                    {thread.tags.length > 0 && (
                      <div className="flex gap-1">
                        {thread.tags.slice(0, 3).map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {thread.tags.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{thread.tags.length - 3}
                          </Badge>
                        )}
                      </div>
                    )}

                    {/* Participants Preview */}
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground">Participants:</span>
                      <div className="flex gap-1">
                        {thread.participants.slice(0, 3).map((participant) => (
                          <Badge key={participant} variant="outline" className="text-xs">
                            {participant.split('@')[0]}
                          </Badge>
                        ))}
                        {thread.participants.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{thread.participants.length - 3}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {thread.status === "active" && (
                      <Button variant="outline" size="sm">
                        Reply
                      </Button>
                    )}
                    <Button variant="outline" size="sm">
                      View
                    </Button>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {filteredThreads.length === 0 && (
          <div className="text-center py-8">
            <MessageSquare className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-2 text-sm font-medium">No conversations found</h3>
            <p className="mt-1 text-sm text-muted-foreground">
              {searchQuery ? "Try adjusting your search criteria" : `No ${filter} conversations at this time.`}
            </p>
          </div>
        )}

        {/* Quick Actions */}
        <div className="pt-4 border-t">
          <div className="flex items-center justify-between">
            <Button variant="outline" size="sm">
              <Archive className="mr-2 h-4 w-4" />
              Archive Resolved
            </Button>
            <Button variant="outline" size="sm">
              <Users className="mr-2 h-4 w-4" />
              Team Directory
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
