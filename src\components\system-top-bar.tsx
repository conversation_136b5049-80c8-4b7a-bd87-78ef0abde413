"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import { ChevronRight } from "lucide-react"
import { EnhancedSidebarTrigger } from "@/components/enhanced-sidebar-trigger"
import { ThemeToggle } from "@/components/theme-toggle"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbEllipsis,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { navigationData } from "@/lib/navigation-data"
import { AIChatbot } from "@/components/ai-chatbot"

export function SystemTopBar() {
  const pathname = usePathname()
  const [currentTime, setCurrentTime] = React.useState(new Date())

  // Update time every second
  React.useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Generate breadcrumb items based on current path
  const breadcrumbItems = React.useMemo(() => {
    const items = []

    // Find the main navigation item
    for (const mainItem of navigationData.navMain) {
      if (pathname === mainItem.url || pathname.startsWith(mainItem.url + "/")) {
        items.push({
          title: mainItem.title,
          url: mainItem.url,
          isLast: pathname === mainItem.url
        })

        // If we're on a sub-page, find the sub-navigation item
        if (pathname !== mainItem.url && mainItem.items) {
          for (const subItem of mainItem.items) {
            if (pathname === subItem.url || pathname.startsWith(subItem.url + "/")) {
              items.push({
                title: subItem.title,
                url: subItem.url,
                isLast: pathname === subItem.url
              })

              // Check for deeper nesting if needed
              if (pathname !== subItem.url && subItem.items) {
                for (const deepItem of subItem.items) {
                  if (pathname === deepItem.url) {
                    items.push({
                      title: deepItem.title,
                      url: deepItem.url,
                      isLast: true
                    })
                    break
                  }
                }
              }
              break
            }
          }
        }
        break
      }
    }

    // If no items found, default to GRCOS
    if (items.length === 0) {
      items.push({
        title: "GRCOS",
        url: "/",
        isLast: true
      })
    } else {
      // Mark the last item as current
      items[items.length - 1].isLast = true
    }

    return items
  }, [pathname])

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    })
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  return (
    <div className="fixed top-0 left-0 right-0 z-50 h-12 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 flex items-center px-4">
      {/* Left side - Sidebar toggle and breadcrumb */}
      <div className="flex items-center gap-3">
        <EnhancedSidebarTrigger />
        <Separator orientation="vertical" className="h-6" />
        <Breadcrumb>
          <BreadcrumbList>
            {breadcrumbItems.map((item, index) => (
              <React.Fragment key={item.url}>
                <BreadcrumbItem>
                  {item.isLast ? (
                    <BreadcrumbPage className="text-sm font-medium">
                      {item.title}
                    </BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink asChild>
                      <Link href={item.url} className="text-sm font-medium hover:text-foreground">
                        {item.title}
                      </Link>
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
                {!item.isLast && <BreadcrumbSeparator />}
              </React.Fragment>
            ))}
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {/* Center spacer */}
      <div className="flex-1" />

      {/* Right side - Date/time, theme toggle, and AI chatbot */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400">
          <span className="font-medium">
            {formatDate(currentTime)}
          </span>
          <Separator orientation="vertical" className="h-4" />
          <span className="font-mono">
            {formatTime(currentTime)}
          </span>
        </div>
        <Separator orientation="vertical" className="h-6" />
        <ThemeToggle />
        <Separator orientation="vertical" className="h-6" />
        <AIChatbot />
      </div>
    </div>
  )
}
