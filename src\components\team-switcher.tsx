"use client"

import * as React from "react"
import { ChevronsUpDown, Plus, Building2, Circle } from "lucide-react"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"
import { cn } from "@/lib/utils"
import { Entity, ENTITY_TYPE_LABELS, ENTITY_PLAN_LABELS } from "@/lib/entity-types"
import { sampleEntities, ENTITY_TYPE_ICONS } from "@/lib/entity-data"

export function EntitySwitcher() {
  const { isMobile, state } = useSidebar()
  const [activeEntity, setActiveEntity] = React.useState<Entity>(sampleEntities[0])

  if (!activeEntity) {
    return null
  }

  const getStatusColor = (status: Entity['status']) => {
    switch (status) {
      case 'active': return 'text-green-500'
      case 'inactive': return 'text-gray-500'
      case 'setup': return 'text-orange-500'
      case 'archived': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }



  const ActiveIcon = activeEntity.logo || ENTITY_TYPE_ICONS[activeEntity.type] || Building2

  return (
    <>
      <SidebarMenu>
        <SidebarMenuItem>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <SidebarMenuButton
                size="lg"
                className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              >
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <ActiveIcon className="size-4" />
                </div>
                <div className={cn(
                  "grid flex-1 text-left text-sm leading-tight transition-all duration-200",
                  state === "collapsed" && "opacity-0 w-0 overflow-hidden"
                )}>
                  <div className="flex items-center gap-2">
                    <span className="truncate font-semibold font-display">
                      {activeEntity.name}
                    </span>
                    <Circle className={cn("size-2 fill-current", getStatusColor(activeEntity.status))} />
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="truncate text-xs text-muted-foreground">
                      {ENTITY_TYPE_LABELS[activeEntity.type]}
                    </span>
                    <span className="truncate text-xs text-muted-foreground">
                      • {ENTITY_PLAN_LABELS[activeEntity.plan]}
                    </span>
                  </div>
                </div>
                <ChevronsUpDown className={cn(
                  "ml-auto size-4 transition-all duration-200",
                  state === "collapsed" && "opacity-0 w-0 overflow-hidden"
                )} />
              </SidebarMenuButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-[--radix-dropdown-menu-trigger-width] min-w-72 rounded-lg"
              align="start"
              side={isMobile ? "bottom" : "right"}
              sideOffset={4}
            >
              <DropdownMenuLabel className="text-xs text-muted-foreground">
                Entities
              </DropdownMenuLabel>
              {sampleEntities.map((entity, index) => {
                const EntityIcon = entity.logo || ENTITY_TYPE_ICONS[entity.type] || Building2
                const isActive = entity.id === activeEntity.id

                return (
                  <DropdownMenuItem
                    key={entity.id}
                    onClick={() => setActiveEntity(entity)}
                    className={cn(
                      "gap-3 p-3 cursor-pointer",
                      isActive && "bg-sidebar-accent text-sidebar-accent-foreground"
                    )}
                  >
                    <div className="flex size-8 items-center justify-center rounded-lg border bg-background">
                      <EntityIcon className="size-4 shrink-0" />
                    </div>
                    <div className="flex-1 grid text-left text-sm leading-tight">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{entity.name}</span>
                        <Circle className={cn("size-2 fill-current", getStatusColor(entity.status))} />
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-muted-foreground">
                          {ENTITY_TYPE_LABELS[entity.type]}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          • {ENTITY_PLAN_LABELS[entity.plan]}
                        </span>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {entity.stats.totalAssets} assets • {entity.stats.complianceScore}% compliant
                      </div>
                    </div>
                    {index < 9 && (
                      <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut>
                    )}
                  </DropdownMenuItem>
                )
              })}
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="gap-3 p-3 cursor-pointer"
                onClick={() => {
                  // TODO: Implement create entity functionality
                  console.log("Create entity clicked")
                }}
              >
                <div className="flex size-8 items-center justify-center rounded-lg border bg-background">
                  <Plus className="size-4" />
                </div>
                <div className="flex-1">
                  <div className="font-medium">Create Entity</div>
                  <div className="text-xs text-muted-foreground">Add new department, branch, or subsidiary</div>
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarMenuItem>
      </SidebarMenu>
    </>
  )
}
