"use client"

import React from "react"
import Link from "next/link"
import { 
  Calendar, 
  User, 
  Shield, 
  FileText, 
  AlertTriangle, 
  CheckCircle, 
  Download,
  ExternalLink,
  Clock,
  Activity,
  Settings,
  Users,
  BarChart3,
  Archive
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"

import { Policy } from "@/types/policies"
import { cn } from "@/lib/utils"

interface PolicyDetailViewProps {
  policy: Policy
  onClose: () => void
  onEdit?: (policy: Policy) => void
  onDownload?: (policy: Policy) => void
  onArchive?: (policy: Policy) => void
  className?: string
}

export function PolicyDetailView({
  policy,
  onClose,
  onEdit,
  onDownload,
  onArchive,
  className
}: PolicyDetailViewProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "published": return "text-green-600"
      case "approved": return "text-blue-600"
      case "under-review": return "text-yellow-600"
      case "draft": return "text-gray-600"
      case "expired": return "text-red-600"
      case "suspended": return "text-red-600"
      default: return "text-gray-600"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "critical": return "text-red-600"
      case "high": return "text-orange-600"
      case "medium": return "text-yellow-600"
      case "low": return "text-green-600"
      default: return "text-gray-600"
    }
  }

  return (
    <div className={cn("space-y-6", className)}>
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
          <TabsTrigger value="enforcement">Enforcement</TabsTrigger>
          <TabsTrigger value="lifecycle">Lifecycle</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Policy Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Type</span>
                    <Badge variant="outline" className="capitalize">
                      {policy.policyType.replace('-', ' ')}
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Priority</span>
                    <Badge variant="outline" className={cn("capitalize", getPriorityColor(policy.priority))}>
                      {policy.priority}
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Status</span>
                    <Badge variant="outline" className={cn("capitalize", getStatusColor(policy.status))}>
                      {policy.status.replace('-', ' ')}
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Version</span>
                    <span className="text-sm font-medium">v{policy.version}</span>
                  </div>
                </div>

                <Separator />

                <div className="grid gap-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Owner</span>
                    <span className="text-sm font-medium">{policy.owner}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Business Function</span>
                    <span className="text-sm">{policy.businessFunction}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Responsible Party</span>
                    <span className="text-sm">{policy.responsibleParty}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Compliance Rate</span>
                      <span className="font-medium">{policy.metrics.complianceRate.toFixed(1)}%</span>
                    </div>
                    <Progress value={policy.metrics.complianceRate} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Attestation Rate</span>
                      <span className="font-medium">{policy.metrics.attestationRate.toFixed(1)}%</span>
                    </div>
                    <Progress value={policy.metrics.attestationRate} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Enforcement Effectiveness</span>
                      <span className="font-medium">{policy.metrics.enforcementEffectiveness.toFixed(1)}%</span>
                    </div>
                    <Progress value={policy.metrics.enforcementEffectiveness} className="h-2" />
                  </div>
                </div>

                <Separator />

                <div className="grid gap-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Active Violations</span>
                    <Badge variant={policy.metrics.violationCount > 0 ? "destructive" : "secondary"}>
                      {policy.metrics.violationCount}
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Active Exceptions</span>
                    <Badge variant={policy.metrics.exceptionCount > 0 ? "outline" : "secondary"}>
                      {policy.metrics.exceptionCount}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Dates and Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Timeline & Dates</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Created</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {new Date(policy.createdDate).toLocaleDateString()}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    by {policy.createdBy.split('@')[0]}
                  </p>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Last Modified</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {new Date(policy.lastModifiedDate).toLocaleDateString()}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    by {policy.lastModifiedBy.split('@')[0]}
                  </p>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Activity className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Next Review</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {new Date(policy.nextReviewDate).toLocaleDateString()}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {policy.reviewFrequency}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Applicable Frameworks */}
          {policy.applicableFrameworks.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Applicable Frameworks</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {policy.applicableFrameworks.map((framework) => (
                    <Badge key={framework} variant="outline">
                      {framework}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Policy Content</CardTitle>
              <CardDescription>
                Full policy document content and attachments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Summary</h4>
                  <p className="text-sm text-muted-foreground">{policy.summary}</p>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="text-sm font-medium mb-2">Scope</h4>
                  <p className="text-sm text-muted-foreground">{policy.scope}</p>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="text-sm font-medium mb-2">Applicability</h4>
                  <div className="flex flex-wrap gap-2">
                    {policy.applicability.map((item) => (
                      <Badge key={item} variant="secondary">
                        {item}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                {policy.attachments.length > 0 && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="text-sm font-medium mb-2">Attachments</h4>
                      <div className="space-y-2">
                        {policy.attachments.map((attachment) => (
                          <div key={attachment.id} className="flex items-center justify-between p-2 border rounded">
                            <div className="flex items-center gap-2">
                              <FileText className="h-4 w-4 text-muted-foreground" />
                              <div>
                                <p className="text-sm font-medium">{attachment.name}</p>
                                <p className="text-xs text-muted-foreground">
                                  {(attachment.fileSize / 1024).toFixed(1)} KB
                                </p>
                              </div>
                            </div>
                            <Button variant="outline" size="sm">
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Compliance Status</CardTitle>
              <CardDescription>
                Framework compliance and control mappings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Overall Compliance</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Compliance Rate</span>
                        <span className="font-medium">{policy.metrics.complianceRate.toFixed(1)}%</span>
                      </div>
                      <Progress value={policy.metrics.complianceRate} />
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Assessment Status</h4>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>Last Assessment</span>
                        <span>{policy.metrics.lastAssessmentDate ? new Date(policy.metrics.lastAssessmentDate).toLocaleDateString() : 'N/A'}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Next Review</span>
                        <span>{new Date(policy.metrics.nextReviewDate).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {policy.mappedControls.length > 0 && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="text-sm font-medium mb-2">Mapped Controls</h4>
                      <div className="space-y-2">
                        {policy.mappedControls.map((control) => (
                          <div key={control.id} className="flex items-center justify-between p-2 border rounded">
                            <div>
                              <p className="text-sm font-medium">{control.controlId} - {control.controlTitle}</p>
                              <p className="text-xs text-muted-foreground">{control.frameworkName}</p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant={control.implementationStatus === "implemented" ? "default" : "outline"}>
                                {control.implementationStatus}
                              </Badge>
                              <Badge variant={control.testingStatus === "passed" ? "default" : "destructive"}>
                                {control.testingStatus}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="enforcement" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Enforcement Status</CardTitle>
              <CardDescription>
                Policy enforcement mechanisms and effectiveness
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Enforcement Status</h4>
                    <Badge variant={
                      policy.enforcementStatus === "fully-enforced" ? "default" :
                      policy.enforcementStatus === "partially-enforced" ? "outline" :
                      "destructive"
                    } className="capitalize">
                      {policy.enforcementStatus.replace('-', ' ')}
                    </Badge>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Effectiveness</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Effectiveness Rate</span>
                        <span className="font-medium">{policy.metrics.enforcementEffectiveness.toFixed(1)}%</span>
                      </div>
                      <Progress value={policy.metrics.enforcementEffectiveness} />
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="text-sm font-medium mb-2">Enforcement Mechanisms</h4>
                  <div className="flex flex-wrap gap-2">
                    {policy.enforcementMechanism.map((mechanism) => (
                      <Badge key={mechanism} variant="outline">
                        {mechanism}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                {policy.opaPolicy && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="text-sm font-medium mb-2">OPA Policy</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Package Name</span>
                          <code className="text-xs bg-muted px-2 py-1 rounded">{policy.opaPolicy.packageName}</code>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Deployment Status</span>
                          <Badge variant={policy.opaPolicy.deploymentStatus === "deployed" ? "default" : "destructive"}>
                            {policy.opaPolicy.deploymentStatus}
                          </Badge>
                        </div>
                        {policy.opaPolicy.lastDeployed && (
                          <div className="flex justify-between text-sm">
                            <span>Last Deployed</span>
                            <span>{new Date(policy.opaPolicy.lastDeployed).toLocaleDateString()}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="lifecycle" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Lifecycle Management</CardTitle>
              <CardDescription>
                Policy lifecycle, approvals, and review schedule
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Review Schedule</h4>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>Frequency</span>
                        <span className="capitalize">{policy.reviewFrequency}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Last Review</span>
                        <span>{policy.lastReviewDate ? new Date(policy.lastReviewDate).toLocaleDateString() : 'N/A'}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Next Review</span>
                        <span>{new Date(policy.nextReviewDate).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Approval Status</h4>
                    <Badge variant={policy.currentApprovalStatus === "approved" ? "default" : "outline"}>
                      {policy.currentApprovalStatus}
                    </Badge>
                  </div>
                </div>
                
                {policy.approvals.length > 0 && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="text-sm font-medium mb-2">Approvals</h4>
                      <div className="space-y-2">
                        {policy.approvals.map((approval) => (
                          <div key={approval.id} className="flex items-center justify-between p-2 border rounded">
                            <div>
                              <p className="text-sm font-medium">{approval.approverName}</p>
                              <p className="text-xs text-muted-foreground">{approval.approverRole}</p>
                            </div>
                            <div className="text-right">
                              <Badge variant={approval.status === "approved" ? "default" : "outline"}>
                                {approval.status}
                              </Badge>
                              {approval.approvalDate && (
                                <p className="text-xs text-muted-foreground mt-1">
                                  {new Date(approval.approvalDate).toLocaleDateString()}
                                </p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Version History</CardTitle>
              <CardDescription>
                Policy version history and blockchain verification
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {policy.blockchainStatus === "verified" && (
                  <div className="p-3 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-green-800 dark:text-green-200">
                        Blockchain Verified
                      </span>
                    </div>
                    <div className="text-xs text-green-700 dark:text-green-300 space-y-1">
                      <div>Hash: <code className="bg-green-100 dark:bg-green-900/30 px-1 rounded">{policy.blockchainHash}</code></div>
                      <div>Block: <code className="bg-green-100 dark:bg-green-900/30 px-1 rounded">{policy.blockchainBlock}</code></div>
                      <div>Verified: {policy.verificationTimestamp ? new Date(policy.verificationTimestamp).toLocaleString() : 'N/A'}</div>
                    </div>
                  </div>
                )}
                
                <div>
                  <h4 className="text-sm font-medium mb-2">Current Version</h4>
                  <div className="p-3 border rounded">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm font-medium">Version {policy.version}</p>
                        <p className="text-xs text-muted-foreground">
                          Modified on {new Date(policy.lastModifiedDate).toLocaleDateString()}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          by {policy.lastModifiedBy.split('@')[0]}
                        </p>
                      </div>
                      <Badge variant="default">Current</Badge>
                    </div>
                  </div>
                </div>
                
                {policy.versions.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium mb-2">Previous Versions</h4>
                    <div className="space-y-2">
                      {policy.versions.map((version) => (
                        <div key={version.id} className="p-3 border rounded">
                          <div className="flex justify-between items-start">
                            <div>
                              <p className="text-sm font-medium">Version {version.version}</p>
                              <p className="text-xs text-muted-foreground mb-1">
                                {version.changeDescription}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                Changed on {new Date(version.changeDate).toLocaleDateString()} by {version.changedBy.split('@')[0]}
                              </p>
                            </div>
                            <div className="flex items-center gap-2">
                              {version.blockchainHash && (
                                <CheckCircle className="h-4 w-4 text-green-600" />
                              )}
                              <Badge variant={version.isActive ? "default" : "secondary"}>
                                {version.isActive ? "Active" : "Archived"}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
