"use client"

import { useState } from "react"
import Link from "next/link"
import { 
  Shield, 
  Plus,
  Filter,
  Search,
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle,
  MoreHorizontal,
  TrendingUp,
  Award,
  Target,
  AlertCircle
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { StandardModuleLayout, ModuleLayoutConfigs } from "@/components/standard-module-layout"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"

import { sampleFrameworks } from "@/lib/frameworks-data"
import { Framework, ImplementationStatus, CertificationStatus, RiskLevel } from "@/types/frameworks"

const implementationStatusConfig = {
  "not-started": { 
    label: "Not Started", 
    variant: "secondary" as const, 
    icon: Clock, 
    color: "text-gray-600 dark:text-gray-400" 
  },
  "in-progress": { 
    label: "In Progress", 
    variant: "default" as const, 
    icon: Clock, 
    color: "text-blue-600 dark:text-blue-400" 
  },
  "implemented": { 
    label: "Implemented", 
    variant: "default" as const, 
    icon: CheckCircle, 
    color: "text-green-600 dark:text-green-400" 
  },
  "certified": { 
    label: "Certified", 
    variant: "default" as const, 
    icon: Award, 
    color: "text-green-600 dark:text-green-400" 
  },
  "maintenance": { 
    label: "Maintenance", 
    variant: "outline" as const, 
    icon: Target, 
    color: "text-orange-600 dark:text-orange-400" 
  }
}

const certificationStatusConfig = {
  "not-applicable": { label: "N/A", variant: "secondary" as const },
  "planning": { label: "Planning", variant: "outline" as const },
  "in-process": { label: "In Process", variant: "default" as const },
  "certified": { label: "Certified", variant: "default" as const },
  "expired": { label: "Expired", variant: "destructive" as const },
  "suspended": { label: "Suspended", variant: "destructive" as const }
}

const riskLevelConfig = {
  "low": { label: "Low", variant: "default" as const, color: "bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300" },
  "medium": { label: "Medium", variant: "secondary" as const, color: "bg-yellow-50 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-300" },
  "high": { label: "High", variant: "destructive" as const, color: "bg-orange-50 text-orange-700 dark:bg-orange-900/20 dark:text-orange-300" },
  "critical": { label: "Critical", variant: "destructive" as const, color: "bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-300" }
}

const frameworkColors = {
  "NIST CSF 2.0": "bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300",
  "ISO 27001": "bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300",
  "SOC 2": "bg-purple-50 text-purple-700 dark:bg-purple-900/20 dark:text-purple-300",
  "PCI DSS": "bg-orange-50 text-orange-700 dark:bg-orange-900/20 dark:text-orange-300",
  "HIPAA": "bg-pink-50 text-pink-700 dark:bg-pink-900/20 dark:text-pink-300",
  "Custom": "bg-gray-50 text-gray-700 dark:bg-gray-800 dark:text-gray-300"
}

function formatRelativeTime(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) return "Just now"
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`
  
  return date.toLocaleDateString()
}

export default function FrameworksPage() {
  const [frameworks] = useState<Framework[]>(sampleFrameworks)

  // For now, we'll display all frameworks. The filtering will be handled by the layout-level StickySearch
  // TODO: Implement context or event system to communicate with layout-level search
  const filteredFrameworks = frameworks

  // Calculate dashboard stats
  const totalFrameworks = frameworks.length
  const implementedFrameworks = frameworks.filter(f => f.implementationStatus === "implemented" || f.implementationStatus === "certified").length
  const certifiedFrameworks = frameworks.filter(f => f.certificationStatus === "certified").length
  const averageCompliance = Math.round(frameworks.reduce((sum, f) => sum + f.compliancePercentage, 0) / frameworks.length)
  const totalGaps = frameworks.reduce((sum, f) => sum + f.gapCount, 0)
  const criticalGaps = frameworks.reduce((sum, f) => sum + f.criticalGaps, 0)

  return (
    <StandardModuleLayout {...ModuleLayoutConfigs.simple}>

      {/* Dashboard Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Frameworks</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalFrameworks}</div>
            <p className="text-xs text-muted-foreground">
              {implementedFrameworks} implemented
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Compliance</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageCompliance}%</div>
            <p className="text-xs text-muted-foreground">
              Across all frameworks
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Certified</CardTitle>
            <Award className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{certifiedFrameworks}</div>
            <p className="text-xs text-muted-foreground">
              Active certifications
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical Gaps</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{criticalGaps}</div>
            <p className="text-xs text-muted-foreground">
              {totalGaps} total gaps
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Framework Portfolio Overview */}
      <div className="grid gap-6 lg:grid-cols-3 mb-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Framework Portfolio</CardTitle>
            <CardDescription>
              Implementation status across all frameworks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {frameworks.slice(0, 4).map((framework) => (
                <div key={framework.id} className="flex items-center space-x-4">
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium">{framework.name}</p>
                      <span className="text-sm text-muted-foreground">
                        {framework.compliancePercentage}%
                      </span>
                    </div>
                    <Progress value={framework.compliancePercentage} className="h-2" />
                  </div>
                  <Badge 
                    variant={implementationStatusConfig[framework.implementationStatus].variant}
                    className="ml-2"
                  >
                    {implementationStatusConfig[framework.implementationStatus].label}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Gap Summary</CardTitle>
            <CardDescription>
              Open gaps by severity level
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-sm">Critical</span>
                </div>
                <span className="text-sm font-medium">{criticalGaps}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <span className="text-sm">High</span>
                </div>
                <span className="text-sm font-medium">
                  {frameworks.reduce((sum, f) => sum + f.highGaps, 0)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm">Medium</span>
                </div>
                <span className="text-sm font-medium">
                  {frameworks.reduce((sum, f) => sum + f.mediumGaps, 0)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">Low</span>
                </div>
                <span className="text-sm font-medium">
                  {frameworks.reduce((sum, f) => sum + f.lowGaps, 0)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>



      {/* Frameworks Table */}
      <div className="border-0 rounded-none -mx-4">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Framework Name</TableHead>
              <TableHead>Version</TableHead>
              <TableHead>Implementation Status</TableHead>
              <TableHead>Compliance %</TableHead>
              <TableHead>Gap Count</TableHead>
              <TableHead>Certification</TableHead>
              <TableHead>Next Milestone</TableHead>
              <TableHead>Risk Level</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredFrameworks.map((framework) => {
              const statusInfo = implementationStatusConfig[framework.implementationStatus]
              const certInfo = certificationStatusConfig[framework.certificationStatus]
              const riskInfo = riskLevelConfig[framework.riskLevel]
              const StatusIcon = statusInfo.icon
              
              return (
                <TableRow key={framework.id}>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium">{framework.name}</div>
                      <Badge 
                        variant="secondary" 
                        className={frameworkColors[framework.standard] || frameworkColors["Custom"]}
                      >
                        {framework.standard}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">{framework.version}</div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <StatusIcon className={`h-4 w-4 ${statusInfo.color}`} />
                      <Badge variant={statusInfo.variant}>
                        {statusInfo.label}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{framework.compliancePercentage}%</span>
                      </div>
                      <Progress value={framework.compliancePercentage} className="h-1" />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">{framework.gapCount}</div>
                      {framework.criticalGaps > 0 && (
                        <div className="flex items-center gap-1 text-xs text-red-600">
                          <AlertCircle className="h-3 w-3" />
                          {framework.criticalGaps} critical
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={certInfo.variant}>
                      {certInfo.label}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {framework.nextMilestone && (
                      <div className="space-y-1">
                        <div className="text-sm">
                          {formatRelativeTime(framework.nextMilestone)}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {framework.milestoneDescription}
                        </div>
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge 
                      variant={riskInfo.variant}
                      className={riskInfo.color}
                    >
                      {riskInfo.label}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          Edit Configuration
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          Export OSCAL
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          Duplicate
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          Archive
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </div>
    </StandardModuleLayout>
  )
}
