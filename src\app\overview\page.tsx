"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function OverviewPage() {
  const router = useRouter()

  useEffect(() => {
    // Auto-redirect to the first sub-item (Executive Summary) following GRCOS navigation pattern
    router.replace("/overview/executive")
  }, [router])

  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <div className="text-lg text-muted-foreground">Redirecting to Executive Summary...</div>
      </div>
    </div>
  )
}
