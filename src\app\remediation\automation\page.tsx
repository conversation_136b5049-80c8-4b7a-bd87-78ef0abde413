"use client"

import React from "react"
import Link from "next/link"
import { Plus, Play, Pause, Settings, Zap, Activity, CheckCircle, AlertTriangle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"

import { AutomationDashboard } from "@/components/remediation/automation-dashboard"
import { PlaybookTable } from "@/components/remediation/playbook-table"
import { PlaybookExecutionTable } from "@/components/remediation/playbook-execution-table"
import { IntegrationHealthMonitor } from "@/components/remediation/integration-health-monitor"
import { samplePlaybooks, samplePlaybookExecutions, remediationMetrics } from "@/lib/remediation-data"

export default function RemediationAutomationPage() {
  const [activeTab, setActiveTab] = React.useState("overview")

  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Remediation Automation</h1>
            <p className="text-muted-foreground">
              Playbook execution, containment actions, recovery operations, and integration health monitoring
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" asChild>
              <Link href="/remediation/incidents">
                <Activity className="mr-2 h-4 w-4" />
                Incidents
              </Link>
            </Button>
            <Button variant="outline">
              <Settings className="mr-2 h-4 w-4" />
              Configure
            </Button>
            <Button asChild>
              <Link href="/remediation/automation/create-playbook">
                <Plus className="mr-2 h-4 w-4" />
                New Playbook
              </Link>
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-4 mt-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Playbooks</CardTitle>
              <Play className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{remediationMetrics.playbooks.total}</div>
              <p className="text-xs text-muted-foreground">
                {remediationMetrics.playbooks.automated} automated
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Executions Today</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">18</div>
              <p className="text-xs text-muted-foreground">
                +25% from yesterday
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{remediationMetrics.playbooks.successRate}%</div>
              <p className="text-xs text-muted-foreground">
                +1.2% from last week
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Execution Time</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2.1h</div>
              <p className="text-xs text-muted-foreground">
                -15m from last month
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="flex-1 space-y-4">
        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="playbooks">Playbooks</TabsTrigger>
            <TabsTrigger value="executions">Executions</TabsTrigger>
            <TabsTrigger value="integrations">Integrations</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <AutomationDashboard />
          </TabsContent>

          <TabsContent value="playbooks" className="space-y-4">
            <PlaybookTable playbooks={samplePlaybooks} />
          </TabsContent>

          <TabsContent value="executions" className="space-y-4">
            <PlaybookExecutionTable executions={samplePlaybookExecutions} />
          </TabsContent>

          <TabsContent value="integrations" className="space-y-4">
            <IntegrationHealthMonitor />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
