"use client"

import React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>, Filter } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { NotificationCenter } from "@/components/activity/notification-center"

import { 
  sampleNotifications,
  sampleActivityDashboard
} from "@/lib/activity-data"

export default function ActivityNotificationsPage() {
  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Notifications</h1>
            <p className="text-muted-foreground">
              Role-based notification center with intelligent filtering and escalation management
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Filter Options
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="mr-2 h-4 w-4" />
              Preferences
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1">
        <NotificationCenter 
          notifications={sampleNotifications}
          unreadCount={sampleActivityDashboard.unreadNotifications}
        />
      </div>
    </div>
  )
}
