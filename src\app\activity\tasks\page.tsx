"use client"

import React from "react"
import { CheckSquare, Plus, Filter } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { PersonalTaskDashboard } from "@/components/activity/personal-task-dashboard"

import { 
  sampleTasks, 
  sampleActivityMetrics,
  sampleActivityDashboard
} from "@/lib/activity-data"

export default function ActivityTasksPage() {
  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <div className="sticky top-0 z-30 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Tasks</h1>
            <p className="text-muted-foreground">
              Personal task management dashboard with workload analytics and priority tracking
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Advanced Filters
            </Button>
            <Button size="sm">
              <Plus className="mr-2 h-4 w-4" />
              New Task
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1">
        <PersonalTaskDashboard 
          tasks={sampleTasks}
          metrics={sampleActivityMetrics}
          upcomingDeadlines={sampleActivityDashboard.upcomingDeadlines}
        />
      </div>
    </div>
  )
}
