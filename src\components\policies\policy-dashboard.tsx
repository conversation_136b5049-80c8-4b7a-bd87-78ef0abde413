"use client"

import React from "react"
import Link from "next/link"
import { 
  FileText, 
  Shield, 
  AlertTriangle, 
  Clock, 
  TrendingUp, 
  Users, 
  CheckCircle,
  Calendar,
  BarChart3,
  Activity
} from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"

import { samplePolicies, policyStats } from "@/lib/policies-data"
import { Policy } from "@/types/policies"

export function PolicyDashboard() {
  // Calculate upcoming reviews
  const upcomingReviews = samplePolicies.filter(policy => {
    const reviewDate = new Date(policy.nextReviewDate)
    const now = new Date()
    const thirtyDaysFromNow = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000))
    return reviewDate <= thirtyDaysFromNow && reviewDate >= now
  })

  // Calculate recent activity
  const recentActivity = samplePolicies
    .sort((a, b) => new Date(b.lastModifiedDate).getTime() - new Date(a.lastModifiedDate).getTime())
    .slice(0, 5)

  // Policy type distribution
  const policyTypeData = Object.entries(policyStats.policiesByType)
    .filter(([_, count]) => count > 0)
    .sort(([_, a], [__, b]) => b - a)

  // Status distribution
  const statusData = Object.entries(policyStats.policiesByStatus)
    .filter(([_, count]) => count > 0)

  return (
    <div className="space-y-6">
      {/* Compliance Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Compliance Overview</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Overall Compliance</span>
                <span className="font-medium">{policyStats.overallComplianceRate.toFixed(1)}%</span>
              </div>
              <Progress value={policyStats.overallComplianceRate} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Attestation Rate</span>
                <span className="font-medium">{policyStats.averageAttestationRate.toFixed(1)}%</span>
              </div>
              <Progress value={policyStats.averageAttestationRate} className="h-2" />
            </div>

            <div className="pt-2 border-t">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Active Violations</span>
                <Badge variant={policyStats.totalViolations > 0 ? "destructive" : "secondary"}>
                  {policyStats.totalViolations}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Enforcement Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span className="text-sm">Fully Enforced</span>
              </div>
              <span className="text-sm font-medium">
                {samplePolicies.filter(p => p.enforcementStatus === "fully-enforced").length}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <span className="text-sm">Partially Enforced</span>
              </div>
              <span className="text-sm font-medium">
                {samplePolicies.filter(p => p.enforcementStatus === "partially-enforced").length}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <span className="text-sm">Not Enforced</span>
              </div>
              <span className="text-sm font-medium">
                {samplePolicies.filter(p => p.enforcementStatus === "not-enforced").length}
              </span>
            </div>

            <div className="pt-2 border-t">
              <Button variant="outline" size="sm" className="w-full" asChild>
                <Link href="/policies/enforcement">
                  View Enforcement Details
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Lifecycle Management</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Reviews Due</span>
              </div>
              <Badge variant={upcomingReviews.length > 0 ? "destructive" : "secondary"}>
                {upcomingReviews.length}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Pending Approval</span>
              </div>
              <Badge variant="outline">
                {samplePolicies.filter(p => p.currentApprovalStatus === "pending").length}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Exceptions</span>
              </div>
              <Badge variant="outline">
                {policyStats.totalExceptions}
              </Badge>
            </div>

            <div className="pt-2 border-t">
              <Button variant="outline" size="sm" className="w-full" asChild>
                <Link href="/policies?tab=violations">
                  Manage Lifecycle
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Policy Distribution and Recent Activity */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Policy Distribution by Type</CardTitle>
            <CardDescription>
              Breakdown of policies across different categories
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {policyTypeData.map(([type, count]) => (
                <div key={type} className="flex items-center justify-between">
                  <span className="text-sm capitalize">{type.replace('-', ' ')}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-20 bg-muted rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full" 
                        style={{ width: `${(count / policyStats.totalPolicies) * 100}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium w-8 text-right">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Recent Activity</CardTitle>
            <CardDescription>
              Latest policy updates and modifications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentActivity.map((policy) => (
                <div key={policy.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                    <div>
                      <Link 
                        href={`/policies/${policy.id}`}
                        className="text-sm font-medium hover:text-primary cursor-pointer"
                      >
                        {policy.title}
                      </Link>
                      <p className="text-xs text-muted-foreground">
                        {policy.status === "published" ? "Updated" : "Modified"}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant="outline" className="text-xs">
                      v{policy.version}
                    </Badge>
                    <p className="text-xs text-muted-foreground mt-1">
                      {new Date(policy.lastModifiedDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="pt-3 border-t">
              <Button variant="outline" size="sm" className="w-full" asChild>
                <Link href="/policies?tab=policies">
                  View All Policies
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Policy Status Overview</CardTitle>
          <CardDescription>
            Current status distribution across all policies
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            {statusData.map(([status, count]) => (
              <div key={status} className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold">{count}</div>
                <div className="text-sm text-muted-foreground capitalize">
                  {status.replace('-', ' ')}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
