// GRCOS Activity Module - Sample Data
// Comprehensive sample data demonstrating all activity management use cases

import {
  Task,
  Notification,
  Approval,
  ActivityMetrics,
  CommunicationThread,
  KnowledgeEntry,
  ActivityDashboard,
  TaskComment,
  ApprovalStep,
  CommunicationMessage,
  NotificationPreferences
} from "@/types/activity"

// Sample Tasks - Cross-module assignments
export const sampleTasks: Task[] = [
  {
    id: "task-001",
    title: "Review SOC 2 Control Implementation",
    description: "Conduct quarterly review of SOC 2 Type II controls for availability and security criteria",
    category: "compliance",
    priority: "high",
    status: "in-progress",
    assignedTo: "<EMAIL>",
    assignedBy: "<EMAIL>",
    createdAt: new Date("2024-01-15T09:00:00Z"),
    updatedAt: new Date("2024-01-16T14:30:00Z"),
    dueDate: new Date("2024-01-25T17:00:00Z"),
    estimatedHours: 16,
    actualHours: 8,
    progress: 50,
    tags: ["soc2", "quarterly-review", "controls"],
    sourceModule: "controls",
    sourceEntityId: "ctrl-soc2-av-001",
    sourceEntityType: "control",
    dependencies: ["task-002"],
    blockedBy: [],
    relatedTasks: ["task-003", "task-004"],
    watchers: ["<EMAIL>", "<EMAIL>"],
    comments: [
      {
        id: "comment-001",
        taskId: "task-001",
        author: "<EMAIL>",
        content: "Started review of availability controls. Found minor documentation gaps in backup procedures.",
        createdAt: new Date("2024-01-16T10:15:00Z"),
        isInternal: true,
        mentions: ["<EMAIL>"]
      }
    ],
    attachments: [
      {
        id: "att-001",
        taskId: "task-001",
        fileName: "SOC2_Control_Review_Checklist.xlsx",
        fileSize: 245760,
        fileType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        uploadedBy: "<EMAIL>",
        uploadedAt: new Date("2024-01-16T09:30:00Z"),
        downloadUrl: "/attachments/att-001"
      }
    ],
    complianceFramework: "SOC 2",
    regulatoryDeadline: new Date("2024-01-31T23:59:59Z"),
    auditTrail: [
      {
        id: "audit-001",
        taskId: "task-001",
        action: "created",
        actor: "<EMAIL>",
        timestamp: new Date("2024-01-15T09:00:00Z"),
        details: { priority: "high", dueDate: "2024-01-25T17:00:00Z" }
      },
      {
        id: "audit-002",
        taskId: "task-001",
        action: "assigned",
        actor: "<EMAIL>",
        timestamp: new Date("2024-01-15T09:05:00Z"),
        details: { assignedTo: "<EMAIL>" }
      }
    ]
  },
  {
    id: "task-002",
    title: "Incident Response Playbook Update",
    description: "Update incident response playbook based on lessons learned from recent security incident",
    category: "remediation",
    priority: "critical",
    status: "pending",
    assignedTo: "<EMAIL>",
    assignedBy: "<EMAIL>",
    createdAt: new Date("2024-01-16T11:00:00Z"),
    updatedAt: new Date("2024-01-16T11:00:00Z"),
    dueDate: new Date("2024-01-20T12:00:00Z"),
    estimatedHours: 12,
    progress: 0,
    tags: ["incident-response", "playbook", "security"],
    sourceModule: "remediation",
    sourceEntityId: "playbook-ir-001",
    sourceEntityType: "playbook",
    dependencies: [],
    blockedBy: [],
    relatedTasks: ["task-001"],
    watchers: ["<EMAIL>"],
    comments: [],
    attachments: [],
    auditTrail: [
      {
        id: "audit-003",
        taskId: "task-002",
        action: "created",
        actor: "<EMAIL>",
        timestamp: new Date("2024-01-16T11:00:00Z"),
        details: { priority: "critical", category: "remediation" }
      }
    ]
  },
  {
    id: "task-003",
    title: "Risk Assessment Documentation Review",
    description: "Review and approve quarterly risk assessment documentation for board presentation",
    category: "assessment",
    priority: "medium",
    status: "pending",
    assignedTo: "<EMAIL>",
    assignedBy: "<EMAIL>",
    createdAt: new Date("2024-01-14T16:00:00Z"),
    updatedAt: new Date("2024-01-14T16:00:00Z"),
    dueDate: new Date("2024-01-22T09:00:00Z"),
    estimatedHours: 8,
    progress: 0,
    tags: ["risk-assessment", "quarterly", "board-presentation"],
    sourceModule: "assessments",
    sourceEntityId: "assess-risk-q1-2024",
    sourceEntityType: "assessment",
    dependencies: [],
    blockedBy: [],
    relatedTasks: ["task-001"],
    watchers: ["<EMAIL>"],
    comments: [],
    attachments: [],
    auditTrail: [
      {
        id: "audit-004",
        taskId: "task-003",
        action: "created",
        actor: "<EMAIL>",
        timestamp: new Date("2024-01-14T16:00:00Z"),
        details: { priority: "medium", category: "assessment" }
      }
    ]
  }
]

// Sample Notifications - Role-based and intelligent filtering
export const sampleNotifications: Notification[] = [
  {
    id: "notif-001",
    type: "deadline-reminder",
    priority: "high",
    title: "SOC 2 Control Review Due Soon",
    message: "Your SOC 2 control review task is due in 2 days. Please ensure all documentation is complete.",
    recipient: "<EMAIL>",
    sender: "<EMAIL>",
    createdAt: new Date("2024-01-16T08:00:00Z"),
    actionRequired: true,
    actionUrl: "/activity/tasks/task-001",
    actionLabel: "View Task",
    category: "compliance",
    tags: ["soc2", "deadline", "controls"],
    sourceModule: "controls",
    sourceEntityId: "task-001",
    escalationLevel: 1,
    reminderCount: 1,
    nextReminderAt: new Date("2024-01-17T08:00:00Z"),
    escalationChain: ["<EMAIL>", "<EMAIL>"],
    regulatoryFramework: "SOC 2",
    complianceDeadline: new Date("2024-01-25T17:00:00Z"),
    riskLevel: "medium"
  },
  {
    id: "notif-002",
    type: "approval-request",
    priority: "urgent",
    title: "Policy Approval Required",
    message: "New Information Security Policy requires your approval before implementation.",
    recipient: "<EMAIL>",
    sender: "<EMAIL>",
    createdAt: new Date("2024-01-16T10:30:00Z"),
    actionRequired: true,
    actionUrl: "/activity/approvals/approval-001",
    actionLabel: "Review & Approve",
    category: "policy",
    tags: ["policy", "approval", "security"],
    sourceModule: "policies",
    sourceEntityId: "approval-001",
    escalationLevel: 0,
    reminderCount: 0,
    escalationChain: ["<EMAIL>"],
    riskLevel: "high"
  },
  {
    id: "notif-003",
    type: "regulatory-deadline",
    priority: "urgent",
    title: "GDPR Data Breach Notification Deadline",
    message: "GDPR requires notification within 72 hours. Deadline: January 19, 2024 at 2:00 PM.",
    recipient: "<EMAIL>",
    sender: "<EMAIL>",
    createdAt: new Date("2024-01-16T14:00:00Z"),
    actionRequired: true,
    actionUrl: "/remediation/incidents/inc-2024-001",
    actionLabel: "Complete Notification",
    category: "regulatory",
    tags: ["gdpr", "data-breach", "notification"],
    sourceModule: "remediation",
    sourceEntityId: "inc-2024-001",
    escalationLevel: 2,
    reminderCount: 3,
    escalationChain: ["<EMAIL>", "<EMAIL>"],
    regulatoryFramework: "GDPR",
    complianceDeadline: new Date("2024-01-19T14:00:00Z"),
    riskLevel: "critical"
  }
]

// Sample Approvals - Workflow management
export const sampleApprovals: Approval[] = [
  {
    id: "approval-001",
    type: "policy",
    title: "Information Security Policy v2.1",
    description: "Updated information security policy incorporating new remote work guidelines and cloud security requirements",
    status: "pending",
    priority: "high",
    requestedBy: "<EMAIL>",
    requestedAt: new Date("2024-01-16T09:00:00Z"),
    requiredBy: new Date("2024-01-20T17:00:00Z"),
    approvers: [
      {
        id: "step-001",
        approvalId: "approval-001",
        stepNumber: 1,
        approver: "<EMAIL>",
        status: "approved",
        requiredBy: new Date("2024-01-18T12:00:00Z"),
        decidedAt: new Date("2024-01-16T15:30:00Z"),
        decision: "approved",
        reason: "Technical review complete. All security requirements addressed.",
        escalated: false
      },
      {
        id: "step-002",
        approvalId: "approval-001",
        stepNumber: 2,
        approver: "<EMAIL>",
        status: "pending",
        requiredBy: new Date("2024-01-19T17:00:00Z"),
        escalated: false
      },
      {
        id: "step-003",
        approvalId: "approval-001",
        stepNumber: 3,
        approver: "<EMAIL>",
        status: "pending",
        requiredBy: new Date("2024-01-20T17:00:00Z"),
        escalated: false
      }
    ],
    currentStep: 2,
    sourceModule: "policies",
    sourceEntityId: "policy-infosec-v2.1",
    sourceEntityType: "policy",
    attachments: [
      {
        id: "app-att-001",
        approvalId: "approval-001",
        fileName: "Information_Security_Policy_v2.1.pdf",
        fileSize: 1024000,
        fileType: "application/pdf",
        uploadedBy: "<EMAIL>",
        uploadedAt: new Date("2024-01-16T09:00:00Z"),
        downloadUrl: "/approvals/attachments/app-att-001",
        isConfidential: true
      }
    ],
    complianceFramework: "ISO 27001",
    riskAssessment: "Medium risk if not approved by deadline",
    auditTrail: [
      {
        id: "app-audit-001",
        approvalId: "approval-001",
        action: "created",
        actor: "<EMAIL>",
        timestamp: new Date("2024-01-16T09:00:00Z"),
        details: { type: "policy", priority: "high" },
        ipAddress: "*************",
        userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
      }
    ]
  }
]

// Sample Activity Metrics
export const sampleActivityMetrics: ActivityMetrics = {
  userId: "<EMAIL>",
  period: "weekly",
  startDate: new Date("2024-01-08T00:00:00Z"),
  endDate: new Date("2024-01-14T23:59:59Z"),
  tasksAssigned: 12,
  tasksCompleted: 8,
  tasksOverdue: 1,
  averageCompletionTime: 18.5,
  completionRate: 66.7,
  totalHoursWorked: 38,
  averageHoursPerTask: 4.75,
  peakWorkloadDay: "Wednesday",
  workloadDistribution: {
    compliance: 45,
    security: 25,
    assessment: 20,
    remediation: 10,
    review: 0,
    approval: 0,
    investigation: 0
  },
  approvalsReceived: 3,
  approvalsCompleted: 2,
  averageApprovalTime: 4.2,
  approvalRate: 66.7,
  commentsPosted: 15,
  collaborationRequests: 4,
  knowledgeSharing: 2,
  mentoringSessions: 1,
  reworkRate: 8.3,
  qualityScore: 8.5,
  stakeholderSatisfaction: 9.2,
  complianceAdherence: 95.8
}

// Sample Communication Threads
export const sampleCommunicationThreads: CommunicationThread[] = [
  {
    id: "thread-001",
    title: "SOC 2 Control Testing Coordination",
    type: "collaboration",
    priority: "medium",
    initiator: "<EMAIL>",
    participants: ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
    watchers: ["<EMAIL>"],
    sourceModule: "controls",
    sourceEntityId: "ctrl-soc2-av-001",
    relatedTasks: ["task-001"],
    relatedApprovals: [],
    status: "active",
    createdAt: new Date("2024-01-16T11:00:00Z"),
    lastActivity: new Date("2024-01-16T15:30:00Z"),
    messages: [
      {
        id: "msg-001",
        threadId: "thread-001",
        author: "<EMAIL>",
        content: "Hi team, I need help coordinating the SOC 2 control testing. Can we schedule a meeting to discuss the testing approach?",
        messageType: "text",
        createdAt: new Date("2024-01-16T11:00:00Z"),
        attachments: [],
        mentions: ["<EMAIL>", "<EMAIL>"],
        reactions: [
          { emoji: "👍", users: ["<EMAIL>"], count: 1 }
        ],
        isRead: {
          "<EMAIL>": new Date("2024-01-16T11:00:00Z"),
          "<EMAIL>": new Date("2024-01-16T11:15:00Z")
        },
        isImportant: false,
        isPinned: false
      }
    ],
    tags: ["soc2", "testing", "coordination"],
    isConfidential: false,
    requiresApproval: false
  }
]

// Sample Knowledge Entries
export const sampleKnowledgeEntries: KnowledgeEntry[] = [
  {
    id: "kb-001",
    title: "SOC 2 Control Testing Best Practices",
    content: "# SOC 2 Control Testing Best Practices\n\n## Overview\nThis guide provides best practices for testing SOC 2 controls...",
    type: "best-practice",
    category: "compliance",
    tags: ["soc2", "testing", "controls", "audit"],
    author: "<EMAIL>",
    createdAt: new Date("2024-01-10T10:00:00Z"),
    lastUpdated: new Date("2024-01-15T14:30:00Z"),
    lastReviewed: new Date("2024-01-15T14:30:00Z"),
    nextReview: new Date("2024-04-15T00:00:00Z"),
    reviewers: ["<EMAIL>", "<EMAIL>"],
    viewCount: 45,
    usefulness: 8.7,
    relatedEntries: ["kb-002", "kb-003"],
    relatedTasks: ["task-001"],
    accessLevel: "internal",
    authorizedUsers: [],
    authorizedRoles: ["compliance-team", "audit-team"],
    complianceRelevant: true,
    auditEvidence: true,
    retentionPeriod: 7,
    lastAudit: new Date("2024-01-01T00:00:00Z")
  }
]

// Sample Notification Preferences
export const sampleNotificationPreferences: NotificationPreferences = {
  userId: "<EMAIL>",
  emailNotifications: true,
  pushNotifications: true,
  smsNotifications: false,
  typePreferences: {
    "task-assignment": {
      enabled: true,
      immediateAlert: true,
      digestFrequency: "real-time",
      escalationEnabled: true
    },
    "deadline-reminder": {
      enabled: true,
      immediateAlert: true,
      digestFrequency: "real-time",
      escalationEnabled: true
    },
    "approval-request": {
      enabled: true,
      immediateAlert: true,
      digestFrequency: "real-time",
      escalationEnabled: true
    },
    "escalation": {
      enabled: true,
      immediateAlert: true,
      digestFrequency: "real-time",
      escalationEnabled: false
    },
    "system-alert": {
      enabled: true,
      immediateAlert: false,
      digestFrequency: "hourly",
      escalationEnabled: false
    },
    "regulatory-deadline": {
      enabled: true,
      immediateAlert: true,
      digestFrequency: "real-time",
      escalationEnabled: true
    },
    "collaboration-request": {
      enabled: true,
      immediateAlert: false,
      digestFrequency: "daily",
      escalationEnabled: false
    }
  },
  priorityFilter: ["medium", "high", "urgent"],
  categoryFilter: ["compliance", "security", "assessment"],
  moduleFilter: ["controls", "policies", "remediation", "assessments"],
  quietHours: {
    enabled: true,
    startTime: "18:00",
    endTime: "08:00",
    timezone: "America/New_York",
    weekendsIncluded: true
  }
}

// Sample Activity Dashboard
export const sampleActivityDashboard: ActivityDashboard = {
  userId: "<EMAIL>",
  generatedAt: new Date("2024-01-16T16:00:00Z"),
  pendingTasks: 5,
  overdueItems: 1,
  upcomingDeadlinesCount: 3,
  unreadNotifications: 8,
  pendingApprovals: 2,
  recentTasks: sampleTasks.slice(0, 3),
  recentNotifications: sampleNotifications.slice(0, 3),
  recentApprovals: sampleApprovals.slice(0, 1),
  recentCommunications: sampleCommunicationThreads.slice(0, 2),
  weeklyMetrics: sampleActivityMetrics,
  monthlyTrends: {
    productivity: 85,
    quality: 92,
    collaboration: 78,
    satisfaction: 88
  },
  upcomingDeadlines: [
    {
      task: sampleTasks[0],
      daysUntilDue: 2
    },
    {
      task: sampleTasks[1],
      daysUntilDue: 4
    }
  ],
  recommendations: [
    {
      type: "workload",
      title: "Consider Task Delegation",
      description: "You have 5 pending tasks with upcoming deadlines. Consider delegating lower priority items.",
      actionUrl: "/activity/tasks?filter=pending"
    },
    {
      type: "collaboration",
      title: "Schedule Team Sync",
      description: "Multiple team members are working on related SOC 2 tasks. A coordination meeting could improve efficiency.",
      actionUrl: "/activity/communications/thread-001"
    }
  ]
}
