"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function AssessmentsPage() {
  const router = useRouter()

  useEffect(() => {
    // Auto-redirect to the first sub-item (Risk Assessments) following GRCOS navigation pattern
    router.replace("/assessments/risk")
  }, [router])

  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <div className="text-lg text-muted-foreground">Redirecting to Risk Assessments...</div>
      </div>
    </div>
  )
}
