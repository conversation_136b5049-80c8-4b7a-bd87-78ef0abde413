"use client"

import React from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, Edit, Download, Share, Archive, AlertTriangle, CheckCircle, Shield, Target } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"

import { ControlDetailView } from "@/components/controls/control-detail-view"
import { sampleControls } from "@/lib/controls-data"
import { Control } from "@/types/controls"
import { StandardModuleLayout, ModuleLayoutConfigs } from "@/components/standard-module-layout"

interface ControlDetailPageProps {
  params: Promise<{
    id: string
  }>
}

export default function ControlDetailPage({ params }: ControlDetailPageProps) {
  const router = useRouter()
  const { id } = React.use(params)

  // Find the control by ID
  const control = sampleControls.find(c => c.id === id)

  if (!control) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Control Not Found</h1>
            <p className="text-muted-foreground">
              The requested control could not be found.
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <StandardModuleLayout
      {...ModuleLayoutConfigs.detail}
      hasCustomHeader
      customHeader={
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex-1">
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold tracking-tight">{control.controlId} - {control.title}</h1>
              <Badge variant="outline" className="capitalize">
                {control.controlFamily.replace('-', ' ')}
              </Badge>
            </div>
            <p className="text-muted-foreground">
              {control.description}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Share className="mr-2 h-4 w-4" />
              Share
            </Button>
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export Evidence
            </Button>
            <Button variant="outline" size="sm">
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
          </div>
        </div>
      }
    >
        {/* Quick Status Overview */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Implementation</p>
                  <Badge 
                    variant={control.implementationStatus === "implemented" ? "default" : "outline"}
                    className="mt-1 capitalize"
                  >
                    {control.implementationStatus.replace('-', ' ')}
                  </Badge>
                </div>
                <Shield className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Testing Status</p>
                  <Badge 
                    variant={
                      control.testingStatus === "passed" ? "default" :
                      control.testingStatus === "failed" ? "destructive" :
                      "outline"
                    }
                    className="mt-1 capitalize"
                  >
                    {control.testingStatus.replace('-', ' ')}
                  </Badge>
                </div>
                <div className="text-right">
                  {control.testingStatus === "passed" ? (
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  ) : control.testingStatus === "failed" ? (
                    <AlertTriangle className="h-8 w-8 text-red-600" />
                  ) : (
                    <Target className="h-8 w-8 text-yellow-600" />
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Effectiveness</p>
                  <p className="text-2xl font-bold">{control.metrics.effectivenessScore.toFixed(1)}%</p>
                </div>
                <div className="text-right">
                  {control.metrics.effectivenessScore >= 90 ? (
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  ) : control.metrics.effectivenessScore >= 70 ? (
                    <Target className="h-8 w-8 text-yellow-600" />
                  ) : (
                    <AlertTriangle className="h-8 w-8 text-red-600" />
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Risk Level</p>
                  <Badge 
                    variant={
                      control.riskLevel === "critical" || control.riskLevel === "high" ? "destructive" :
                      control.riskLevel === "medium" ? "outline" :
                      "secondary"
                    }
                    className="mt-1 capitalize"
                  >
                    {control.riskLevel}
                  </Badge>
                </div>
                <div className="text-right">
                  <div className="text-sm text-muted-foreground">
                    Priority: {control.priority}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Control View */}
        <ControlDetailView
          control={control}
          onClose={() => router.back()}
          onEdit={(control) => {
            console.log("Edit control:", control)
            // Navigate to edit page or open edit modal
          }}
          onDownload={(control) => {
            console.log("Download control evidence:", control)
            // Trigger control evidence download
          }}
          onArchive={(control) => {
            console.log("Archive control:", control)
            router.back()
          }}
          className="relative inset-auto bg-transparent backdrop-blur-none"
        />
    </StandardModuleLayout>
  )
}
