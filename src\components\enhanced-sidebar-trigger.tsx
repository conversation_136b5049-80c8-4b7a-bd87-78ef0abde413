"use client"

import * as React from "react"
import { PanelLeft } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useSidebar } from "@/components/ui/sidebar"
import { cn } from "@/lib/utils"

export const EnhancedSidebarTrigger = React.forwardRef<
  React.ElementRef<typeof Button>,
  React.ComponentProps<typeof Button>
>(({ className, onClick, ...props }, ref) => {
  const { toggleSidebar } = useSidebar()

  return (
    <Button
      ref={ref}
      data-sidebar="trigger"
      variant="ghost"
      size="sm"
      className={cn("h-8 px-2 gap-1.5", className)}
      onClick={(event) => {
        onClick?.(event)
        toggleSidebar()
      }}
      {...props}
    >
      <PanelLeft className="h-4 w-4" />
      <span className="text-xs text-muted-foreground font-mono">Alt+S</span>
      <span className="sr-only">Toggle Sidebar (Alt + S)</span>
    </Button>
  )
})
EnhancedSidebarTrigger.displayName = "EnhancedSidebarTrigger"
