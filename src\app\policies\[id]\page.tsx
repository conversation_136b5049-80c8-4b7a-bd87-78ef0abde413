"use client"

import React from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, Edit, Download, Share, Archive, AlertTriangle, CheckCircle, Shield } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"

import { PolicyDetailView } from "@/components/policies/policy-detail-view"
import { samplePolicies } from "@/lib/policies-data"
import { Policy } from "@/types/policies"
import { StandardModuleLayout, ModuleLayoutConfigs } from "@/components/standard-module-layout"

interface PolicyDetailPageProps {
  params: Promise<{
    id: string
  }>
}

export default function PolicyDetailPage({ params }: PolicyDetailPageProps) {
  const router = useRouter()
  const { id } = React.use(params)

  // Find the policy by ID
  const policy = samplePolicies.find(p => p.id === id)

  if (!policy) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Policy Not Found</h1>
            <p className="text-muted-foreground">
              The requested policy could not be found.
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <StandardModuleLayout
      {...ModuleLayoutConfigs.detail}
      hasCustomHeader
      customHeader={
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex-1">
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold tracking-tight">{policy.title}</h1>
              <Badge variant="outline">v{policy.version}</Badge>
            </div>
            <p className="text-muted-foreground">
              {policy.description}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Share className="mr-2 h-4 w-4" />
              Share
            </Button>
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Download
            </Button>
            <Button variant="outline" size="sm">
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
          </div>
        </div>
      }
    >
        {/* Quick Status Overview */}
        <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">Status</p>
                <Badge 
                  variant={policy.status === "published" ? "default" : "outline"}
                  className="mt-1"
                >
                  {policy.status.replace('-', ' ')}
                </Badge>
              </div>
              <CheckCircle className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">Compliance</p>
                <p className="text-2xl font-bold">{policy.metrics.complianceRate.toFixed(1)}%</p>
              </div>
              <div className="text-right">
                {policy.metrics.complianceRate >= 95 ? (
                  <CheckCircle className="h-8 w-8 text-green-600" />
                ) : (
                  <AlertTriangle className="h-8 w-8 text-yellow-600" />
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">Enforcement</p>
                <p className="text-sm capitalize">{policy.enforcementStatus.replace('-', ' ')}</p>
              </div>
              <Shield className={`h-8 w-8 ${
                policy.enforcementStatus === "fully-enforced" ? "text-green-600" :
                policy.enforcementStatus === "partially-enforced" ? "text-yellow-600" :
                "text-red-600"
              }`} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">Next Review</p>
                <p className="text-sm">{new Date(policy.nextReviewDate).toLocaleDateString()}</p>
              </div>
              <div className="text-right">
                <Badge variant={
                  new Date(policy.nextReviewDate) < new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) 
                    ? "destructive" 
                    : "outline"
                }>
                  {policy.reviewFrequency}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

        {/* Detailed Policy View */}
        <PolicyDetailView
          policy={policy}
          onClose={() => router.back()}
          onEdit={(policy) => {
            console.log("Edit policy:", policy)
            // Navigate to edit page or open edit modal
          }}
          onDownload={(policy) => {
            console.log("Download policy:", policy)
            // Trigger policy download
          }}
          onArchive={(policy) => {
            console.log("Archive policy:", policy)
            router.back()
          }}
          className="relative inset-auto bg-transparent backdrop-blur-none"
        />
    </StandardModuleLayout>
  )
}
