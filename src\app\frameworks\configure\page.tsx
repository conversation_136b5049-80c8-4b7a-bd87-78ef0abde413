"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { 
  ArrowLeft, 
  Shield, 
  Calendar, 
  Users,
  Settings,
  Download,
  Upload,
  FileText,
  Target
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"

import { sampleFrameworkTemplates } from "@/lib/frameworks-data"
import { FrameworkType, FrameworkStandard, ImplementationStatus, CertificationStatus } from "@/types/frameworks"

export default function ConfigureFrameworkPage() {
  const router = useRouter()
  const [selectedTemplate, setSelectedTemplate] = useState<string>("")
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    frameworkType: "" as FrameworkType,
    standard: "" as FrameworkStandard,
    version: "",
    implementationStatus: "not-started" as ImplementationStatus,
    certificationStatus: "not-applicable" as CertificationStatus,
    targetCompletionDate: "",
    certificationDate: "",
    implementationTeam: "",
    owner: "",
    budget: "",
    customControls: false,
    oscalImport: false,
    automatedAssessment: false,
    continuousMonitoring: false
  })

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId)
    const template = sampleFrameworkTemplates.find(t => t.id === templateId)
    if (template) {
      setFormData(prev => ({
        ...prev,
        name: template.name,
        description: template.description,
        frameworkType: template.frameworkType,
        standard: template.standard,
        version: template.version
      }))
    }
  }

  const handleSubmit = () => {
    // In a real implementation, this would submit the form data
    console.log("Creating framework with data:", formData)
    router.push("/frameworks")
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Configure Framework</h1>
          <p className="text-muted-foreground">
            Add a new compliance framework or customize an existing template
          </p>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Framework Library */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Framework Library
              </CardTitle>
              <CardDescription>
                Choose from standard frameworks or create custom
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Button
                  variant={selectedTemplate === "" ? "default" : "outline"}
                  className="w-full justify-start"
                  onClick={() => setSelectedTemplate("")}
                >
                  <Settings className="mr-2 h-4 w-4" />
                  Custom Framework
                </Button>
              </div>
              
              <div className="space-y-2">
                <Label className="text-sm font-medium">Standard Templates</Label>
                {sampleFrameworkTemplates.map((template) => (
                  <Button
                    key={template.id}
                    variant={selectedTemplate === template.id ? "default" : "outline"}
                    className="w-full justify-start text-left h-auto p-3"
                    onClick={() => handleTemplateSelect(template.id)}
                  >
                    <div className="space-y-1">
                      <div className="font-medium">{template.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {template.standard} • {template.controlCount} controls
                      </div>
                      <div className="flex gap-1">
                        {template.isOfficial && (
                          <Badge variant="secondary" className="text-xs">Official</Badge>
                        )}
                        {template.oscalProfile && (
                          <Badge variant="outline" className="text-xs">OSCAL</Badge>
                        )}
                      </div>
                    </div>
                  </Button>
                ))}
              </div>

              <div className="pt-4 border-t">
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Import Options</Label>
                  <Button variant="outline" size="sm" className="w-full">
                    <Upload className="mr-2 h-4 w-4" />
                    Import OSCAL Profile
                  </Button>
                  <Button variant="outline" size="sm" className="w-full">
                    <FileText className="mr-2 h-4 w-4" />
                    Import from File
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Configuration Form */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Framework Configuration</CardTitle>
              <CardDescription>
                Configure framework settings and implementation details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="general" className="space-y-6">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="general">General</TabsTrigger>
                  <TabsTrigger value="implementation">Implementation</TabsTrigger>
                  <TabsTrigger value="certification">Certification</TabsTrigger>
                  <TabsTrigger value="advanced">Advanced</TabsTrigger>
                </TabsList>

                <TabsContent value="general" className="space-y-4">
                  <div className="grid gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Framework Name</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter framework name"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Enter framework description"
                        rows={3}
                      />
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label>Framework Type</Label>
                        <Select
                          value={formData.frameworkType}
                          onValueChange={(value: FrameworkType) => 
                            setFormData(prev => ({ ...prev, frameworkType: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="regulatory">Regulatory</SelectItem>
                            <SelectItem value="industry">Industry Standard</SelectItem>
                            <SelectItem value="internal">Internal</SelectItem>
                            <SelectItem value="hybrid">Hybrid</SelectItem>
                            <SelectItem value="custom">Custom</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Standard</Label>
                        <Select
                          value={formData.standard}
                          onValueChange={(value: FrameworkStandard) => 
                            setFormData(prev => ({ ...prev, standard: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select standard" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="NIST CSF 2.0">NIST CSF 2.0</SelectItem>
                            <SelectItem value="ISO 27001">ISO 27001</SelectItem>
                            <SelectItem value="SOC 2">SOC 2</SelectItem>
                            <SelectItem value="PCI DSS">PCI DSS</SelectItem>
                            <SelectItem value="HIPAA">HIPAA</SelectItem>
                            <SelectItem value="GDPR">GDPR</SelectItem>
                            <SelectItem value="Custom">Custom</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="version">Version</Label>
                      <Input
                        id="version"
                        value={formData.version}
                        onChange={(e) => setFormData(prev => ({ ...prev, version: e.target.value }))}
                        placeholder="e.g., 2.0, 2022, 4.0"
                      />
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="implementation" className="space-y-4">
                  <div className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label>Implementation Status</Label>
                        <Select
                          value={formData.implementationStatus}
                          onValueChange={(value: ImplementationStatus) => 
                            setFormData(prev => ({ ...prev, implementationStatus: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="not-started">Not Started</SelectItem>
                            <SelectItem value="in-progress">In Progress</SelectItem>
                            <SelectItem value="implemented">Implemented</SelectItem>
                            <SelectItem value="certified">Certified</SelectItem>
                            <SelectItem value="maintenance">Maintenance</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="targetDate">Target Completion Date</Label>
                        <Input
                          id="targetDate"
                          type="date"
                          value={formData.targetCompletionDate}
                          onChange={(e) => setFormData(prev => ({ ...prev, targetCompletionDate: e.target.value }))}
                        />
                      </div>
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor="owner">Framework Owner</Label>
                        <Input
                          id="owner"
                          value={formData.owner}
                          onChange={(e) => setFormData(prev => ({ ...prev, owner: e.target.value }))}
                          placeholder="Enter owner name or email"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="team">Implementation Team</Label>
                        <Input
                          id="team"
                          value={formData.implementationTeam}
                          onChange={(e) => setFormData(prev => ({ ...prev, implementationTeam: e.target.value }))}
                          placeholder="Enter team members (comma separated)"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="budget">Budget (Optional)</Label>
                      <Input
                        id="budget"
                        value={formData.budget}
                        onChange={(e) => setFormData(prev => ({ ...prev, budget: e.target.value }))}
                        placeholder="Enter implementation budget"
                      />
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="certification" className="space-y-4">
                  <div className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label>Certification Status</Label>
                        <Select
                          value={formData.certificationStatus}
                          onValueChange={(value: CertificationStatus) => 
                            setFormData(prev => ({ ...prev, certificationStatus: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="not-applicable">Not Applicable</SelectItem>
                            <SelectItem value="planning">Planning</SelectItem>
                            <SelectItem value="in-process">In Process</SelectItem>
                            <SelectItem value="certified">Certified</SelectItem>
                            <SelectItem value="expired">Expired</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="certDate">Certification Date</Label>
                        <Input
                          id="certDate"
                          type="date"
                          value={formData.certificationDate}
                          onChange={(e) => setFormData(prev => ({ ...prev, certificationDate: e.target.value }))}
                        />
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="advanced" className="space-y-4">
                  <div className="space-y-4">
                    <Label className="text-sm font-medium">Advanced Options</Label>
                    
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="customControls"
                          checked={formData.customControls}
                          onCheckedChange={(checked) => 
                            setFormData(prev => ({ ...prev, customControls: !!checked }))
                          }
                        />
                        <Label htmlFor="customControls">
                          Enable custom control definitions
                        </Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="oscalImport"
                          checked={formData.oscalImport}
                          onCheckedChange={(checked) => 
                            setFormData(prev => ({ ...prev, oscalImport: !!checked }))
                          }
                        />
                        <Label htmlFor="oscalImport">
                          Enable OSCAL import/export capabilities
                        </Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="automatedAssessment"
                          checked={formData.automatedAssessment}
                          onCheckedChange={(checked) => 
                            setFormData(prev => ({ ...prev, automatedAssessment: !!checked }))
                          }
                        />
                        <Label htmlFor="automatedAssessment">
                          Enable automated assessment capabilities
                        </Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="continuousMonitoring"
                          checked={formData.continuousMonitoring}
                          onCheckedChange={(checked) => 
                            setFormData(prev => ({ ...prev, continuousMonitoring: !!checked }))
                          }
                        />
                        <Label htmlFor="continuousMonitoring">
                          Enable continuous monitoring and reporting
                        </Label>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>

              <div className="flex justify-end gap-4 pt-6 border-t">
                <Button variant="outline" onClick={() => router.back()}>
                  Cancel
                </Button>
                <Button onClick={handleSubmit}>
                  <Target className="mr-2 h-4 w-4" />
                  Create Framework
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
