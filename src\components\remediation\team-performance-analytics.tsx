"use client"

import React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Users, Award, TrendingUp, Clock } from "lucide-react"
import { sampleTeamMembers } from "@/lib/remediation-data"

export function TeamPerformanceAnalytics() {
  return (
    <div className="space-y-6">
      {/* Team Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Team Size
            </CardTitle>
            <CardDescription>Active analysts</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{sampleTeamMembers.length}</div>
            <p className="text-xs text-muted-foreground">
              {sampleTeamMembers.filter(m => m.availability === "available").length} available
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Efficiency
            </CardTitle>
            <CardDescription>Team productivity</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">87%</div>
            <p className="text-xs text-muted-foreground">
              +5% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Avg Case Load
            </CardTitle>
            <CardDescription>Cases per analyst</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3.2</div>
            <p className="text-xs text-muted-foreground">
              Within optimal range
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Certifications
            </CardTitle>
            <CardDescription>Professional credentials</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">45</div>
            <p className="text-xs text-muted-foreground">
              Total team certifications
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Individual Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Individual Performance</CardTitle>
          <CardDescription>Analyst productivity and case resolution metrics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {sampleTeamMembers.map((member) => {
              const workloadPercentage = (member.currentCases / member.maxCaseLoad) * 100
              const efficiency = Math.floor(Math.random() * 20) + 80 // Mock efficiency score
              
              return (
                <div key={member.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{member.name}</h4>
                      <Badge variant="outline">{member.role}</Badge>
                      <Badge variant={member.availability === "available" ? "default" : "secondary"}>
                        {member.availability}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span>Cases: {member.currentCases}/{member.maxCaseLoad}</span>
                      <span>Efficiency: {efficiency}%</span>
                      <span>Certs: {member.certifications.length}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="text-right">
                      <div className="text-sm font-medium">Workload: {Math.round(workloadPercentage)}%</div>
                      <Progress value={workloadPercentage} className="w-24 h-2" />
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">Performance: {efficiency}%</div>
                      <Progress value={efficiency} className="w-24 h-2" />
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Skills and Training */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Skills Distribution</CardTitle>
            <CardDescription>Team expertise areas</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Array.from(new Set(sampleTeamMembers.flatMap(m => m.skills))).map((skill) => {
                const memberCount = sampleTeamMembers.filter(m => m.skills.includes(skill)).length
                const percentage = (memberCount / sampleTeamMembers.length) * 100
                
                return (
                  <div key={skill} className="flex items-center justify-between">
                    <span className="text-sm">{skill}</span>
                    <div className="flex items-center gap-2">
                      <div className="w-20 bg-muted rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full" 
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium w-8">{memberCount}</span>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Training Recommendations</CardTitle>
            <CardDescription>Suggested skill development areas</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { skill: "Cloud Forensics", priority: "High", members: 2 },
                { skill: "AI/ML Security", priority: "Medium", members: 1 },
                { skill: "Mobile Forensics", priority: "Medium", members: 3 },
                { skill: "Threat Hunting", priority: "Low", members: 4 },
              ].map((recommendation) => (
                <div key={recommendation.skill} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">{recommendation.skill}</p>
                    <p className="text-xs text-muted-foreground">
                      {recommendation.members} team members have this skill
                    </p>
                  </div>
                  <Badge variant={
                    recommendation.priority === "High" ? "destructive" :
                    recommendation.priority === "Medium" ? "default" : "outline"
                  }>
                    {recommendation.priority}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Trends</CardTitle>
          <CardDescription>Team metrics over time</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <h4 className="text-sm font-medium">This Month</h4>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>Cases Resolved</span>
                  <span className="font-medium">156</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Avg Resolution Time</span>
                  <span className="font-medium">4.2h</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Team Efficiency</span>
                  <span className="font-medium">87%</span>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Last Month</h4>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>Cases Resolved</span>
                  <span className="font-medium">142</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Avg Resolution Time</span>
                  <span className="font-medium">4.8h</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Team Efficiency</span>
                  <span className="font-medium">82%</span>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Change</h4>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>Cases</span>
                  <Badge variant="outline" className="text-green-600">+9.9%</Badge>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Time</span>
                  <Badge variant="outline" className="text-green-600">-12.5%</Badge>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Efficiency</span>
                  <Badge variant="outline" className="text-green-600">+6.1%</Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
