"use client"

import React from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Filter, Play, Settings } from "lucide-react"
import { RemediationPlaybook } from "@/types/remediation"

interface PlaybookTableProps {
  playbooks: RemediationPlaybook[]
}

export function PlaybookTable({ playbooks }: PlaybookTableProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input placeholder="Search playbooks..." className="pl-8 w-64" />
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
        </div>
      </div>
      
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            {playbooks.map((playbook) => (
              <div key={playbook.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{playbook.name}</h4>
                    <Badge variant={playbook.automated ? "default" : "outline"}>
                      {playbook.automated ? "Automated" : "Manual"}
                    </Badge>
                    <Badge variant="outline">{playbook.category}</Badge>
                    {!playbook.isActive && <Badge variant="secondary">Inactive</Badge>}
                  </div>
                  <p className="text-sm text-muted-foreground">{playbook.description}</p>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span>Version: {playbook.version}</span>
                    <span>Usage: {playbook.usageCount}</span>
                    <span>Success Rate: {playbook.successRate}%</span>
                    <span>Est. Duration: {Math.floor(playbook.estimatedDuration / 60)}h</span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Play className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
