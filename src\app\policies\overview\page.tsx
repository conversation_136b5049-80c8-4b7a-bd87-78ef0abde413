"use client"

import { useState } from "react"
import Link from "next/link"
import { 
  FileText, 
  Plus,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  Users,
  Shield
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { StandardModuleLayout, ModuleLayoutConfigs, createSearchConfig } from "@/components/standard-module-layout"

export default function PoliciesOverviewPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [currentSort, setCurrentSort] = useState("")

  // Filter and sort options
  const filterOptions = [
    { label: "Active", value: "active" },
    { label: "Draft", value: "draft" },
    { label: "Under Review", value: "review" },
    { label: "Archived", value: "archived" },
    { label: "High Priority", value: "high" },
    { label: "Medium Priority", value: "medium" },
    { label: "Low Priority", value: "low" },
  ]

  const sortOptions = [
    { label: "Name (A-Z)", value: "name-asc" },
    { label: "Name (Z-A)", value: "name-desc" },
    { label: "Updated (Newest)", value: "updated-desc" },
    { label: "Updated (Oldest)", value: "updated-asc" },
    { label: "Priority", value: "priority" },
    { label: "Status", value: "status" },
  ]

  const searchConfig = createSearchConfig("policies", {
    actionButton: (
      <Button asChild>
        <Link href="/policies/create">
          <Plus className="mr-2 h-4 w-4" />
          Create Policy
        </Link>
      </Button>
    )
  })

  return (
    <StandardModuleLayout
      {...ModuleLayoutConfigs.overview}
      customHeader={
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Policies</h1>
            <p className="text-muted-foreground">
              Comprehensive policy management with automated compliance tracking and OPA-based enforcement
            </p>
          </div>
          <Button asChild>
            <Link href="/policies/create">
              <Plus className="mr-2 h-4 w-4" />
              Create Policy
            </Link>
          </Button>
        </div>
      }
    >
        {/* Dashboard Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Policies</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">127</div>
              <p className="text-xs text-muted-foreground">
                +3 from last month
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Violations</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">23</div>
              <p className="text-xs text-muted-foreground">
                -5 from last week
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Compliance Rate</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">94.2%</div>
              <p className="text-xs text-muted-foreground">
                +2.1% from last month
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Reviews</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">8</div>
              <p className="text-xs text-muted-foreground">
                2 overdue
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Policy Categories */}
        <div className="grid gap-6 lg:grid-cols-2 mb-6">
          <Card>
            <CardHeader>
              <CardTitle>Policy Categories</CardTitle>
              <CardDescription>
                Distribution of policies by category
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { name: "Security Policies", count: 45, percentage: 85 },
                  { name: "Privacy Policies", count: 32, percentage: 92 },
                  { name: "Operational Policies", count: 28, percentage: 78 },
                  { name: "HR Policies", count: 22, percentage: 95 },
                ].map((category) => (
                  <div key={category.name} className="flex items-center space-x-4">
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">{category.name}</p>
                        <span className="text-sm text-muted-foreground">
                          {category.count} policies
                        </span>
                      </div>
                      <Progress value={category.percentage} className="h-2" />
                    </div>
                    <Badge variant="outline">
                      {category.percentage}%
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest policy updates and violations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    type: "violation",
                    title: "Data Retention Policy Violation",
                    description: "Automated detection in Marketing dept",
                    time: "2 hours ago",
                    severity: "high"
                  },
                  {
                    type: "update",
                    title: "Password Policy Updated",
                    description: "Minimum length increased to 12 characters",
                    time: "1 day ago",
                    severity: "medium"
                  },
                  {
                    type: "exception",
                    title: "Exception Request Approved",
                    description: "Temporary access for audit team",
                    time: "2 days ago",
                    severity: "low"
                  },
                ].map((activity, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className={`w-2 h-2 rounded-full mt-2 ${
                      activity.severity === 'high' ? 'bg-red-500' :
                      activity.severity === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                    }`} />
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium">{activity.title}</p>
                      <p className="text-xs text-muted-foreground">{activity.description}</p>
                      <p className="text-xs text-muted-foreground">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common policy management tasks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Button variant="outline" className="h-20 flex-col gap-2" asChild>
                <Link href="/policies/all">
                  <FileText className="h-6 w-6" />
                  <span>View All Policies</span>
                </Link>
              </Button>
              <Button variant="outline" className="h-20 flex-col gap-2" asChild>
                <Link href="/policies/violations">
                  <AlertTriangle className="h-6 w-6" />
                  <span>Review Violations</span>
                </Link>
              </Button>
              <Button variant="outline" className="h-20 flex-col gap-2" asChild>
                <Link href="/policies/exceptions">
                  <Clock className="h-6 w-6" />
                  <span>Manage Exceptions</span>
                </Link>
              </Button>
              <Button variant="outline" className="h-20 flex-col gap-2" asChild>
                <Link href="/policies/create">
                  <Plus className="h-6 w-6" />
                  <span>Create New Policy</span>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
    </StandardModuleLayout>
  )
}
