"use client"

import React from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON>eft, Save, Play, Settings, Plus, Trash2 } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"

interface PlaybookStep {
  id: string
  name: string
  description: string
  type: "manual" | "automated" | "approval"
  order: number
  required: boolean
  estimatedDuration: number
}

export default function CreatePlaybookPage() {
  const router = useRouter()
  const [formData, setFormData] = React.useState({
    name: "",
    description: "",
    category: "",
    version: "1.0.0",
    automated: false,
    approvalRequired: false,
    estimatedDuration: 60,
    tags: [] as string[]
  })

  const [steps, setSteps] = React.useState<PlaybookStep[]>([
    {
      id: "step-1",
      name: "Initial Assessment",
      description: "Assess the scope and severity of the incident",
      type: "manual",
      order: 1,
      required: true,
      estimatedDuration: 30
    }
  ])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Here you would typically submit the form data to your API
    console.log("Creating playbook:", { ...formData, steps })
    // Redirect back to automation page
    router.push("/remediation/automation")
  }

  const handleCancel = () => {
    router.back()
  }

  const addStep = () => {
    const newStep: PlaybookStep = {
      id: `step-${steps.length + 1}`,
      name: "",
      description: "",
      type: "manual",
      order: steps.length + 1,
      required: false,
      estimatedDuration: 30
    }
    setSteps([...steps, newStep])
  }

  const removeStep = (stepId: string) => {
    setSteps(steps.filter(step => step.id !== stepId))
  }

  const updateStep = (stepId: string, updates: Partial<PlaybookStep>) => {
    setSteps(steps.map(step => 
      step.id === stepId ? { ...step, ...updates } : step
    ))
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Create New Playbook</h1>
              <p className="text-muted-foreground">
                Design an automated remediation playbook
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button onClick={handleSubmit}>
              <Save className="mr-2 h-4 w-4" />
              Create Playbook
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 space-y-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Play className="h-5 w-5" />
                Playbook Information
              </CardTitle>
              <CardDescription>
                Define the basic properties of this remediation playbook
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="name">Playbook Name *</Label>
                  <Input
                    id="name"
                    placeholder="Enter playbook name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="version">Version</Label>
                  <Input
                    id="version"
                    placeholder="1.0.0"
                    value={formData.version}
                    onChange={(e) => setFormData({ ...formData, version: e.target.value })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  placeholder="Describe what this playbook does and when to use it"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  required
                />
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="incident-response">Incident Response</SelectItem>
                      <SelectItem value="containment">Containment</SelectItem>
                      <SelectItem value="eradication">Eradication</SelectItem>
                      <SelectItem value="recovery">Recovery</SelectItem>
                      <SelectItem value="communication">Communication</SelectItem>
                      <SelectItem value="forensics">Forensics</SelectItem>
                      <SelectItem value="threat-hunting">Threat Hunting</SelectItem>
                      <SelectItem value="compliance">Compliance</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="estimatedDuration">Est. Duration (minutes)</Label>
                  <Input
                    id="estimatedDuration"
                    type="number"
                    value={formData.estimatedDuration}
                    onChange={(e) => setFormData({ ...formData, estimatedDuration: parseInt(e.target.value) || 60 })}
                  />
                </div>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="automated"
                      checked={formData.automated}
                      onCheckedChange={(checked) => setFormData({ ...formData, automated: checked })}
                    />
                    <Label htmlFor="automated">Fully Automated</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="approvalRequired"
                      checked={formData.approvalRequired}
                      onCheckedChange={(checked) => setFormData({ ...formData, approvalRequired: checked })}
                    />
                    <Label htmlFor="approvalRequired">Requires Approval</Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Tags</Label>
                <div className="flex flex-wrap gap-2">
                  {["malware", "phishing", "data-breach", "ddos", "insider-threat", "compliance", "critical", "automated"].map((tag) => (
                    <Badge
                      key={tag}
                      variant={formData.tags.includes(tag) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        const tags = formData.tags.includes(tag)
                          ? formData.tags.filter(t => t !== tag)
                          : [...formData.tags, tag]
                        setFormData({ ...formData, tags })
                      }}
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Playbook Steps */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Playbook Steps
              </CardTitle>
              <CardDescription>
                Define the sequence of actions in this playbook
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {steps.map((step, index) => (
                <div key={step.id} className="p-4 border rounded-lg space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Step {index + 1}</h4>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{step.type}</Badge>
                      {steps.length > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeStep(step.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Step Name</Label>
                      <Input
                        value={step.name}
                        onChange={(e) => updateStep(step.id, { name: e.target.value })}
                        placeholder="Enter step name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Type</Label>
                      <Select
                        value={step.type}
                        onValueChange={(value: "manual" | "automated" | "approval") => 
                          updateStep(step.id, { type: value })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="manual">Manual</SelectItem>
                          <SelectItem value="automated">Automated</SelectItem>
                          <SelectItem value="approval">Approval</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Description</Label>
                    <Textarea
                      value={step.description}
                      onChange={(e) => updateStep(step.id, { description: e.target.value })}
                      placeholder="Describe what this step does"
                      rows={2}
                    />
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Estimated Duration (minutes)</Label>
                      <Input
                        type="number"
                        value={step.estimatedDuration}
                        onChange={(e) => updateStep(step.id, { estimatedDuration: parseInt(e.target.value) || 30 })}
                      />
                    </div>
                    <div className="flex items-center space-x-2 pt-6">
                      <Switch
                        checked={step.required}
                        onCheckedChange={(checked) => updateStep(step.id, { required: checked })}
                      />
                      <Label>Required Step</Label>
                    </div>
                  </div>
                </div>
              ))}

              <Button
                type="button"
                variant="outline"
                onClick={addStep}
                className="w-full"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Step
              </Button>
            </CardContent>
          </Card>
        </form>
      </div>
    </div>
  )
}
