# GRCOS - GRC Orchestration System

A comprehensive GRC operations platform based on a flat, agent-orchestrated architecture. GRCOS provides unified governance, risk, and compliance management through blockchain-secured foundations and AI-powered automation.

## Architecture

GRCOS implements a flat navigation structure with 13 core sections, each leveraging specialized AI agents for automation and orchestration:

- **Overview**: Executive dashboard with System Agent intelligence
- **Activity**: Unified event stream from all system components
- **Assets**: Blockchain-secured CMDB foundation
- **Monitor**: Real-time security monitoring with AI-powered detection
- **Frameworks**: OSCAL-based compliance framework library
- **Controls**: Security control implementation and testing
- **Policies**: OPA-based policy management and enforcement
- **Assessments**: Automated risk assessments and gap analysis
- **Workflows**: Flowable engine business process automation
- **Remediation**: DFIR-IRIS integrated incident response
- **Reports**: AI-generated compliance documentation
- **Artifacts**: Blockchain-secured evidence repository
- **Portals**: Stakeholder-specific trust interfaces

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Technology Stack

- **Frontend**: Next.js 15 with React 19
- **UI Components**: shadcn/ui with Tailwind CSS
- **Typography**: Wix Madefor font family
- **Icons**: Lucide React icons
- **Theme**: Dark/light mode support
- **Authentication**: Appwrite integration

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
