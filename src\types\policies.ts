export type PolicyType = 
  | "security"
  | "privacy"
  | "compliance"
  | "operational"
  | "hr"
  | "financial"
  | "it"
  | "data-governance"
  | "risk-management"
  | "business-continuity"

export type PolicyStatus = 
  | "draft"
  | "under-review"
  | "approved"
  | "published"
  | "archived"
  | "expired"
  | "suspended"

export type PolicyPriority = "low" | "medium" | "high" | "critical"

export type EnforcementStatus = 
  | "not-enforced"
  | "partially-enforced"
  | "fully-enforced"
  | "enforcement-failed"

export type ComplianceLevel = 
  | "compliant"
  | "non-compliant"
  | "partially-compliant"
  | "not-assessed"

export type ReviewFrequency = 
  | "monthly"
  | "quarterly"
  | "semi-annually"
  | "annually"
  | "bi-annually"
  | "as-needed"

export type ApprovalStatus = 
  | "pending"
  | "approved"
  | "rejected"
  | "requires-changes"

export interface PolicyApproval {
  id: string
  approverId: string
  approverName: string
  approverRole: string
  status: ApprovalStatus
  approvalDate?: Date
  comments?: string
  blockchainHash?: string
  blockchainBlock?: string
  verificationTimestamp?: Date
}

export interface PolicyException {
  id: string
  policyId: string
  requestorId: string
  requestorName: string
  justification: string
  riskAssessment: string
  approvalStatus: ApprovalStatus
  approvedBy?: string
  approvedDate?: Date
  expirationDate: Date
  createdDate: Date
  isActive: boolean
  mitigationMeasures?: string[]
}

export interface PolicyViolation {
  id: string
  policyId: string
  violationType: "technical" | "procedural" | "compliance"
  severity: "low" | "medium" | "high" | "critical"
  description: string
  detectedDate: Date
  detectedBy: "automated" | "manual" | "audit"
  affectedAssets?: string[]
  remediationStatus: "open" | "in-progress" | "resolved" | "accepted-risk"
  remediationDueDate?: Date
  assignedTo?: string
  resolutionDate?: Date
  resolutionNotes?: string
}

export interface PolicyControl {
  id: string
  controlId: string
  frameworkId: string
  frameworkName: string
  controlTitle: string
  implementationStatus: "implemented" | "partially-implemented" | "not-implemented"
  testingStatus: "passed" | "failed" | "not-tested"
  lastTestDate?: Date
  nextTestDate?: Date
}

export interface PolicyAttachment {
  id: string
  name: string
  description?: string
  fileType: string
  fileSize: number
  uploadDate: Date
  uploadedBy: string
  downloadUrl: string
  isRequired: boolean
}

export interface PolicyVersion {
  id: string
  version: string
  changeDescription: string
  changedBy: string
  changeDate: Date
  approvals: PolicyApproval[]
  isActive: boolean
  documentUrl?: string
  blockchainHash?: string
}

export interface PolicyMetrics {
  complianceRate: number
  violationCount: number
  exceptionCount: number
  lastAssessmentDate?: Date
  nextReviewDate: Date
  attestationRate: number
  enforcementEffectiveness: number
}

export interface Policy {
  id: string
  title: string
  description: string
  policyType: PolicyType
  status: PolicyStatus
  priority: PolicyPriority
  version: string
  
  // Ownership and responsibility
  owner: string
  ownerEmail: string
  responsibleParty: string
  businessFunction: string
  
  // Lifecycle management
  createdDate: Date
  lastModifiedDate: Date
  lastReviewDate?: Date
  nextReviewDate: Date
  reviewFrequency: ReviewFrequency
  expirationDate?: Date
  
  // Approval and governance
  approvals: PolicyApproval[]
  currentApprovalStatus: ApprovalStatus
  approvalWorkflowId?: string
  
  // Content and documentation
  content: string
  summary: string
  scope: string
  applicability: string[]
  attachments: PolicyAttachment[]
  relatedPolicies: string[]
  
  // Compliance and controls
  applicableFrameworks: string[]
  mappedControls: PolicyControl[]
  complianceLevel: ComplianceLevel
  
  // Enforcement
  enforcementStatus: EnforcementStatus
  enforcementMechanism: string[]
  opaPolicy?: {
    regoCode: string
    packageName: string
    lastDeployed?: Date
    deploymentStatus: "deployed" | "pending" | "failed"
  }
  
  // Exceptions and violations
  activeExceptions: PolicyException[]
  recentViolations: PolicyViolation[]
  
  // Metrics and performance
  metrics: PolicyMetrics
  
  // Multi-entity support
  entityId: string
  
  // Versioning and audit trail
  versions: PolicyVersion[]
  
  // Blockchain verification
  blockchainStatus: "verified" | "pending" | "failed" | "not-applicable"
  blockchainHash?: string
  blockchainBlock?: string
  verificationTimestamp?: Date
  
  // Metadata
  tags: string[]
  customFields?: Record<string, any>
  isArchived: boolean
  createdBy: string
  lastModifiedBy: string
}

export interface PolicyTemplate {
  id: string
  name: string
  description: string
  policyType: PolicyType
  templateContent: string
  applicableFrameworks: string[]
  requiredFields: string[]
  optionalFields: string[]
  isOfficial: boolean
  source: string
  usageCount: number
  createdAt: Date
  updatedAt: Date
  tags: string[]
}

export interface PolicyWorkflow {
  id: string
  name: string
  description: string
  workflowType: "creation" | "review" | "approval" | "exception" | "retirement"
  steps: PolicyWorkflowStep[]
  isActive: boolean
  entityId: string
}

export interface PolicyWorkflowStep {
  id: string
  name: string
  description: string
  stepType: "review" | "approval" | "notification" | "automation"
  assignedTo: string[]
  requiredApprovals: number
  timeoutDays: number
  isParallel: boolean
  conditions?: Record<string, any>
}

export interface PolicyAttestation {
  id: string
  policyId: string
  userId: string
  userName: string
  userRole: string
  attestationDate: Date
  attestationType: "read" | "understood" | "compliance"
  comments?: string
  ipAddress: string
  userAgent: string
  isValid: boolean
  expirationDate?: Date
}

export interface PolicyAssessment {
  id: string
  policyId: string
  assessmentType: "compliance" | "effectiveness" | "gap-analysis"
  assessorId: string
  assessorName: string
  assessmentDate: Date
  findings: PolicyFinding[]
  overallRating: "excellent" | "good" | "fair" | "poor"
  recommendations: string[]
  nextAssessmentDate: Date
}

export interface PolicyFinding {
  id: string
  category: string
  severity: "low" | "medium" | "high" | "critical"
  description: string
  evidence?: string
  recommendation: string
  status: "open" | "in-progress" | "resolved"
  assignedTo?: string
  dueDate?: Date
}
