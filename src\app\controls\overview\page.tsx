"use client"

import { useState } from "react"
import Link from "next/link"
import { 
  Shield<PERSON>heck, 
  Plus,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  BarChart3
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { StandardModuleLayout, ModuleLayoutConfigs, createSearchConfig } from "@/components/standard-module-layout"

export default function ControlsOverviewPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [currentSort, setCurrentSort] = useState("")

  // Filter and sort options
  const filterOptions = [
    { label: "Effective", value: "effective" },
    { label: "Partially Effective", value: "partial" },
    { label: "Ineffective", value: "ineffective" },
    { label: "Not Tested", value: "not-tested" },
    { label: "Preventive", value: "preventive" },
    { label: "Detective", value: "detective" },
    { label: "Corrective", value: "corrective" },
    { label: "High Risk", value: "high" },
    { label: "Medium Risk", value: "medium" },
    { label: "Low Risk", value: "low" },
  ]

  const sortOptions = [
    { label: "Name (A-Z)", value: "name-asc" },
    { label: "Name (Z-A)", value: "name-desc" },
    { label: "Effectiveness", value: "effectiveness" },
    { label: "Last Tested", value: "tested" },
    { label: "Risk Level", value: "risk" },
    { label: "Control Type", value: "type" },
  ]

  const searchConfig = createSearchConfig("controls", {
    actionButton: (
      <Button asChild>
        <Link href="/controls/create">
          <Plus className="mr-2 h-4 w-4" />
          Create Control
        </Link>
      </Button>
    )
  })

  return (
    <StandardModuleLayout
      {...ModuleLayoutConfigs.overview}
      customHeader={
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Controls</h1>
            <p className="text-muted-foreground">
              Comprehensive control management with automated testing and effectiveness monitoring
            </p>
          </div>
          <Button asChild>
            <Link href="/controls/create">
              <Plus className="mr-2 h-4 w-4" />
              Create Control
            </Link>
          </Button>
        </div>
      }
    >
        {/* Dashboard Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Controls</CardTitle>
              <ShieldCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">342</div>
              <p className="text-xs text-muted-foreground">
                +12 from last quarter
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Effectiveness Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">87.3%</div>
              <p className="text-xs text-muted-foreground">
                +3.2% from last month
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Failed Tests</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">18</div>
              <p className="text-xs text-muted-foreground">
                Require attention
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Due for Testing</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">45</div>
              <p className="text-xs text-muted-foreground">
                Next 30 days
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Control Effectiveness by Category */}
        <div className="grid gap-6 lg:grid-cols-2 mb-6">
          <Card>
            <CardHeader>
              <CardTitle>Control Effectiveness by Category</CardTitle>
              <CardDescription>
                Effectiveness metrics across different control categories
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { name: "Access Controls", total: 89, effective: 82, percentage: 92 },
                  { name: "Data Protection", total: 67, effective: 58, percentage: 87 },
                  { name: "Network Security", total: 54, effective: 45, percentage: 83 },
                  { name: "Incident Response", total: 32, effective: 28, percentage: 88 },
                  { name: "Business Continuity", total: 28, effective: 24, percentage: 86 },
                ].map((category) => (
                  <div key={category.name} className="flex items-center space-x-4">
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">{category.name}</p>
                        <span className="text-sm text-muted-foreground">
                          {category.effective}/{category.total}
                        </span>
                      </div>
                      <Progress value={category.percentage} className="h-2" />
                    </div>
                    <Badge variant="outline">
                      {category.percentage}%
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Testing Schedule</CardTitle>
              <CardDescription>
                Upcoming control testing activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    control: "User Access Review",
                    type: "Quarterly",
                    dueDate: "2024-02-15",
                    owner: "IT Security",
                    status: "scheduled"
                  },
                  {
                    control: "Backup Verification",
                    type: "Monthly",
                    dueDate: "2024-02-01",
                    owner: "Operations",
                    status: "overdue"
                  },
                  {
                    control: "Vulnerability Scanning",
                    type: "Weekly",
                    dueDate: "2024-01-28",
                    owner: "Security Team",
                    status: "in-progress"
                  },
                ].map((test, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className={`w-2 h-2 rounded-full mt-2 ${
                      test.status === 'overdue' ? 'bg-red-500' :
                      test.status === 'in-progress' ? 'bg-yellow-500' : 'bg-green-500'
                    }`} />
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium">{test.control}</p>
                      <p className="text-xs text-muted-foreground">{test.type} • {test.owner}</p>
                      <p className="text-xs text-muted-foreground">Due: {test.dueDate}</p>
                    </div>
                    <Badge variant={test.status === 'overdue' ? 'destructive' : 'outline'}>
                      {test.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Control Performance Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Control Performance Trends</CardTitle>
            <CardDescription>
              Monthly effectiveness trends and key metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <div className="text-2xl font-bold text-green-600">94.2%</div>
                <div className="text-sm text-muted-foreground">Preventive Controls</div>
                <div className="text-xs text-green-600">+2.1% from last month</div>
              </div>
              <div className="space-y-2">
                <div className="text-2xl font-bold text-blue-600">89.7%</div>
                <div className="text-sm text-muted-foreground">Detective Controls</div>
                <div className="text-xs text-blue-600">+1.8% from last month</div>
              </div>
              <div className="space-y-2">
                <div className="text-2xl font-bold text-orange-600">76.3%</div>
                <div className="text-sm text-muted-foreground">Corrective Controls</div>
                <div className="text-xs text-orange-600">-0.5% from last month</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common control management tasks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Button variant="outline" className="h-20 flex-col gap-2" asChild>
                <Link href="/controls/all">
                  <ShieldCheck className="h-6 w-6" />
                  <span>View All Controls</span>
                </Link>
              </Button>
              <Button variant="outline" className="h-20 flex-col gap-2" asChild>
                <Link href="/controls/testing">
                  <Target className="h-6 w-6" />
                  <span>Schedule Testing</span>
                </Link>
              </Button>
              <Button variant="outline" className="h-20 flex-col gap-2" asChild>
                <Link href="/controls/effectiveness">
                  <BarChart3 className="h-6 w-6" />
                  <span>View Effectiveness</span>
                </Link>
              </Button>
              <Button variant="outline" className="h-20 flex-col gap-2" asChild>
                <Link href="/controls/create">
                  <Plus className="h-6 w-6" />
                  <span>Create New Control</span>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
    </StandardModuleLayout>
  )
}
