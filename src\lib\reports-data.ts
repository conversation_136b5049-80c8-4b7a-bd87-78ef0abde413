import { Report, ReportTemplate, ReportGenerationProgress } from "@/types/reports"

// Sample reports data for demonstration
export const sampleReports: Report[] = [
  {
    id: "rpt-001",
    name: "SOC 2 Type II Compliance Report Q4 2024",
    description: "Comprehensive SOC 2 Type II compliance assessment covering security, availability, and confidentiality controls",
    reportType: "compliance",
    framework: "SOC 2",
    generationDate: new Date("2024-01-15T14:30:00Z"),
    status: "generated",
    targetAudience: "auditor",
    dataFreshness: new Date("2024-01-14T23:59:59Z"),
    category: "compliance-reports",
    createdBy: "<EMAIL>",
    fileSize: 3456789,
    fileFormat: "pdf",
    downloadUrl: "/reports/download/rpt-001",
    shareUrl: "/reports/share/rpt-001",
    isScheduled: true,
    scheduledDate: new Date("2024-04-15T14:30:00Z"),
    tags: ["SOC 2", "quarterly", "audit-ready"],
    entityId: "auris-hq",
    lastAccessed: new Date("2024-01-16T09:15:00Z"),
    accessCount: 23,
    isArchived: false,
    retentionDate: new Date("2027-01-15T14:30:00Z")
  },
  {
    id: "rpt-002",
    name: "Executive Risk Dashboard - January 2024",
    description: "Monthly executive summary of risk posture, compliance status, and key performance indicators",
    reportType: "executive",
    framework: "N/A",
    generationDate: new Date("2024-01-31T16:00:00Z"),
    status: "generated",
    targetAudience: "board",
    dataFreshness: new Date("2024-01-31T15:45:00Z"),
    category: "executive-reports",
    createdBy: "<EMAIL>",
    fileSize: 1234567,
    fileFormat: "pdf",
    downloadUrl: "/reports/download/rpt-002",
    shareUrl: "/reports/share/rpt-002",
    isScheduled: true,
    scheduledDate: new Date("2024-02-29T16:00:00Z"),
    tags: ["executive", "monthly", "risk-summary"],
    entityId: "auris-hq",
    lastAccessed: new Date("2024-02-01T08:30:00Z"),
    accessCount: 45,
    isArchived: false
  },
  {
    id: "rpt-003",
    name: "NIST CSF 2.0 Implementation Assessment",
    description: "Detailed assessment of NIST Cybersecurity Framework 2.0 implementation status and gap analysis",
    reportType: "compliance",
    framework: "NIST CSF 2.0",
    generationDate: new Date("2024-01-20T11:15:00Z"),
    status: "generated",
    targetAudience: "internal",
    dataFreshness: new Date("2024-01-19T23:59:59Z"),
    category: "compliance-reports",
    createdBy: "<EMAIL>",
    fileSize: 2789456,
    fileFormat: "pdf",
    downloadUrl: "/reports/download/rpt-003",
    isScheduled: false,
    tags: ["NIST", "assessment", "gap-analysis"],
    entityId: "auris-hq",
    lastAccessed: new Date("2024-01-22T14:20:00Z"),
    accessCount: 12,
    isArchived: false
  },
  {
    id: "rpt-004",
    name: "Customer Security Attestation - TechCorp",
    description: "Security attestation report for TechCorp customer including control evidence and compliance status",
    reportType: "stakeholder",
    framework: "ISO 27001",
    generationDate: new Date("2024-01-25T10:45:00Z"),
    status: "generated",
    targetAudience: "customer",
    dataFreshness: new Date("2024-01-24T23:59:59Z"),
    category: "stakeholder-reports",
    createdBy: "<EMAIL>",
    fileSize: 1876543,
    fileFormat: "pdf",
    downloadUrl: "/reports/download/rpt-004",
    shareUrl: "/reports/share/rpt-004",
    isScheduled: false,
    tags: ["customer", "attestation", "ISO 27001"],
    entityId: "auris-hq",
    lastAccessed: new Date("2024-01-26T13:10:00Z"),
    accessCount: 8,
    isArchived: false
  },
  {
    id: "rpt-005",
    name: "Operational Security Metrics Q4 2024",
    description: "Quarterly operational security metrics including incident response times, control effectiveness, and trend analysis",
    reportType: "operational",
    framework: "Custom",
    generationDate: new Date("2024-01-10T09:30:00Z"),
    status: "generated",
    targetAudience: "internal",
    dataFreshness: new Date("2024-01-09T23:59:59Z"),
    category: "operational-reports",
    createdBy: "<EMAIL>",
    fileSize: 987654,
    fileFormat: "excel",
    downloadUrl: "/reports/download/rpt-005",
    isScheduled: true,
    scheduledDate: new Date("2024-04-10T09:30:00Z"),
    tags: ["operational", "metrics", "quarterly"],
    entityId: "auris-hq",
    lastAccessed: new Date("2024-01-12T11:45:00Z"),
    accessCount: 34,
    isArchived: false
  },
  {
    id: "rpt-006",
    name: "PCI DSS Compliance Assessment 2024",
    description: "Annual PCI DSS compliance assessment with detailed control testing results and remediation recommendations",
    reportType: "compliance",
    framework: "PCI DSS",
    generationDate: new Date("2024-01-05T13:20:00Z"),
    status: "in-progress",
    targetAudience: "regulator",
    dataFreshness: new Date("2024-01-04T23:59:59Z"),
    category: "compliance-reports",
    createdBy: "<EMAIL>",
    isScheduled: false,
    tags: ["PCI DSS", "annual", "assessment"],
    entityId: "auris-hq",
    accessCount: 0,
    isArchived: false
  }
]

// Sample report templates
export const sampleReportTemplates: ReportTemplate[] = [
  {
    id: "tpl-001",
    name: "SOC 2 Type II Compliance Report",
    description: "Standard SOC 2 Type II compliance report template with all required sections",
    reportType: "compliance",
    framework: "SOC 2",
    targetAudience: "auditor",
    category: "compliance-reports",
    sections: [
      {
        id: "sec-001",
        title: "Executive Summary",
        description: "High-level overview of compliance status",
        dataSource: "compliance-dashboard",
        visualizationType: "text",
        isRequired: true,
        order: 1
      },
      {
        id: "sec-002", 
        title: "Control Testing Results",
        description: "Detailed control testing results and evidence",
        dataSource: "control-testing",
        visualizationType: "table",
        isRequired: true,
        order: 2
      }
    ],
    isCustom: false,
    createdBy: "system",
    createdAt: new Date("2023-12-01T00:00:00Z"),
    usageCount: 45,
    tags: ["SOC 2", "compliance", "standard"]
  },
  {
    id: "tpl-002",
    name: "Executive Risk Dashboard",
    description: "Monthly executive dashboard template with key risk metrics and trends",
    reportType: "executive",
    framework: "N/A",
    targetAudience: "board",
    category: "executive-reports",
    sections: [
      {
        id: "sec-003",
        title: "Risk Overview",
        description: "Current risk posture and key metrics",
        dataSource: "risk-dashboard",
        visualizationType: "dashboard",
        isRequired: true,
        order: 1
      }
    ],
    isCustom: false,
    createdBy: "system",
    createdAt: new Date("2023-12-01T00:00:00Z"),
    usageCount: 78,
    tags: ["executive", "risk", "dashboard"]
  }
]

// Sample generation progress
export const sampleGenerationProgress: ReportGenerationProgress[] = [
  {
    id: "prog-001",
    requestId: "req-001",
    status: "processing",
    progress: 65,
    currentStep: "Generating control evidence section",
    estimatedTimeRemaining: 180,
    startedAt: new Date("2024-01-30T14:30:00Z")
  },
  {
    id: "prog-002", 
    requestId: "req-002",
    status: "completed",
    progress: 100,
    currentStep: "Report generation completed",
    startedAt: new Date("2024-01-30T13:00:00Z"),
    completedAt: new Date("2024-01-30T13:45:00Z"),
    resultUrl: "/reports/download/rpt-007"
  }
]
