"use client"

import * as React from "react"
import { Search, X, Save, Clock } from "lucide-react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface SavedSearch {
  id: string
  name: string
  query: string
  filters: any
  createdAt: Date
}

interface ArtifactsSearchProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  onSaveSearch?: (name: string) => void
  className?: string
}

// Sample saved searches
const savedSearches: SavedSearch[] = [
  {
    id: "search-1",
    name: "SOC 2 Evidence",
    query: "SOC 2",
    filters: { artifactTypes: ["audit-evidence"], frameworks: ["SOC 2"] },
    createdAt: new Date("2024-01-10")
  },
  {
    id: "search-2", 
    name: "Policy Documents",
    query: "",
    filters: { artifactTypes: ["policy-document"] },
    createdAt: new Date("2024-01-08")
  },
  {
    id: "search-3",
    name: "Pending Verification",
    query: "",
    filters: { blockchainStatus: ["pending"] },
    createdAt: new Date("2024-01-15")
  }
]

export function ArtifactsSearch({ 
  searchQuery, 
  onSearchChange, 
  onSaveSearch,
  className 
}: ArtifactsSearchProps) {
  const [showSaveDialog, setShowSaveDialog] = React.useState(false)
  const [saveName, setSaveName] = React.useState("")

  const handleSaveSearch = () => {
    if (saveName.trim() && onSaveSearch) {
      onSaveSearch(saveName.trim())
      setSaveName("")
      setShowSaveDialog(false)
    }
  }

  const handleLoadSearch = (search: SavedSearch) => {
    onSearchChange(search.query)
    // TODO: Apply filters as well
  }

  const clearSearch = () => {
    onSearchChange("")
  }

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      {/* Main Search Input */}
      <div className="relative flex-1 max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search artifacts by name, content, or tags..."
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-10 pr-10"
        />
        {searchQuery && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearSearch}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Save Search Button */}
      {searchQuery && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowSaveDialog(true)}
          className="flex items-center gap-2"
        >
          <Save className="h-4 w-4" />
          Save
        </Button>
      )}

      {/* Saved Searches Dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Saved
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-64">
          <DropdownMenuLabel>Saved Searches</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {savedSearches.length === 0 ? (
            <DropdownMenuItem disabled>
              No saved searches
            </DropdownMenuItem>
          ) : (
            savedSearches.map((search) => (
              <DropdownMenuItem
                key={search.id}
                onClick={() => handleLoadSearch(search)}
                className="flex flex-col items-start gap-1 p-3"
              >
                <div className="font-medium">{search.name}</div>
                {search.query && (
                  <div className="text-xs text-muted-foreground">
                    Query: "{search.query}"
                  </div>
                )}
                <div className="text-xs text-muted-foreground">
                  Saved {search.createdAt.toLocaleDateString()}
                </div>
              </DropdownMenuItem>
            ))
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Save Search Dialog */}
      {showSaveDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-background border rounded-lg p-6 w-96">
            <h3 className="text-lg font-semibold mb-4">Save Search</h3>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Search Name</label>
                <Input
                  value={saveName}
                  onChange={(e) => setSaveName(e.target.value)}
                  placeholder="Enter a name for this search..."
                  className="mt-1"
                />
              </div>
              <div className="text-sm text-muted-foreground">
                Current query: "{searchQuery}"
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowSaveDialog(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSaveSearch}
                  disabled={!saveName.trim()}
                >
                  Save Search
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Active Search Indicator */}
      {searchQuery && (
        <Badge variant="secondary" className="flex items-center gap-1">
          <Search className="h-3 w-3" />
          Active Search
        </Badge>
      )}
    </div>
  )
}
