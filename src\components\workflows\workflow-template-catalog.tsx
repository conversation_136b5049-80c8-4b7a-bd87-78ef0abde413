"use client"

import React from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Star, 
  Download, 
  Eye, 
  Copy, 
  Search,
  Filter,
  Play,
  BookOpen
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import { WorkflowTemplate } from "@/types/workflows"

interface WorkflowTemplateCatalogProps {
  templates: WorkflowTemplate[]
}

const categoryConfig = {
  compliance: { label: "Compliance", variant: "default" as const, color: "bg-blue-100 text-blue-800" },
  security: { label: "Security", variant: "destructive" as const, color: "bg-red-100 text-red-800" },
  risk: { label: "Risk", variant: "secondary" as const, color: "bg-yellow-100 text-yellow-800" },
  audit: { label: "<PERSON><PERSON>", variant: "outline" as const, color: "bg-purple-100 text-purple-800" },
  incident: { label: "Incident", variant: "destructive" as const, color: "bg-red-100 text-red-800" },
  remediation: { label: "Remediation", variant: "default" as const, color: "bg-green-100 text-green-800" },
  assessment: { label: "Assessment", variant: "secondary" as const, color: "bg-orange-100 text-orange-800" },
  approval: { label: "Approval", variant: "outline" as const, color: "bg-indigo-100 text-indigo-800" },
  notification: { label: "Notification", variant: "secondary" as const, color: "bg-gray-100 text-gray-800" },
  integration: { label: "Integration", variant: "outline" as const, color: "bg-teal-100 text-teal-800" },
  custom: { label: "Custom", variant: "secondary" as const, color: "bg-gray-100 text-gray-800" },
}

export function WorkflowTemplateCatalog({ templates }: WorkflowTemplateCatalogProps) {
  const [searchTerm, setSearchTerm] = React.useState("")
  const [categoryFilter, setCategoryFilter] = React.useState<string>("all")
  const [sortBy, setSortBy] = React.useState<string>("popular")

  const filteredTemplates = React.useMemo(() => {
    return templates.filter((template) => {
      const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      
      const matchesCategory = categoryFilter === "all" || template.category === categoryFilter

      return matchesSearch && matchesCategory && template.isActive
    }).sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name)
        case "rating":
          return b.rating - a.rating
        case "popular":
          return b.usageCount - a.usageCount
        case "newest":
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        case "updated":
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        default:
          return b.usageCount - a.usageCount
      }
    })
  }, [templates, searchTerm, categoryFilter, sortBy])

  const renderStarRating = (rating: number) => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-3 w-3 ${
              star <= rating ? "fill-current text-yellow-500" : "text-gray-300"
            }`}
          />
        ))}
        <span className="text-xs text-muted-foreground ml-1">({rating})</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 w-80"
            />
          </div>
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="compliance">Compliance</SelectItem>
              <SelectItem value="security">Security</SelectItem>
              <SelectItem value="risk">Risk</SelectItem>
              <SelectItem value="audit">Audit</SelectItem>
              <SelectItem value="incident">Incident</SelectItem>
              <SelectItem value="remediation">Remediation</SelectItem>
              <SelectItem value="assessment">Assessment</SelectItem>
              <SelectItem value="approval">Approval</SelectItem>
              <SelectItem value="notification">Notification</SelectItem>
              <SelectItem value="integration">Integration</SelectItem>
              <SelectItem value="custom">Custom</SelectItem>
            </SelectContent>
          </Select>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="popular">Most Popular</SelectItem>
              <SelectItem value="rating">Highest Rated</SelectItem>
              <SelectItem value="newest">Newest</SelectItem>
              <SelectItem value="updated">Recently Updated</SelectItem>
              <SelectItem value="name">Name A-Z</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button variant="outline">
          <Filter className="mr-2 h-4 w-4" />
          More Filters
        </Button>
      </div>

      {/* Results Summary */}
      <div className="text-sm text-muted-foreground">
        Showing {filteredTemplates.length} of {templates.filter(t => t.isActive).length} templates
      </div>

      {/* Template Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredTemplates.map((template) => {
          const categoryInfo = categoryConfig[template.category]
          
          return (
            <Card key={template.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <CardTitle className="text-lg">{template.name}</CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge className={categoryInfo.color}>
                        {categoryInfo.label}
                      </Badge>
                      <span className="text-xs text-muted-foreground">v{template.version}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    {renderStarRating(template.rating)}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <CardDescription className="text-sm">
                  {template.description}
                </CardDescription>

                {/* Template Stats */}
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>{template.usageCount} uses</span>
                  <span>by {template.author}</span>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-1">
                  {template.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {template.tags.length > 3 && (
                    <span className="text-xs text-muted-foreground">
                      +{template.tags.length - 3} more
                    </span>
                  )}
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2 pt-2">
                  <Button size="sm" className="flex-1">
                    <Play className="mr-2 h-3 w-3" />
                    Use Template
                  </Button>
                  <Button variant="outline" size="sm">
                    <Eye className="h-3 w-3" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Copy className="h-3 w-3" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="h-3 w-3" />
                  </Button>
                </div>

                {/* Documentation Link */}
                {template.documentation && (
                  <div className="pt-2 border-t">
                    <Button variant="ghost" size="sm" className="w-full justify-start">
                      <BookOpen className="mr-2 h-3 w-3" />
                      View Documentation
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Empty State */}
      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No templates found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your search criteria or browse all categories
          </p>
          <Button variant="outline" onClick={() => {
            setSearchTerm("")
            setCategoryFilter("all")
          }}>
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  )
}
