export type ReportType = 
  | "compliance"
  | "executive" 
  | "operational"
  | "stakeholder"
  | "custom"

export type ReportFramework = 
  | "NIST CSF 2.0"
  | "ISO 27001"
  | "PCI DSS"
  | "SOC 2"
  | "HIPAA"
  | "Custom"
  | "N/A"

export type ReportStatus = 
  | "generated"
  | "in-progress"
  | "failed"
  | "scheduled"

export type TargetAudience = 
  | "internal"
  | "auditor"
  | "customer"
  | "regulator"
  | "board"

export type ReportFormat = "pdf" | "html" | "excel" | "csv"

export type ReportCategory = 
  | "compliance-reports"
  | "executive-reports"
  | "operational-reports"
  | "stakeholder-reports"

export interface Report {
  id: string
  name: string
  description?: string
  reportType: ReportType
  framework: ReportFramework
  generationDate: Date
  status: ReportStatus
  targetAudience: TargetAudience
  dataFreshness: Date
  category: ReportCategory
  createdBy: string
  fileSize?: number
  fileFormat?: ReportFormat
  downloadUrl?: string
  shareUrl?: string
  isScheduled: boolean
  scheduledDate?: Date
  tags: string[]
  entityId: string
  lastAccessed?: Date
  accessCount: number
  isArchived: boolean
  retentionDate?: Date
}

export interface ReportTemplate {
  id: string
  name: string
  description: string
  reportType: ReportType
  framework: ReportFramework
  targetAudience: TargetAudience
  category: ReportCategory
  sections: ReportSection[]
  isCustom: boolean
  createdBy: string
  createdAt: Date
  usageCount: number
  tags: string[]
}

export interface ReportSection {
  id: string
  title: string
  description?: string
  dataSource: string
  visualizationType?: "table" | "chart" | "graph" | "text" | "dashboard"
  isRequired: boolean
  order: number
  configuration?: Record<string, any>
}

export interface ReportGenerationRequest {
  templateId?: string
  name: string
  description?: string
  reportType: ReportType
  framework: ReportFramework
  targetAudience: TargetAudience
  format: ReportFormat
  dataRange: {
    startDate: Date
    endDate: Date
  }
  sections: string[]
  customSections?: ReportSection[]
  scheduledGeneration?: {
    enabled: boolean
    frequency: "daily" | "weekly" | "monthly" | "quarterly"
    nextRun?: Date
  }
  deliveryOptions?: {
    email?: string[]
    shareWithStakeholders?: boolean
    autoArchive?: boolean
  }
}

export interface ReportGenerationProgress {
  id: string
  requestId: string
  status: "queued" | "processing" | "completed" | "failed"
  progress: number
  currentStep: string
  estimatedTimeRemaining?: number
  startedAt: Date
  completedAt?: Date
  errorMessage?: string
  resultUrl?: string
}

export interface ReportAnalytics {
  reportId: string
  totalViews: number
  uniqueViewers: number
  downloadCount: number
  shareCount: number
  averageViewDuration: number
  lastViewed?: Date
  viewHistory: Array<{
    date: Date
    viewerId: string
    duration: number
    action: "view" | "download" | "share"
  }>
}

export interface ReportFilter {
  reportType?: ReportType[]
  framework?: ReportFramework[]
  status?: ReportStatus[]
  targetAudience?: TargetAudience[]
  category?: ReportCategory[]
  dateRange?: {
    startDate: Date
    endDate: Date
  }
  createdBy?: string[]
  tags?: string[]
  isArchived?: boolean
  searchQuery?: string
}

export interface ReportSortOptions {
  field: "name" | "generationDate" | "status" | "framework" | "targetAudience" | "dataFreshness"
  direction: "asc" | "desc"
}
