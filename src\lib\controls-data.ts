import { Control, ControlTemplate, ControlDashboardMetrics } from "@/types/controls"

// Sample controls data for demonstration
export const sampleControls: Control[] = [
  {
    id: "ctrl-001",
    controlId: "AC-1",
    title: "Access Control Policy and Procedures",
    description: "Develop, document, and disseminate access control policy and procedures",
    controlFamily: "access-control",
    controlType: "preventive",
    
    implementationStatus: "implemented",
    implementationDate: new Date("2023-06-15T10:00:00Z"),
    implementationNotes: "Comprehensive access control policy implemented with quarterly reviews",
    implementationGuidance: "Establish formal access control policy covering all systems and data",
    
    testingStatus: "passed",
    lastTestDate: new Date("2024-01-15T14:00:00Z"),
    nextTestDate: new Date("2024-04-15T14:00:00Z"),
    testingFrequency: "quarterly",
    testingMethod: "hybrid",
    
    frameworkMappings: [
      {
        frameworkId: "fw-001",
        frameworkName: "NIST CSF 2.0",
        controlId: "PR.AC-1",
        controlTitle: "Identities and credentials are issued, managed, verified, revoked, and audited",
        mappingStrength: 95,
        oscalCatalog: "nist-sp-800-53-rev5",
        oscalProfile: "nist-csf-2.0"
      },
      {
        frameworkId: "fw-002",
        frameworkName: "ISO 27001",
        controlId: "A.9.1.1",
        controlTitle: "Access control policy",
        mappingStrength: 90,
        oscalCatalog: "iso-27001-2022"
      }
    ],
    primaryFramework: "NIST CSF 2.0",
    
    riskLevel: "high",
    priority: 1,
    businessCriticality: "critical",
    
    ownership: {
      primaryOwner: "Security Manager",
      primaryOwnerEmail: "<EMAIL>",
      secondaryOwner: "Compliance Officer",
      secondaryOwnerEmail: "<EMAIL>",
      implementationTeam: ["<EMAIL>", "<EMAIL>"],
      testingTeam: ["<EMAIL>", "<EMAIL>"],
      businessOwner: "CISO",
      technicalOwner: "IT Security Lead",
      approver: "Chief Information Security Officer"
    },
    
    evidence: [
      {
        id: "ev-001",
        controlId: "ctrl-001",
        evidenceType: "document",
        title: "Access Control Policy Document",
        description: "Comprehensive access control policy covering all organizational systems",
        fileName: "access-control-policy-v2.1.pdf",
        fileSize: 524288,
        uploadDate: new Date("2024-01-10T09:00:00Z"),
        uploadedBy: "<EMAIL>",
        downloadUrl: "/evidence/ctrl-001/access-control-policy-v2.1.pdf",
        isRequired: true,
        verificationStatus: "verified",
        verifiedBy: "<EMAIL>",
        verificationDate: new Date("2024-01-12T11:00:00Z"),
        blockchainHash: "0x1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2b",
        blockchainBlock: "0x123456",
        verificationTimestamp: new Date("2024-01-12T11:05:00Z")
      }
    ],
    requiredEvidence: ["Policy Document", "Procedure Documentation", "Training Records"],
    documentationLinks: ["/policies/access-control", "/procedures/user-management"],
    
    testRecords: [
      {
        id: "test-001",
        controlId: "ctrl-001",
        testType: "hybrid",
        testMethod: "Document review and interview",
        testDate: new Date("2024-01-15T14:00:00Z"),
        tester: "<EMAIL>",
        testDuration: 120,
        testStatus: "passed",
        testResults: "Policy is comprehensive, current, and properly communicated",
        findings: [],
        evidence: ["ev-001"],
        nextTestDate: new Date("2024-04-15T14:00:00Z"),
        testFrequency: "quarterly",
        automationLevel: 25,
        effectivenessScore: 92,
        recommendations: ["Consider adding remote work specific guidelines"]
      }
    ],
    
    findings: [],
    remediation: [],
    
    metrics: {
      implementationRate: 100,
      testingCompletionRate: 100,
      effectivenessScore: 92,
      lastTestDate: new Date("2024-01-15T14:00:00Z"),
      nextTestDate: new Date("2024-04-15T14:00:00Z"),
      averageTestDuration: 120,
      automationLevel: 25,
      evidenceCompleteness: 100,
      findingsCount: 0,
      openFindingsCount: 0,
      remediationProgress: 100,
      costOfImplementation: 15000,
      timeToImplement: 45,
      riskReduction: 85
    },
    
    regulatoryRequirement: true,
    complianceMapping: ["SOX", "HIPAA", "PCI DSS"],
    auditTrail: [
      {
        id: "audit-001",
        controlId: "ctrl-001",
        action: "tested",
        description: "Quarterly control testing completed successfully",
        performedBy: "<EMAIL>",
        performedDate: new Date("2024-01-15T14:00:00Z"),
        details: { testResult: "passed", effectivenessScore: 92 }
      }
    ],
    
    entityId: "auris-hq",
    
    automationCapable: true,
    automationLevel: 25,
    agentMonitored: true,
    continuousMonitoring: false,
    
    dependentControls: ["ctrl-002", "ctrl-003"],
    supportingControls: ["ctrl-010", "ctrl-015"],
    relatedPolicies: ["pol-001", "pol-004"],
    relatedAssets: ["asset-001", "asset-002"],
    
    blockchainStatus: "verified",
    blockchainHash: "0x1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2b",
    blockchainBlock: "0x123456",
    verificationTimestamp: new Date("2024-01-12T11:05:00Z"),
    
    tags: ["access-control", "policy", "critical", "nist-csf"],
    customFields: {
      businessUnit: "Corporate Security",
      regulatoryDriver: "SOX Compliance",
      lastAuditDate: "2024-01-15"
    },
    isArchived: false,
    createdDate: new Date("2023-06-01T09:00:00Z"),
    lastModifiedDate: new Date("2024-01-10T09:00:00Z"),
    createdBy: "<EMAIL>",
    lastModifiedBy: "<EMAIL>"
  },
  {
    id: "ctrl-002",
    controlId: "AC-2",
    title: "Account Management",
    description: "Manage information system accounts including establishment, activation, modification, review, and removal",
    controlFamily: "access-control",
    controlType: "preventive",
    
    implementationStatus: "implemented",
    implementationDate: new Date("2023-07-20T10:00:00Z"),
    implementationNotes: "Automated account management system with approval workflows",
    implementationGuidance: "Implement comprehensive account lifecycle management",
    
    testingStatus: "passed",
    lastTestDate: new Date("2024-01-20T10:00:00Z"),
    nextTestDate: new Date("2024-04-20T10:00:00Z"),
    testingFrequency: "quarterly",
    testingMethod: "automated",
    
    frameworkMappings: [
      {
        frameworkId: "fw-001",
        frameworkName: "NIST CSF 2.0",
        controlId: "PR.AC-1",
        controlTitle: "Identities and credentials are issued, managed, verified, revoked, and audited",
        mappingStrength: 98,
        oscalCatalog: "nist-sp-800-53-rev5"
      }
    ],
    primaryFramework: "NIST CSF 2.0",
    
    riskLevel: "high",
    priority: 2,
    businessCriticality: "critical",
    
    ownership: {
      primaryOwner: "Identity Manager",
      primaryOwnerEmail: "<EMAIL>",
      implementationTeam: ["<EMAIL>"],
      testingTeam: ["<EMAIL>"],
      businessOwner: "IT Director",
      technicalOwner: "Identity Management Lead",
      approver: "CISO"
    },
    
    evidence: [],
    requiredEvidence: ["Account Management Procedures", "System Configuration", "Audit Logs"],
    documentationLinks: ["/procedures/account-management"],
    
    testRecords: [
      {
        id: "test-002",
        controlId: "ctrl-002",
        testType: "automated",
        testMethod: "Automated account review and testing",
        testDate: new Date("2024-01-20T10:00:00Z"),
        tester: "<EMAIL>",
        testDuration: 30,
        testStatus: "passed",
        testResults: "All accounts properly managed with appropriate approvals",
        findings: [],
        evidence: [],
        nextTestDate: new Date("2024-04-20T10:00:00Z"),
        testFrequency: "quarterly",
        automationLevel: 95,
        effectivenessScore: 96,
        recommendations: []
      }
    ],
    
    findings: [],
    remediation: [],
    
    metrics: {
      implementationRate: 100,
      testingCompletionRate: 100,
      effectivenessScore: 96,
      lastTestDate: new Date("2024-01-20T10:00:00Z"),
      nextTestDate: new Date("2024-04-20T10:00:00Z"),
      averageTestDuration: 30,
      automationLevel: 95,
      evidenceCompleteness: 80,
      findingsCount: 0,
      openFindingsCount: 0,
      remediationProgress: 100,
      costOfImplementation: 25000,
      timeToImplement: 60,
      riskReduction: 90
    },
    
    regulatoryRequirement: true,
    complianceMapping: ["SOX", "HIPAA", "PCI DSS"],
    auditTrail: [],
    
    entityId: "auris-hq",
    
    automationCapable: true,
    automationLevel: 95,
    agentMonitored: true,
    continuousMonitoring: true,
    
    dependentControls: ["ctrl-001"],
    supportingControls: ["ctrl-003", "ctrl-004"],
    relatedPolicies: ["pol-001", "pol-004"],
    relatedAssets: ["asset-003", "asset-004"],
    
    blockchainStatus: "verified",
    blockchainHash: "0x2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2b3c",
    blockchainBlock: "0x234567",
    verificationTimestamp: new Date("2024-01-20T10:05:00Z"),
    
    tags: ["access-control", "account-management", "automated", "critical"],
    isArchived: false,
    createdDate: new Date("2023-07-01T09:00:00Z"),
    lastModifiedDate: new Date("2024-01-15T09:00:00Z"),
    createdBy: "<EMAIL>",
    lastModifiedBy: "<EMAIL>"
  },
  {
    id: "ctrl-003",
    controlId: "IR-1",
    title: "Incident Response Policy and Procedures",
    description: "Develop, document, and disseminate incident response policy and procedures",
    controlFamily: "incident-response",
    controlType: "corrective",

    implementationStatus: "implemented",
    implementationDate: new Date("2023-08-10T10:00:00Z"),
    implementationNotes: "Comprehensive incident response plan with 24/7 response capability",
    implementationGuidance: "Establish formal incident response procedures with clear escalation paths",

    testingStatus: "passed",
    lastTestDate: new Date("2024-01-25T09:00:00Z"),
    nextTestDate: new Date("2024-07-25T09:00:00Z"),
    testingFrequency: "semi-annually",
    testingMethod: "manual",

    frameworkMappings: [
      {
        frameworkId: "fw-001",
        frameworkName: "NIST CSF 2.0",
        controlId: "RS.RP-1",
        controlTitle: "Response plan is executed during or after an incident",
        mappingStrength: 92,
        oscalCatalog: "nist-sp-800-53-rev5"
      }
    ],
    primaryFramework: "NIST CSF 2.0",

    riskLevel: "high",
    priority: 3,
    businessCriticality: "high",

    ownership: {
      primaryOwner: "Incident Response Manager",
      primaryOwnerEmail: "<EMAIL>",
      implementationTeam: ["<EMAIL>", "<EMAIL>"],
      testingTeam: ["<EMAIL>"],
      businessOwner: "CISO",
      technicalOwner: "Security Operations Lead",
      approver: "Chief Information Security Officer"
    },

    evidence: [],
    requiredEvidence: ["Incident Response Plan", "Training Records", "Tabletop Exercise Results"],
    documentationLinks: ["/procedures/incident-response"],

    testRecords: [],
    findings: [
      {
        id: "finding-001",
        severity: "medium",
        category: "operating-effectiveness",
        description: "Response time metrics not consistently meeting SLA targets",
        recommendation: "Enhance automated alerting and improve staff training",
        status: "in-progress",
        assignedTo: "<EMAIL>",
        dueDate: new Date("2024-03-15T17:00:00Z")
      }
    ],
    remediation: [
      {
        id: "rem-001",
        controlId: "ctrl-003",
        findingId: "finding-001",
        remediationType: "process-improvement",
        description: "Implement automated incident detection and response system",
        priority: "medium",
        status: "in-progress",
        assignedTo: "<EMAIL>",
        assignedDate: new Date("2024-01-26T09:00:00Z"),
        dueDate: new Date("2024-03-15T17:00:00Z"),
        estimatedEffort: 80,
        verificationRequired: true,
        businessImpact: "Improved incident response times and reduced business impact",
        cost: 12000
      }
    ],

    metrics: {
      implementationRate: 100,
      testingCompletionRate: 100,
      effectivenessScore: 78,
      lastTestDate: new Date("2024-01-25T09:00:00Z"),
      nextTestDate: new Date("2024-07-25T09:00:00Z"),
      averageTestDuration: 240,
      automationLevel: 40,
      evidenceCompleteness: 75,
      findingsCount: 1,
      openFindingsCount: 1,
      remediationProgress: 60,
      costOfImplementation: 35000,
      timeToImplement: 90,
      riskReduction: 75
    },

    regulatoryRequirement: true,
    complianceMapping: ["SOX", "HIPAA"],
    auditTrail: [],

    entityId: "auris-hq",

    automationCapable: true,
    automationLevel: 40,
    agentMonitored: true,
    continuousMonitoring: false,

    dependentControls: [],
    supportingControls: ["ctrl-001", "ctrl-004"],
    relatedPolicies: ["pol-003"],
    relatedAssets: ["asset-005", "asset-006"],

    blockchainStatus: "verified",
    blockchainHash: "0x3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2b3c4d",
    blockchainBlock: "0x345678",
    verificationTimestamp: new Date("2024-01-25T09:05:00Z"),

    tags: ["incident-response", "security", "high-priority"],
    isArchived: false,
    createdDate: new Date("2023-08-01T09:00:00Z"),
    lastModifiedDate: new Date("2024-01-25T09:00:00Z"),
    createdBy: "<EMAIL>",
    lastModifiedBy: "<EMAIL>"
  },
  {
    id: "ctrl-004",
    controlId: "CM-1",
    title: "Configuration Management Policy and Procedures",
    description: "Develop, document, and disseminate configuration management policy and procedures",
    controlFamily: "configuration-management",
    controlType: "preventive",

    implementationStatus: "partially-implemented",
    implementationDate: new Date("2023-09-15T10:00:00Z"),
    implementationNotes: "Basic configuration management in place, automation in progress",
    implementationGuidance: "Implement comprehensive configuration management with automated controls",

    testingStatus: "failed",
    lastTestDate: new Date("2024-01-30T11:00:00Z"),
    nextTestDate: new Date("2024-02-28T11:00:00Z"),
    testingFrequency: "quarterly",
    testingMethod: "automated",

    frameworkMappings: [
      {
        frameworkId: "fw-001",
        frameworkName: "NIST CSF 2.0",
        controlId: "PR.IP-1",
        controlTitle: "A baseline configuration of information technology/industrial control systems is created and maintained",
        mappingStrength: 88,
        oscalCatalog: "nist-sp-800-53-rev5"
      }
    ],
    primaryFramework: "NIST CSF 2.0",

    riskLevel: "medium",
    priority: 4,
    businessCriticality: "medium",

    ownership: {
      primaryOwner: "Configuration Manager",
      primaryOwnerEmail: "<EMAIL>",
      implementationTeam: ["<EMAIL>", "<EMAIL>"],
      testingTeam: ["<EMAIL>"],
      businessOwner: "IT Director",
      technicalOwner: "Systems Administrator",
      approver: "IT Director"
    },

    evidence: [],
    requiredEvidence: ["Configuration Baselines", "Change Management Records", "Compliance Scans"],
    documentationLinks: ["/procedures/configuration-management"],

    testRecords: [],
    findings: [
      {
        id: "finding-002",
        severity: "high",
        category: "implementation",
        description: "Configuration baselines not consistently maintained across all systems",
        recommendation: "Implement automated configuration management tools",
        status: "open",
        assignedTo: "<EMAIL>",
        dueDate: new Date("2024-02-28T17:00:00Z")
      }
    ],
    remediation: [
      {
        id: "rem-002",
        controlId: "ctrl-004",
        findingId: "finding-002",
        remediationType: "implementation",
        description: "Deploy automated configuration management system",
        priority: "high",
        status: "planned",
        assignedTo: "<EMAIL>",
        assignedDate: new Date("2024-01-31T09:00:00Z"),
        dueDate: new Date("2024-02-28T17:00:00Z"),
        estimatedEffort: 120,
        verificationRequired: true,
        businessImpact: "Improved system security and compliance posture",
        cost: 18000
      }
    ],

    metrics: {
      implementationRate: 65,
      testingCompletionRate: 100,
      effectivenessScore: 45,
      lastTestDate: new Date("2024-01-30T11:00:00Z"),
      nextTestDate: new Date("2024-02-28T11:00:00Z"),
      averageTestDuration: 90,
      automationLevel: 30,
      evidenceCompleteness: 40,
      findingsCount: 1,
      openFindingsCount: 1,
      remediationProgress: 20,
      costOfImplementation: 22000,
      timeToImplement: 120,
      riskReduction: 45
    },

    regulatoryRequirement: false,
    complianceMapping: ["SOC 2"],
    auditTrail: [],

    entityId: "auris-hq",

    automationCapable: true,
    automationLevel: 30,
    agentMonitored: false,
    continuousMonitoring: false,

    dependentControls: [],
    supportingControls: ["ctrl-001"],
    relatedPolicies: ["pol-001"],
    relatedAssets: ["asset-007", "asset-008"],

    blockchainStatus: "pending",

    tags: ["configuration-management", "automation", "medium-priority"],
    isArchived: false,
    createdDate: new Date("2023-09-01T09:00:00Z"),
    lastModifiedDate: new Date("2024-01-30T11:00:00Z"),
    createdBy: "<EMAIL>",
    lastModifiedBy: "<EMAIL>"
  }
]

// Sample control templates
export const sampleControlTemplates: ControlTemplate[] = [
  {
    id: "tpl-001",
    name: "Access Control Policy Template",
    description: "Standard template for access control policies based on NIST SP 800-53",
    controlFamily: "access-control",
    controlType: "preventive",
    templateContent: "# Access Control Policy\n\n## Purpose\n[Define the purpose of access control]\n\n## Scope\n[Define scope and applicability]...",
    implementationGuidance: "Establish formal access control policy with clear roles and responsibilities",
    testingProcedures: ["Document review", "Interview key personnel", "Sample testing"],
    requiredEvidence: ["Policy document", "Procedures", "Training records"],
    applicableFrameworks: ["NIST CSF 2.0", "ISO 27001", "SOC 2"],
    isOfficial: true,
    source: "NIST SP 800-53",
    usageCount: 25,
    createdAt: new Date("2023-01-01T00:00:00Z"),
    updatedAt: new Date("2024-01-01T00:00:00Z"),
    tags: ["access-control", "template", "nist", "policy"]
  }
]

// Controls dashboard metrics
export const controlsDashboardMetrics: ControlDashboardMetrics = {
  totalControls: sampleControls.length,
  implementedControls: sampleControls.filter(c => c.implementationStatus === "implemented").length,
  testedControls: sampleControls.filter(c => c.testingStatus === "passed" || c.testingStatus === "failed").length,
  effectiveControls: sampleControls.filter(c => c.metrics.effectivenessScore >= 80).length,
  implementationRate: (sampleControls.filter(c => c.implementationStatus === "implemented").length / sampleControls.length) * 100,
  testingCompletionRate: (sampleControls.filter(c => c.testingStatus !== "not-tested").length / sampleControls.length) * 100,
  averageEffectivenessScore: sampleControls.reduce((acc, c) => acc + c.metrics.effectivenessScore, 0) / sampleControls.length,
  openFindings: sampleControls.reduce((acc, c) => acc + c.metrics.openFindingsCount, 0),
  criticalFindings: sampleControls.reduce((acc, c) => acc + c.findings.filter(f => f.severity === "critical").length, 0),
  remediationProgress: sampleControls.reduce((acc, c) => acc + c.metrics.remediationProgress, 0) / sampleControls.length,
  automationLevel: sampleControls.reduce((acc, c) => acc + c.metrics.automationLevel, 0) / sampleControls.length,
  agentMonitoredControls: sampleControls.filter(c => c.agentMonitored).length,
  blockchainVerifiedControls: sampleControls.filter(c => c.blockchainStatus === "verified").length
}

// Control statistics by family
export const controlsByFamily = {
  "access-control": sampleControls.filter(c => c.controlFamily === "access-control").length,
  "incident-response": sampleControls.filter(c => c.controlFamily === "incident-response").length,
  "configuration-management": sampleControls.filter(c => c.controlFamily === "configuration-management").length,
  "audit-accountability": sampleControls.filter(c => c.controlFamily === "audit-accountability").length,
  "awareness-training": sampleControls.filter(c => c.controlFamily === "awareness-training").length,
  "contingency-planning": sampleControls.filter(c => c.controlFamily === "contingency-planning").length,
  "identification-authentication": sampleControls.filter(c => c.controlFamily === "identification-authentication").length,
  "maintenance": sampleControls.filter(c => c.controlFamily === "maintenance").length,
  "media-protection": sampleControls.filter(c => c.controlFamily === "media-protection").length,
  "physical-environmental": sampleControls.filter(c => c.controlFamily === "physical-environmental").length,
  "planning": sampleControls.filter(c => c.controlFamily === "planning").length,
  "personnel-security": sampleControls.filter(c => c.controlFamily === "personnel-security").length,
  "risk-assessment": sampleControls.filter(c => c.controlFamily === "risk-assessment").length,
  "system-acquisition": sampleControls.filter(c => c.controlFamily === "system-acquisition").length,
  "system-communications": sampleControls.filter(c => c.controlFamily === "system-communications").length,
  "system-information": sampleControls.filter(c => c.controlFamily === "system-information").length,
  "program-management": sampleControls.filter(c => c.controlFamily === "program-management").length
}

// Control statistics by status
export const controlsByStatus = {
  "not-implemented": sampleControls.filter(c => c.implementationStatus === "not-implemented").length,
  "partially-implemented": sampleControls.filter(c => c.implementationStatus === "partially-implemented").length,
  "implemented": sampleControls.filter(c => c.implementationStatus === "implemented").length,
  "not-applicable": sampleControls.filter(c => c.implementationStatus === "not-applicable").length,
  "inherited": sampleControls.filter(c => c.implementationStatus === "inherited").length
}
