"use client"

import React from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { TrendingUp, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, BarChart3 } from "lucide-react"

export function IncidentPatternAnalysis() {
  return (
    <div className="space-y-6">
      {/* Pattern Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Trend Analysis
            </CardTitle>
            <CardDescription>Incident volume trends</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">-15%</div>
            <p className="text-xs text-muted-foreground">
              Decrease from last quarter
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Peak Hours
            </CardTitle>
            <CardDescription>Most active time periods</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2-4 PM</div>
            <p className="text-xs text-muted-foreground">
              Weekday peak activity
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Seasonal Patterns
            </CardTitle>
            <CardDescription>Quarterly variations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Q4</div>
            <p className="text-xs text-muted-foreground">
              Highest incident volume
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Predictability
            </CardTitle>
            <CardDescription>Pattern confidence</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">78%</div>
            <p className="text-xs text-muted-foreground">
              Prediction accuracy
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Incident Categories */}
      <Card>
        <CardHeader>
          <CardTitle>Incident Category Trends</CardTitle>
          <CardDescription>Distribution and trends by incident type</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { category: "Phishing", count: 45, trend: "+12%", severity: "Medium", color: "bg-orange-500" },
              { category: "Malware", count: 23, trend: "-8%", severity: "High", color: "bg-red-500" },
              { category: "Data Breach", count: 8, trend: "+25%", severity: "Critical", color: "bg-red-600" },
              { category: "DDoS", count: 12, trend: "-15%", severity: "High", color: "bg-purple-500" },
              { category: "Insider Threat", count: 6, trend: "+50%", severity: "High", color: "bg-yellow-500" },
              { category: "System Outage", count: 34, trend: "-5%", severity: "Medium", color: "bg-blue-500" },
            ].map((item) => (
              <div key={item.category} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-4 h-4 rounded-full ${item.color}`} />
                  <div className="space-y-1">
                    <h4 className="font-medium">{item.category}</h4>
                    <p className="text-sm text-muted-foreground">{item.count} incidents this quarter</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={item.severity === "Critical" ? "destructive" : 
                                item.severity === "High" ? "default" : "secondary"}>
                    {item.severity}
                  </Badge>
                  <Badge variant={item.trend.startsWith("+") ? "destructive" : "outline"} 
                         className={item.trend.startsWith("+") ? "text-red-600" : "text-green-600"}>
                    {item.trend}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Time-based Patterns */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Daily Patterns</CardTitle>
            <CardDescription>Incident distribution by hour</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { time: "00:00-06:00", incidents: 8, percentage: 12 },
                { time: "06:00-12:00", incidents: 25, percentage: 38 },
                { time: "12:00-18:00", incidents: 28, percentage: 42 },
                { time: "18:00-24:00", incidents: 5, percentage: 8 },
              ].map((period) => (
                <div key={period.time} className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>{period.time}</span>
                    <span className="font-medium">{period.incidents} incidents</span>
                  </div>
                  <Progress value={period.percentage} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Weekly Patterns</CardTitle>
            <CardDescription>Incident distribution by day</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { day: "Monday", incidents: 18, percentage: 22 },
                { day: "Tuesday", incidents: 22, percentage: 27 },
                { day: "Wednesday", incidents: 20, percentage: 24 },
                { day: "Thursday", incidents: 15, percentage: 18 },
                { day: "Friday", incidents: 12, percentage: 15 },
                { day: "Weekend", incidents: 8, percentage: 10 },
              ].map((day) => (
                <div key={day.day} className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>{day.day}</span>
                    <span className="font-medium">{day.incidents} incidents</span>
                  </div>
                  <Progress value={day.percentage} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Predictive Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Predictive Insights</CardTitle>
          <CardDescription>AI-powered incident forecasting and recommendations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <h4 className="text-sm font-medium">Next Week Forecast</h4>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Expected Incidents</span>
                    <span className="font-medium">18-22</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Confidence Level</span>
                    <span className="font-medium">85%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Peak Day</span>
                    <span className="font-medium">Tuesday</span>
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                <h4 className="text-sm font-medium">Risk Factors</h4>
                <div className="space-y-2">
                  {[
                    { factor: "Holiday Weekend", risk: "High" },
                    { factor: "System Maintenance", risk: "Medium" },
                    { factor: "New Software Deployment", risk: "Medium" },
                  ].map((risk) => (
                    <div key={risk.factor} className="flex justify-between text-sm">
                      <span>{risk.factor}</span>
                      <Badge variant={risk.risk === "High" ? "destructive" : "secondary"}>
                        {risk.risk}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            <div className="pt-4 border-t">
              <h4 className="text-sm font-medium mb-3">Recommendations</h4>
              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <div className="w-2 h-2 rounded-full bg-blue-500 mt-2" />
                  <p className="text-sm text-muted-foreground">
                    Increase staffing on Tuesday afternoon (2-4 PM) based on predicted peak activity
                  </p>
                </div>
                <div className="flex items-start gap-2">
                  <div className="w-2 h-2 rounded-full bg-orange-500 mt-2" />
                  <p className="text-sm text-muted-foreground">
                    Prepare additional phishing response resources due to 25% increase trend
                  </p>
                </div>
                <div className="flex items-start gap-2">
                  <div className="w-2 h-2 rounded-full bg-green-500 mt-2" />
                  <p className="text-sm text-muted-foreground">
                    Schedule preventive security awareness training to reduce insider threat incidents
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
