"use client"

import * as React from "react"
import { createPortal } from "react-dom"
import { motion, AnimatePresence } from "framer-motion"
import { ShineBorder } from "@/components/magicui/shine-border"

interface Toast {
  id: string
  message: string
  duration?: number
}

interface ToastContextType {
  showToast: (message: string, duration?: number) => void
}

const ToastContext = React.createContext<ToastContextType | undefined>(undefined)

export function useToast() {
  const context = React.useContext(ToastContext)
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider")
  }
  return context
}

interface ToastItemProps {
  toast: Toast
  onRemove: (id: string) => void
  index: number
}

function ToastItem({ toast, onRemove, index }: ToastItemProps) {
  React.useEffect(() => {
    const timer = setTimeout(() => {
      onRemove(toast.id)
    }, toast.duration || 1500)

    return () => clearTimeout(timer)
  }, [toast.id, toast.duration, onRemove])

  return (
    <motion.div
      initial={{ y: -60, opacity: 0, scale: 0.9 }}
      animate={{
        y: 0,
        opacity: 1,
        scale: 1,
        transition: {
          type: "spring",
          damping: 15,
          stiffness: 400,
          duration: 0.5
        }
      }}
      exit={{
        y: -30,
        opacity: 0,
        scale: 0.9,
        transition: {
          duration: 0.2,
          ease: "easeIn"
        }
      }}
      style={{
        zIndex: 9999 - index
      }}
      className="relative"
    >
      <div className="relative bg-background rounded-md shadow-xl px-3 py-2 text-sm font-medium text-foreground border border-border/20">
        <ShineBorder
          className="absolute inset-0 rounded-md"
          borderWidth={2}
          duration={3}
          shineColor={[
            "#ff6b6b", // Red
            "#4ecdc4", // Teal
            "#45b7d1", // Blue
            "#96ceb4", // Green
            "#feca57", // Yellow
            "#ff9ff3", // Pink
            "#54a0ff", // Light Blue
            "#5f27cd"  // Purple
          ]}
        />
        <div className="relative z-10 text-center min-w-[60px]">
          {toast.message}
        </div>
      </div>
    </motion.div>
  )
}

interface ToastProviderProps {
  children: React.ReactNode
}

export function ToastProvider({ children }: ToastProviderProps) {
  const [toasts, setToasts] = React.useState<Toast[]>([])
  const [mounted, setMounted] = React.useState(false)

  React.useEffect(() => {
    setMounted(true)
  }, [])

  const showToast = React.useCallback((message: string, duration = 1500) => {
    const id = Math.random().toString(36).substr(2, 9)
    const newToast: Toast = { id, message, duration }
    
    setToasts(prev => [...prev, newToast])
  }, [])

  const removeToast = React.useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }, [])

  const contextValue = React.useMemo(() => ({
    showToast
  }), [showToast])

  if (!mounted) {
    return <ToastContext.Provider value={contextValue}>{children}</ToastContext.Provider>
  }

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
      {createPortal(
        <div className="fixed top-0 left-0 right-0 z-[9999] flex justify-center pt-14">
          <div className="flex flex-col items-center space-y-1">
            <AnimatePresence mode="popLayout">
              {toasts.map((toast, index) => (
                <ToastItem
                  key={toast.id}
                  toast={toast}
                  onRemove={removeToast}
                  index={index}
                />
              ))}
            </AnimatePresence>
          </div>
        </div>,
        document.body
      )}
    </ToastContext.Provider>
  )
}
