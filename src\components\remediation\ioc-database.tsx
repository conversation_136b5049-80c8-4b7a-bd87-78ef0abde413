"use client"

import React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Shield, Search, Filter, AlertTriangle, CheckCircle, XCircle } from "lucide-react"
import { IOC } from "@/types/remediation"

interface IOCDatabaseProps {
  iocs: IOC[]
}

const severityConfig = {
  info: { label: "Info", variant: "outline" as const },
  low: { label: "Low", variant: "secondary" as const },
  medium: { label: "Medium", variant: "default" as const },
  high: { label: "High", variant: "default" as const },
  critical: { label: "Critical", variant: "destructive" as const },
}

const typeConfig = {
  ip: { label: "IP Address", icon: "🌐" },
  domain: { label: "Domain", icon: "🌍" },
  url: { label: "URL", icon: "🔗" },
  hash: { label: "File Hash", icon: "🔒" },
  email: { label: "Email", icon: "📧" },
  "file-path": { label: "File Path", icon: "📁" },
  "registry-key": { label: "Registry Key", icon: "🔑" },
  "user-agent": { label: "User Agent", icon: "🖥️" },
  certificate: { label: "Certificate", icon: "📜" },
  mutex: { label: "Mutex", icon: "🔐" },
}

export function IOCDatabase({ iocs }: IOCDatabaseProps) {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Indicators of Compromise (IOC) Database
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input placeholder="Search IOCs..." className="pl-8 w-64" />
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
              <Button variant="outline" size="sm">
                <AlertTriangle className="mr-2 h-4 w-4" />
                Threat Intel
              </Button>
            </div>
          </div>
          
          <div className="space-y-4">
            {iocs.map((ioc) => {
              const severityInfo = severityConfig[ioc.severity]
              const typeInfo = typeConfig[ioc.type]
              
              return (
                <div key={ioc.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="text-lg">{typeInfo.icon}</span>
                      <h4 className="font-medium font-mono">{ioc.value}</h4>
                      <Badge variant={severityInfo.variant}>
                        {severityInfo.label}
                      </Badge>
                      <Badge variant="outline">{typeInfo.label}</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{ioc.description}</p>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>Confidence: {ioc.confidence}</span>
                      <span>Source: {ioc.source}</span>
                      <span>First seen: {new Date(ioc.firstSeen).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {ioc.blocked && (
                        <Badge variant="outline" className="text-green-600">
                          <CheckCircle className="mr-1 h-3 w-3" />
                          Blocked
                        </Badge>
                      )}
                      {ioc.falsePositive && (
                        <Badge variant="outline" className="text-yellow-600">
                          <XCircle className="mr-1 h-3 w-3" />
                          False Positive
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      {ioc.blocked ? "Unblock" : "Block"}
                    </Button>
                    <Button variant="outline" size="sm">View Details</Button>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
