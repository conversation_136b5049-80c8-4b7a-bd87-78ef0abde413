"use client"

import React from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Filter, Download } from "lucide-react"
import { Investigation } from "@/types/remediation"

interface InvestigationTableProps {
  investigations: Investigation[]
}

export function InvestigationTable({ investigations }: InvestigationTableProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input placeholder="Search investigations..." className="pl-8 w-64" />
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>
      
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            {investigations.map((investigation) => (
              <div key={investigation.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{investigation.title}</h4>
                    <Badge variant={investigation.priority === "critical" ? "destructive" : "default"}>
                      {investigation.priority}
                    </Badge>
                    <Badge variant="outline">{investigation.status}</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{investigation.description}</p>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span>ID: {investigation.id}</span>
                    <span>Investigator: {investigation.investigator}</span>
                    <span>Created: {new Date(investigation.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>
                <Button variant="outline" size="sm">View Details</Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
