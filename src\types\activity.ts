// GRCOS Activity Module - TypeScript Interfaces
// Comprehensive type definitions for personal task management, notifications, approvals, and collaboration

export type TaskStatus = "pending" | "in-progress" | "completed" | "overdue" | "cancelled"
export type TaskPriority = "low" | "medium" | "high" | "critical"
export type TaskCategory = "compliance" | "security" | "assessment" | "remediation" | "review" | "approval" | "investigation"
export type NotificationType = "task-assignment" | "deadline-reminder" | "approval-request" | "escalation" | "system-alert" | "regulatory-deadline" | "collaboration-request"
export type NotificationPriority = "low" | "medium" | "high" | "urgent"
export type ApprovalStatus = "pending" | "approved" | "rejected" | "delegated" | "expired"
export type ApprovalType = "policy" | "control" | "assessment" | "remediation" | "workflow" | "exception" | "risk-acceptance"

// Core Task Management
export interface Task {
  id: string
  title: string
  description: string
  category: TaskCategory
  priority: TaskPriority
  status: TaskStatus
  assignedTo: string
  assignedBy: string
  createdAt: Date
  updatedAt: Date
  dueDate: Date
  completedAt?: Date
  estimatedHours: number
  actualHours?: number
  progress: number // 0-100
  tags: string[]
  
  // Module Integration
  sourceModule: "remediation" | "workflows" | "policies" | "controls" | "assessments" | "frameworks" | "assets" | "reports" | "artifacts"
  sourceEntityId: string
  sourceEntityType: string
  
  // Dependencies and Relationships
  dependencies: string[] // Task IDs this task depends on
  blockedBy: string[] // Task IDs blocking this task
  relatedTasks: string[]
  
  // Collaboration
  watchers: string[]
  comments: TaskComment[]
  attachments: TaskAttachment[]
  
  // Compliance and Audit
  complianceFramework?: string
  regulatoryDeadline?: Date
  auditTrail: TaskAuditEntry[]
}

export interface TaskComment {
  id: string
  taskId: string
  author: string
  content: string
  createdAt: Date
  isInternal: boolean
  mentions: string[]
}

export interface TaskAttachment {
  id: string
  taskId: string
  fileName: string
  fileSize: number
  fileType: string
  uploadedBy: string
  uploadedAt: Date
  downloadUrl: string
}

export interface TaskAuditEntry {
  id: string
  taskId: string
  action: "created" | "updated" | "assigned" | "completed" | "cancelled" | "commented" | "status-changed"
  actor: string
  timestamp: Date
  details: Record<string, any>
  previousValues?: Record<string, any>
}

// Notification System
export interface Notification {
  id: string
  type: NotificationType
  priority: NotificationPriority
  title: string
  message: string
  recipient: string
  sender?: string
  createdAt: Date
  readAt?: Date
  dismissedAt?: Date
  actionRequired: boolean
  actionUrl?: string
  actionLabel?: string
  
  // Categorization and Filtering
  category: string
  tags: string[]
  sourceModule: string
  sourceEntityId?: string
  
  // Escalation and Reminders
  escalationLevel: number
  reminderCount: number
  nextReminderAt?: Date
  escalationChain: string[]
  
  // Regulatory and Compliance
  regulatoryFramework?: string
  complianceDeadline?: Date
  riskLevel?: "low" | "medium" | "high" | "critical"
}

export interface NotificationPreferences {
  userId: string
  emailNotifications: boolean
  pushNotifications: boolean
  smsNotifications: boolean
  
  // Notification Type Preferences
  typePreferences: Record<NotificationType, {
    enabled: boolean
    immediateAlert: boolean
    digestFrequency: "real-time" | "hourly" | "daily" | "weekly"
    escalationEnabled: boolean
  }>
  
  // Filtering Preferences
  priorityFilter: NotificationPriority[]
  categoryFilter: string[]
  moduleFilter: string[]
  
  // Quiet Hours
  quietHours: {
    enabled: boolean
    startTime: string // HH:MM format
    endTime: string
    timezone: string
    weekendsIncluded: boolean
  }
}

// Approval and Review Workflows
export interface Approval {
  id: string
  type: ApprovalType
  title: string
  description: string
  status: ApprovalStatus
  priority: TaskPriority
  
  // Request Details
  requestedBy: string
  requestedAt: Date
  requiredBy: Date
  
  // Approval Chain
  approvers: ApprovalStep[]
  currentStep: number
  
  // Content and Context
  sourceModule: string
  sourceEntityId: string
  sourceEntityType: string
  attachments: ApprovalAttachment[]
  
  // Decision Tracking
  finalDecision?: "approved" | "rejected"
  finalDecisionBy?: string
  finalDecisionAt?: Date
  finalDecisionReason?: string
  
  // Audit and Compliance
  complianceFramework?: string
  riskAssessment?: string
  auditTrail: ApprovalAuditEntry[]
}

export interface ApprovalStep {
  id: string
  approvalId: string
  stepNumber: number
  approver: string
  status: "pending" | "approved" | "rejected" | "delegated" | "skipped"
  requiredBy: Date
  
  // Decision Details
  decidedAt?: Date
  decision?: "approved" | "rejected" | "delegated"
  reason?: string
  conditions?: string[]
  
  // Delegation
  delegatedTo?: string
  delegatedAt?: Date
  delegationReason?: string
  
  // Escalation
  escalated: boolean
  escalatedAt?: Date
  escalatedTo?: string
}

export interface ApprovalAttachment {
  id: string
  approvalId: string
  fileName: string
  fileSize: number
  fileType: string
  uploadedBy: string
  uploadedAt: Date
  downloadUrl: string
  isConfidential: boolean
}

export interface ApprovalAuditEntry {
  id: string
  approvalId: string
  action: "created" | "approved" | "rejected" | "delegated" | "escalated" | "modified" | "cancelled"
  actor: string
  timestamp: Date
  details: Record<string, any>
  ipAddress?: string
  userAgent?: string
}

// Performance and Analytics
export interface ActivityMetrics {
  userId: string
  period: "daily" | "weekly" | "monthly" | "quarterly"
  startDate: Date
  endDate: Date
  
  // Task Performance
  tasksAssigned: number
  tasksCompleted: number
  tasksOverdue: number
  averageCompletionTime: number // hours
  completionRate: number // percentage
  
  // Workload Analysis
  totalHoursWorked: number
  averageHoursPerTask: number
  peakWorkloadDay: string
  workloadDistribution: Record<TaskCategory, number>
  
  // Approval Performance
  approvalsReceived: number
  approvalsCompleted: number
  averageApprovalTime: number // hours
  approvalRate: number // percentage
  
  // Collaboration Metrics
  commentsPosted: number
  collaborationRequests: number
  knowledgeSharing: number
  mentoringSessions: number
  
  // Quality Metrics
  reworkRate: number // percentage
  qualityScore: number // 1-10
  stakeholderSatisfaction: number // 1-10
  complianceAdherence: number // percentage
}

export interface TeamPerformance {
  teamId: string
  teamName: string
  period: "daily" | "weekly" | "monthly" | "quarterly"
  startDate: Date
  endDate: Date
  
  // Team Metrics
  totalMembers: number
  activeMembers: number
  teamEfficiency: number // percentage
  collaborationIndex: number // 1-10
  
  // Workload Distribution
  workloadBalance: number // 1-10 (10 = perfectly balanced)
  overloadedMembers: string[]
  underutilizedMembers: string[]
  
  // Performance Trends
  productivityTrend: "increasing" | "stable" | "decreasing"
  qualityTrend: "improving" | "stable" | "declining"
  satisfactionTrend: "improving" | "stable" | "declining"
  
  // Individual Performance
  memberMetrics: Record<string, ActivityMetrics>
  topPerformers: string[]
  improvementNeeded: string[]
}

// Communication and Collaboration
export interface CommunicationThread {
  id: string
  title: string
  type: "discussion" | "announcement" | "question" | "collaboration" | "escalation"
  priority: "low" | "medium" | "high" | "urgent"
  
  // Participants
  initiator: string
  participants: string[]
  watchers: string[]
  
  // Content and Context
  sourceModule?: string
  sourceEntityId?: string
  relatedTasks: string[]
  relatedApprovals: string[]
  
  // Thread Management
  status: "active" | "resolved" | "archived" | "escalated"
  createdAt: Date
  lastActivity: Date
  resolvedAt?: Date
  resolvedBy?: string
  
  // Messages
  messages: CommunicationMessage[]
  
  // Metadata
  tags: string[]
  isConfidential: boolean
  requiresApproval: boolean
}

export interface CommunicationMessage {
  id: string
  threadId: string
  author: string
  content: string
  messageType: "text" | "file" | "link" | "mention" | "system"
  createdAt: Date
  editedAt?: Date
  
  // Rich Content
  attachments: MessageAttachment[]
  mentions: string[]
  reactions: MessageReaction[]
  
  // Threading
  replyTo?: string
  replies: string[]
  
  // Status
  isRead: Record<string, Date> // userId -> readAt
  isImportant: boolean
  isPinned: boolean
}

export interface MessageAttachment {
  id: string
  messageId: string
  fileName: string
  fileSize: number
  fileType: string
  downloadUrl: string
  thumbnailUrl?: string
}

export interface MessageReaction {
  emoji: string
  users: string[]
  count: number
}

// Knowledge Base and Context
export interface KnowledgeEntry {
  id: string
  title: string
  content: string
  type: "procedure" | "guideline" | "faq" | "best-practice" | "lesson-learned" | "template"
  category: string
  tags: string[]
  
  // Authorship and Maintenance
  author: string
  createdAt: Date
  lastUpdated: Date
  lastReviewed: Date
  nextReview: Date
  reviewers: string[]
  
  // Usage and Relevance
  viewCount: number
  usefulness: number // 1-10 average rating
  relatedEntries: string[]
  relatedTasks: string[]
  
  // Access Control
  accessLevel: "public" | "internal" | "restricted" | "confidential"
  authorizedUsers: string[]
  authorizedRoles: string[]
  
  // Compliance and Audit
  complianceRelevant: boolean
  auditEvidence: boolean
  retentionPeriod: number // years
  lastAudit: Date
}

// Dashboard and Summary Interfaces
export interface ActivityDashboard {
  userId: string
  generatedAt: Date
  
  // Quick Stats
  pendingTasks: number
  overdueItems: number
  upcomingDeadlinesCount: number
  unreadNotifications: number
  pendingApprovals: number

  // Recent Activity
  recentTasks: Task[]
  recentNotifications: Notification[]
  recentApprovals: Approval[]
  recentCommunications: CommunicationThread[]

  // Performance Summary
  weeklyMetrics: ActivityMetrics
  monthlyTrends: {
    productivity: number
    quality: number
    collaboration: number
    satisfaction: number
  }

  // Upcoming Items
  upcomingDeadlines: {
    task: Task
    daysUntilDue: number
  }[]
  
  // Recommendations
  recommendations: {
    type: "workload" | "skill" | "collaboration" | "efficiency"
    title: string
    description: string
    actionUrl?: string
  }[]
}
