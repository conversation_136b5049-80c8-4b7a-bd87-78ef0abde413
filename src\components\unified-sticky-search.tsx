"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import { Search, Filter, SortAsc, X, Plus } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface UnifiedStickySearchProps {
  className?: string
  customConfig?: {
    placeholder: string
    actionButton?: React.ReactNode
    filterOptions: Array<{ label: string; value: string }>
    sortOptions: Array<{ label: string; value: string }>
  }
}

export function UnifiedStickySearch({ className, customConfig }: UnifiedStickySearchProps) {
  const pathname = usePathname()
  const [searchQuery, setSearchQuery] = React.useState("")
  const [activeFilters, setActiveFilters] = React.useState<string[]>([])
  const [currentSort, setCurrentSort] = React.useState("")

  // Get module configuration based on pathname or use custom config
  const getModuleConfig = React.useMemo(() => {
    if (customConfig) {
      return customConfig
    }

    if (pathname.startsWith("/reports")) {
      return {
        placeholder: "Search reports by name, framework, or audience...",
        actionButton: (
          <Button asChild>
            <Link href="/reports/generate">
              <Plus className="mr-2 h-4 w-4" />
              Create Report
            </Link>
          </Button>
        ),
        filterOptions: [
          { label: "Generated", value: "generated" },
          { label: "In Progress", value: "in-progress" },
          { label: "Failed", value: "failed" },
          { label: "Scheduled", value: "scheduled" },
          { label: "Compliance Report", value: "compliance" },
          { label: "Executive Summary", value: "executive" },
          { label: "Audit Report", value: "audit" },
          { label: "Risk Assessment", value: "risk" },
          { label: "NIST CSF 2.0", value: "NIST CSF 2.0" },
          { label: "ISO 27001", value: "ISO 27001" },
          { label: "SOC 2", value: "SOC 2" },
          { label: "PCI DSS", value: "PCI DSS" },
          { label: "HIPAA", value: "HIPAA" },
        ],
        sortOptions: [
          { label: "Name (A-Z)", value: "name-asc" },
          { label: "Name (Z-A)", value: "name-desc" },
          { label: "Date (Newest)", value: "date-desc" },
          { label: "Date (Oldest)", value: "date-asc" },
          { label: "Status", value: "status" },
          { label: "Framework", value: "framework" },
        ]
      }
    }

    if (pathname.startsWith("/frameworks")) {
      return {
        placeholder: "Search frameworks by name, standard, or type...",
        actionButton: (
          <Button asChild>
            <Link href="/frameworks/configure">
              <Plus className="mr-2 h-4 w-4" />
              Add Framework
            </Link>
          </Button>
        ),
        filterOptions: [
          { label: "Not Started", value: "not-started" },
          { label: "In Progress", value: "in-progress" },
          { label: "Implemented", value: "implemented" },
          { label: "Certified", value: "certified" },
          { label: "Maintenance", value: "maintenance" },
          { label: "Low Risk", value: "low" },
          { label: "Medium Risk", value: "medium" },
          { label: "High Risk", value: "high" },
          { label: "Critical Risk", value: "critical" },
          { label: "NIST CSF 2.0", value: "NIST CSF 2.0" },
          { label: "ISO 27001", value: "ISO 27001" },
          { label: "SOC 2", value: "SOC 2" },
          { label: "PCI DSS", value: "PCI DSS" },
          { label: "HIPAA", value: "HIPAA" },
        ],
        sortOptions: [
          { label: "Name (A-Z)", value: "name-asc" },
          { label: "Name (Z-A)", value: "name-desc" },
          { label: "Compliance (High-Low)", value: "compliance-desc" },
          { label: "Compliance (Low-High)", value: "compliance-asc" },
          { label: "Gap Count (High-Low)", value: "gaps-desc" },
          { label: "Gap Count (Low-High)", value: "gaps-asc" },
          { label: "Risk Level", value: "risk" },
          { label: "Implementation Status", value: "status" },
        ]
      }
    }

    if (pathname.startsWith("/artifacts")) {
      return {
        placeholder: "Search artifacts by name, description, tags, or creator...",
        actionButton: null,
        filterOptions: [
          { label: "Policy Document", value: "policy" },
          { label: "Evidence", value: "evidence" },
          { label: "Report", value: "report" },
          { label: "Certificate", value: "certificate" },
          { label: "Assessment", value: "assessment" },
          { label: "Verified", value: "verified" },
          { label: "Pending", value: "pending" },
          { label: "Failed", value: "failed" },
          { label: "Public", value: "public" },
          { label: "Internal", value: "internal" },
          { label: "Confidential", value: "confidential" },
          { label: "Restricted", value: "restricted" },
        ],
        sortOptions: [
          { label: "Name (A-Z)", value: "name-asc" },
          { label: "Name (Z-A)", value: "name-desc" },
          { label: "Date (Newest)", value: "date-desc" },
          { label: "Date (Oldest)", value: "date-asc" },
          { label: "Type", value: "type" },
          { label: "Status", value: "status" },
          { label: "Access Level", value: "access" },
        ]
      }
    }

    if (pathname.startsWith("/portals")) {
      return {
        placeholder: "Search portals by name or description...",
        actionButton: (
          <Button asChild>
            <Link href="/portals/configure/new">
              <Plus className="h-4 w-4 mr-2" />
              Create Portal
            </Link>
          </Button>
        ),
        filterOptions: [
          { label: "Active", value: "active" },
          { label: "Draft", value: "draft" },
          { label: "Suspended", value: "suspended" },
          { label: "Expired", value: "expired" },
          { label: "Auditor", value: "auditor" },
          { label: "Customer", value: "customer" },
          { label: "Vendor", value: "vendor" },
          { label: "Regulator", value: "regulator" },
          { label: "Complete", value: "complete" },
          { label: "Needs Setup", value: "needs-setup" },
          { label: "Warning", value: "warning" },
        ],
        sortOptions: [
          { label: "Name (A-Z)", value: "name-asc" },
          { label: "Name (Z-A)", value: "name-desc" },
          { label: "Created (Newest)", value: "created-desc" },
          { label: "Created (Oldest)", value: "created-asc" },
          { label: "Last Activity", value: "activity" },
          { label: "Monthly Views", value: "views" },
          { label: "Status", value: "status" },
        ]
      }
    }

    if (pathname.startsWith("/policies")) {
      return {
        placeholder: "Search policies, violations, and exceptions...",
        actionButton: (
          <Button asChild>
            <Link href="/policies/create">
              <Plus className="mr-2 h-4 w-4" />
              Create Policy
            </Link>
          </Button>
        ),
        filterOptions: [
          { label: "Active", value: "active" },
          { label: "Draft", value: "draft" },
          { label: "Under Review", value: "review" },
          { label: "Archived", value: "archived" },
          { label: "High Priority", value: "high" },
          { label: "Medium Priority", value: "medium" },
          { label: "Low Priority", value: "low" },
        ],
        sortOptions: [
          { label: "Name (A-Z)", value: "name-asc" },
          { label: "Name (Z-A)", value: "name-desc" },
          { label: "Updated (Newest)", value: "updated-desc" },
          { label: "Updated (Oldest)", value: "updated-asc" },
          { label: "Priority", value: "priority" },
          { label: "Status", value: "status" },
        ]
      }
    }

    return null
  }, [pathname, customConfig])

  // Handle search input changes
  const handleSearchChange = React.useCallback((value: string) => {
    setSearchQuery(value)
    // TODO: Emit search event or use context to communicate with page components
  }, [])

  // Handle filter changes
  const handleFilterToggle = React.useCallback((filterValue: string) => {
    const newFilters = activeFilters.includes(filterValue)
      ? activeFilters.filter(f => f !== filterValue)
      : [...activeFilters, filterValue]
    
    setActiveFilters(newFilters)
    // TODO: Emit filter event or use context to communicate with page components
  }, [activeFilters])

  // Handle sort changes
  const handleSortChange = React.useCallback((sortValue: string) => {
    setCurrentSort(sortValue)
    // TODO: Emit sort event or use context to communicate with page components
  }, [])

  // Clear all filters
  const clearFilters = React.useCallback(() => {
    setActiveFilters([])
  }, [])

  // Clear search
  const clearSearch = React.useCallback(() => {
    setSearchQuery("")
  }, [])

  const config = getModuleConfig

  if (!config) {
    return null
  }

  return (
    <div className={cn(
      "sticky top-0 z-30 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-3",
      className
    )}>
      <div className="flex items-center gap-3">
        {/* Search Input */}
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="text"
            placeholder={config.placeholder}
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-9 pr-9 h-9"
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearSearch}
              className="absolute right-1 top-1/2 h-7 w-7 p-0 -translate-y-1/2 hover:bg-muted"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Filter Dropdown */}
        {config.filterOptions.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-9">
                <Filter className="h-4 w-4 mr-2" />
                Filters
                {activeFilters.length > 0 && (
                  <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
                    {activeFilters.length}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-56">
              <DropdownMenuLabel>Filter Options</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {config.filterOptions.map((option) => (
                <DropdownMenuItem
                  key={option.value}
                  onClick={() => handleFilterToggle(option.value)}
                  className="flex items-center justify-between"
                >
                  <span>{option.label}</span>
                  {activeFilters.includes(option.value) && (
                    <div className="h-2 w-2 rounded-full bg-primary" />
                  )}
                </DropdownMenuItem>
              ))}
              {activeFilters.length > 0 && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={clearFilters} className="text-muted-foreground">
                    Clear all filters
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* Sort Dropdown */}
        {config.sortOptions.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-9">
                <SortAsc className="h-4 w-4 mr-2" />
                Sort
                {currentSort && (
                  <Badge variant="secondary" className="ml-2 text-xs">
                    {config.sortOptions.find(s => s.value === currentSort)?.label}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-48">
              <DropdownMenuLabel>Sort Options</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {config.sortOptions.map((option) => (
                <DropdownMenuItem
                  key={option.value}
                  onClick={() => handleSortChange(option.value)}
                  className="flex items-center justify-between"
                >
                  <span>{option.label}</span>
                  {currentSort === option.value && (
                    <div className="h-2 w-2 rounded-full bg-primary" />
                  )}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* Active Filters Display */}
        {activeFilters.length > 0 && (
          <div className="flex items-center gap-2 flex-wrap">
            {activeFilters.map((filter) => {
              const filterOption = config.filterOptions.find(f => f.value === filter)
              return (
                <Badge
                  key={filter}
                  variant="secondary"
                  className="text-xs flex items-center gap-1"
                >
                  {filterOption?.label || filter}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleFilterToggle(filter)}
                    className="h-3 w-3 p-0 hover:bg-muted-foreground/20"
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              )
            })}
          </div>
        )}

        {/* Action Button */}
        {config.actionButton && (
          <div className="flex-shrink-0">
            {config.actionButton}
          </div>
        )}
      </div>
    </div>
  )
}
