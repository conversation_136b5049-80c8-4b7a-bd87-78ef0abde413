"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { navigationData } from "@/lib/navigation-data"

export function SubNavigation() {
  const pathname = usePathname()
  const router = useRouter()
  const scrollContainerRef = React.useRef<HTMLDivElement>(null)
  const [canScrollLeft, setCanScrollLeft] = React.useState(false)
  const [canScrollRight, setCanScrollRight] = React.useState(false)

  // Find the current active main item and its sub-modules
  const { activeMainItem, subModules } = React.useMemo(() => {
    for (const mainItem of navigationData.navMain) {
      if (pathname === mainItem.url || pathname.startsWith(mainItem.url + "/")) {
        if (mainItem.subModules && mainItem.subModules.length > 0) {
          return { activeMainItem: mainItem, subModules: mainItem.subModules }
        }
      }
    }
    return { activeMainItem: null, subModules: null }
  }, [pathname])

  // Check scroll state
  const checkScrollState = React.useCallback(() => {
    if (!scrollContainerRef.current) return

    const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current
    setCanScrollLeft(scrollLeft > 0)
    setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1)
  }, [])

  // Scroll functions
  const scrollLeft = () => {
    if (!scrollContainerRef.current) return
    scrollContainerRef.current.scrollBy({ left: -200, behavior: 'smooth' })
  }

  const scrollRight = () => {
    if (!scrollContainerRef.current) return
    scrollContainerRef.current.scrollBy({ left: 200, behavior: 'smooth' })
  }

  // Scroll active tab into view
  const scrollActiveIntoView = React.useCallback(() => {
    if (!scrollContainerRef.current) return

    const activeButton = scrollContainerRef.current.querySelector('[data-active="true"]') as HTMLElement
    if (activeButton) {
      const containerRect = scrollContainerRef.current.getBoundingClientRect()
      const buttonRect = activeButton.getBoundingClientRect()

      if (buttonRect.left < containerRect.left || buttonRect.right > containerRect.right) {
        activeButton.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' })
      }
    }
  }, [])

  // Keyboard navigation
  const handleKeyDown = React.useCallback((e: KeyboardEvent) => {
    if (!scrollContainerRef.current) return

    if (e.key === 'ArrowLeft') {
      e.preventDefault()
      scrollLeft()
    } else if (e.key === 'ArrowRight') {
      e.preventDefault()
      scrollRight()
    }
  }, [])

  // Effect to check scroll state and scroll active tab into view
  React.useEffect(() => {
    checkScrollState()
    scrollActiveIntoView()

    const container = scrollContainerRef.current
    if (container) {
      container.addEventListener('scroll', checkScrollState)
      container.addEventListener('keydown', handleKeyDown)
      return () => {
        container.removeEventListener('scroll', checkScrollState)
        container.removeEventListener('keydown', handleKeyDown)
      }
    }
  }, [checkScrollState, scrollActiveIntoView, handleKeyDown, subModules])

  // Don't render if no active main item or no sub-modules
  if (!activeMainItem || !subModules || subModules.length === 0) {
    return null
  }

  return (
    <div className="flex items-center w-full">
      {/* Left scroll indicator */}
      {canScrollLeft && (
        <Button
          variant="ghost"
          size="sm"
          onClick={scrollLeft}
          className="h-8 w-8 p-0 flex-shrink-0 mr-2"
          aria-label="Scroll left"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
      )}

      {/* Scrollable navigation container */}
      <div
        ref={scrollContainerRef}
        className="flex items-center gap-2 overflow-x-auto scrollbar-hide flex-1 min-w-0 focus:outline-none"
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        tabIndex={0}
        role="tablist"
        aria-label="Sub-navigation"
      >
        <nav className="flex items-center gap-2 min-w-max">
          {subModules.map((subModule) => {
            // Enhanced active state detection for nested routes
            // For asset detail pages like /assets/it/it-001, we want /assets/it to be active
            const isActive = pathname === subModule.url ||
              (subModule.url !== "/assets" && pathname.startsWith(subModule.url + "/"))

            return (
              <Button
                key={subModule.url}
                variant="ghost"
                size="sm"
                asChild
                data-active={isActive}
                className={cn(
                  "h-8 px-4 text-sm font-medium transition-all duration-200 flex-shrink-0",
                  "hover:bg-accent hover:text-accent-foreground",
                  "text-muted-foreground",
                  isActive && [
                    "border border-border bg-accent text-accent-foreground font-medium shadow-sm"
                  ]
                )}
              >
                <Link href={subModule.url}>
                  {subModule.title}
                </Link>
              </Button>
            )
          })}
        </nav>
      </div>

      {/* Right scroll indicator */}
      {canScrollRight && (
        <Button
          variant="ghost"
          size="sm"
          onClick={scrollRight}
          className="h-8 w-8 p-0 flex-shrink-0 ml-2"
          aria-label="Scroll right"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      )}
    </div>
  )
}
