"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function MonitorPage() {
  const router = useRouter()

  useEffect(() => {
    // Auto-redirect to the first sub-item (Security Dashboard) following GRCOS navigation pattern
    router.replace("/monitor/security")
  }, [router])

  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <div className="text-lg text-muted-foreground">Redirecting to Security Dashboard...</div>
      </div>
    </div>
  )
}
