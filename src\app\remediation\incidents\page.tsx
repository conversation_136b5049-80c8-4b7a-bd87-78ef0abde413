"use client"

import React from "react"
import Link from "next/link"
import { Plus, AlertTriangle, Clock, Shield, TrendingUp, Users, Activity, CheckCircle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"

import { IncidentCommandCenter } from "@/components/remediation/incident-command-center"
import { IncidentTable } from "@/components/remediation/incident-table"
import { ResponseTimelineView } from "@/components/remediation/response-timeline-view"
import { TeamResourceStatus } from "@/components/remediation/team-resource-status"
import { sampleIncidents, remediationMetrics } from "@/lib/remediation-data"

export default function RemediationIncidentsPage() {
  const [activeTab, setActiveTab] = React.useState("command-center")

  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Incident Command Center</h1>
            <p className="text-muted-foreground">
              Real-time incident monitoring, response coordination, and escalation management
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" asChild>
              <Link href="/remediation/investigation">
                <Activity className="mr-2 h-4 w-4" />
                Investigations
              </Link>
            </Button>
            <Button asChild>
              <Link href="/remediation/incidents/create">
                <Plus className="mr-2 h-4 w-4" />
                New Incident
              </Link>
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-4 mt-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Incidents</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{remediationMetrics.incidents.open + remediationMetrics.incidents.inProgress}</div>
              <p className="text-xs text-muted-foreground">
                {remediationMetrics.incidents.open} new, {remediationMetrics.incidents.inProgress} in progress
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">15m</div>
              <p className="text-xs text-muted-foreground">
                -5m from last week
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Resolution Rate</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">94.2%</div>
              <p className="text-xs text-muted-foreground">
                +2.1% from last month
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Team Workload</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{remediationMetrics.team.workload}%</div>
              <p className="text-xs text-muted-foreground">
                {remediationMetrics.team.activeAnalysts} analysts active
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="flex-1 space-y-4">
        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="command-center">Command Center</TabsTrigger>
            <TabsTrigger value="active-incidents">Active Incidents</TabsTrigger>
            <TabsTrigger value="timeline">Response Timeline</TabsTrigger>
            <TabsTrigger value="resources">Team Resources</TabsTrigger>
          </TabsList>

          <TabsContent value="command-center" className="space-y-4">
            <IncidentCommandCenter />
          </TabsContent>

          <TabsContent value="active-incidents" className="space-y-4">
            <IncidentTable 
              incidents={sampleIncidents.filter(i => i.status !== "closed")} 
            />
          </TabsContent>

          <TabsContent value="timeline" className="space-y-4">
            <ResponseTimelineView incidents={sampleIncidents} />
          </TabsContent>

          <TabsContent value="resources" className="space-y-4">
            <TeamResourceStatus />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
