"use client"

import React from "react"
import { Book<PERSON>pen, Search, Plus, Lightbulb } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { KnowledgeContextPanel } from "@/components/activity/knowledge-context-panel"

import { 
  sampleKnowledgeEntries,
  sampleTasks
} from "@/lib/activity-data"

export default function ActivityKnowledgePage() {
  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Knowledge</h1>
            <p className="text-muted-foreground">
              Knowledge context panel with contextual information, procedures, and compliance documentation
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Search className="mr-2 h-4 w-4" />
              Advanced Search
            </Button>
            <Button variant="outline" size="sm">
              <Lightbulb className="mr-2 h-4 w-4" />
              Suggest Content
            </Button>
            <Button size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Add Entry
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1">
        <KnowledgeContextPanel 
          knowledgeEntries={sampleKnowledgeEntries}
          relatedTasks={sampleTasks}
        />
      </div>
    </div>
  )
}
