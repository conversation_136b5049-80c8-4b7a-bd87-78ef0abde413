"use client"

import React from "react"
import { MessageSquare, Plus, Users } from "lucide-react"

import { Button } from "@/components/ui/button"
import { CommunicationHub } from "@/components/activity/communication-hub"

import { 
  sampleCommunicationThreads,
  sampleActivityDashboard
} from "@/lib/activity-data"

export default function ActivityCommunicationsPage() {
  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Communications</h1>
            <p className="text-muted-foreground">
              Communication hub for team collaboration, knowledge sharing, and cross-functional coordination
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Users className="mr-2 h-4 w-4" />
              Team Directory
            </Button>
            <Button size="sm">
              <Plus className="mr-2 h-4 w-4" />
              New Thread
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1">
        <CommunicationHub 
          threads={sampleCommunicationThreads}
          recentActivity={sampleActivityDashboard.recentCommunications}
        />
      </div>
    </div>
  )
}
