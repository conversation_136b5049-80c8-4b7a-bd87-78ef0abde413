"use client"

import React from "react"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Clock, TrendingUp, Shield, AlertTriangle } from "lucide-react"

export function ResponseMetricsDashboard() {
  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              MTTD
            </CardTitle>
            <CardDescription>Mean Time to Detect</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-2xl font-bold">12m</div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Target: 15m</span>
                <span className="font-medium text-green-600">-3m</span>
              </div>
              <Progress value={80} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              MTTR
            </CardTitle>
            <CardDescription>Mean Time to Resolve</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-2xl font-bold">4.2h</div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Target: 6h</span>
                <span className="font-medium text-green-600">-1.8h</span>
              </div>
              <Progress value={70} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              SLA Compliance
            </CardTitle>
            <CardDescription>Service Level Agreement</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-2xl font-bold">98.1%</div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Target: 95%</span>
                <span className="font-medium text-green-600">+3.1%</span>
              </div>
              <Progress value={98} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              False Positives
            </CardTitle>
            <CardDescription>Detection Accuracy</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-2xl font-bold">2.3%</div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Target: &lt;5%</span>
                <span className="font-medium text-green-600">-1.2%</span>
              </div>
              <Progress value={95} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Trend Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Response Time Trends</CardTitle>
          <CardDescription>Historical performance metrics over time</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <h4 className="text-sm font-medium">This Week</h4>
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>MTTD</span>
                    <span className="font-medium">11m</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>MTTR</span>
                    <span className="font-medium">3.8h</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Incidents</span>
                    <span className="font-medium">23</span>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Last Week</h4>
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>MTTD</span>
                    <span className="font-medium">14m</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>MTTR</span>
                    <span className="font-medium">4.5h</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Incidents</span>
                    <span className="font-medium">31</span>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Improvement</h4>
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>MTTD</span>
                    <Badge variant="outline" className="text-green-600">-21%</Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>MTTR</span>
                    <Badge variant="outline" className="text-green-600">-16%</Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Volume</span>
                    <Badge variant="outline" className="text-green-600">-26%</Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Severity Breakdown */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Incident Severity Distribution</CardTitle>
            <CardDescription>Breakdown by severity level</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { severity: "Critical", count: 3, percentage: 12, color: "bg-red-500" },
                { severity: "High", count: 8, percentage: 32, color: "bg-orange-500" },
                { severity: "Medium", count: 12, percentage: 48, color: "bg-yellow-500" },
                { severity: "Low", count: 2, percentage: 8, color: "bg-green-500" },
              ].map((item) => (
                <div key={item.severity} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`w-3 h-3 rounded-full ${item.color}`} />
                    <span className="text-sm">{item.severity}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-16 bg-muted rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${item.color}`}
                        style={{ width: `${item.percentage}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium w-8">{item.count}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Resolution Efficiency</CardTitle>
            <CardDescription>Time to resolution by severity</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { severity: "Critical", time: "2.1h", target: "4h", efficiency: 95 },
                { severity: "High", time: "6.3h", target: "8h", efficiency: 88 },
                { severity: "Medium", time: "18h", target: "24h", efficiency: 75 },
                { severity: "Low", time: "3.2d", target: "5d", efficiency: 68 },
              ].map((item) => (
                <div key={item.severity} className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>{item.severity}</span>
                    <span className="font-medium">{item.time} / {item.target}</span>
                  </div>
                  <Progress value={item.efficiency} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
