import { GalleryVerticalEnd } from "lucide-react"

import { LoginForm } from "@/components/login-form"
import { InteractiveGridPattern } from "@/components/magicui/interactive-grid-pattern"

export default function LoginPage() {
  return (
    <div className="relative flex min-h-svh flex-col items-center justify-center gap-6 bg-muted p-6 md:p-10 overflow-hidden">
      {/* Interactive Grid Pattern Background */}
      <InteractiveGridPattern
        className="absolute inset-0 h-full w-full"
        width={60}
        height={60}
        squares={[40, 40]}
      />

      {/* Login Content */}
      <div className="relative z-10 flex w-full max-w-sm flex-col gap-6">
        <a href="#" className="flex items-center gap-2 self-center font-medium">
          <div className="flex h-6 w-6 items-center justify-center rounded-md bg-primary text-primary-foreground">
            <GalleryVerticalEnd className="size-4" />
          </div>
          Auris Compliance
        </a>
        <LoginForm />
      </div>
    </div>
  )
}
