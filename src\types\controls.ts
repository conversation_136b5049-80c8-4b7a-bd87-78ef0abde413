export type ControlFamily = 
  | "access-control"
  | "audit-accountability"
  | "awareness-training"
  | "configuration-management"
  | "contingency-planning"
  | "identification-authentication"
  | "incident-response"
  | "maintenance"
  | "media-protection"
  | "physical-environmental"
  | "planning"
  | "personnel-security"
  | "risk-assessment"
  | "system-acquisition"
  | "system-communications"
  | "system-information"
  | "program-management"

export type ImplementationStatus = 
  | "not-implemented"
  | "partially-implemented"
  | "implemented"
  | "not-applicable"
  | "inherited"

export type TestingStatus = 
  | "not-tested"
  | "scheduled"
  | "in-progress"
  | "passed"
  | "failed"
  | "conditional-pass"
  | "not-applicable"

export type ControlType = 
  | "preventive"
  | "detective"
  | "corrective"
  | "deterrent"
  | "recovery"
  | "compensating"

export type RiskLevel = "low" | "medium" | "high" | "critical"

export type EvidenceType = 
  | "document"
  | "screenshot"
  | "configuration"
  | "log-file"
  | "interview"
  | "observation"
  | "automated-scan"
  | "penetration-test"

export type RemediationStatus = 
  | "not-required"
  | "planned"
  | "in-progress"
  | "completed"
  | "deferred"
  | "accepted-risk"

export interface ControlFrameworkMapping {
  frameworkId: string
  frameworkName: string
  controlId: string
  controlTitle: string
  mappingStrength: number
  oscalCatalog?: string
  oscalProfile?: string
}

export interface ControlEvidence {
  id: string
  controlId: string
  evidenceType: EvidenceType
  title: string
  description: string
  fileName?: string
  fileSize?: number
  uploadDate: Date
  uploadedBy: string
  downloadUrl?: string
  isRequired: boolean
  expirationDate?: Date
  verificationStatus: "pending" | "verified" | "rejected"
  verifiedBy?: string
  verificationDate?: Date
  blockchainHash?: string
  blockchainBlock?: string
  verificationTimestamp?: Date
}

export interface ControlTestRecord {
  id: string
  controlId: string
  testType: "manual" | "automated" | "hybrid"
  testMethod: string
  testDate: Date
  tester: string
  testDuration: number // minutes
  testStatus: TestingStatus
  testResults: string
  findings: ControlFinding[]
  evidence: string[]
  nextTestDate?: Date
  testFrequency: "monthly" | "quarterly" | "semi-annually" | "annually" | "ad-hoc"
  automationLevel: number // 0-100%
  effectivenessScore: number // 0-100%
  recommendations: string[]
}

export interface ControlFinding {
  id: string
  severity: "low" | "medium" | "high" | "critical"
  category: "implementation" | "design" | "operating-effectiveness"
  description: string
  recommendation: string
  status: "open" | "in-progress" | "resolved" | "accepted"
  assignedTo?: string
  dueDate?: Date
  resolutionDate?: Date
  resolutionNotes?: string
}

export interface ControlRemediation {
  id: string
  controlId: string
  findingId?: string
  remediationType: "implementation" | "enhancement" | "documentation" | "process-improvement"
  description: string
  priority: "low" | "medium" | "high" | "critical"
  status: RemediationStatus
  assignedTo: string
  assignedDate: Date
  dueDate: Date
  estimatedEffort: number // hours
  actualEffort?: number // hours
  completionDate?: Date
  verificationRequired: boolean
  verificationStatus?: "pending" | "verified" | "rejected"
  verifiedBy?: string
  verificationDate?: Date
  cost?: number
  businessImpact: string
  technicalNotes?: string
}

export interface ControlMetrics {
  implementationRate: number
  testingCompletionRate: number
  effectivenessScore: number
  lastTestDate?: Date
  nextTestDate?: Date
  averageTestDuration: number
  automationLevel: number
  evidenceCompleteness: number
  findingsCount: number
  openFindingsCount: number
  remediationProgress: number
  costOfImplementation?: number
  timeToImplement?: number // days
  riskReduction: number
}

export interface ControlOwnership {
  primaryOwner: string
  primaryOwnerEmail: string
  secondaryOwner?: string
  secondaryOwnerEmail?: string
  implementationTeam: string[]
  testingTeam: string[]
  businessOwner: string
  technicalOwner: string
  approver: string
}

export interface Control {
  id: string
  controlId: string
  title: string
  description: string
  controlFamily: ControlFamily
  controlType: ControlType
  
  // Implementation details
  implementationStatus: ImplementationStatus
  implementationDate?: Date
  implementationNotes?: string
  implementationGuidance: string
  
  // Testing information
  testingStatus: TestingStatus
  lastTestDate?: Date
  nextTestDate?: Date
  testingFrequency: "monthly" | "quarterly" | "semi-annually" | "annually" | "ad-hoc"
  testingMethod: "manual" | "automated" | "hybrid"
  
  // Framework mappings
  frameworkMappings: ControlFrameworkMapping[]
  primaryFramework: string
  
  // Risk and priority
  riskLevel: RiskLevel
  priority: number
  businessCriticality: "low" | "medium" | "high" | "critical"
  
  // Ownership and responsibility
  ownership: ControlOwnership
  
  // Evidence and documentation
  evidence: ControlEvidence[]
  requiredEvidence: string[]
  documentationLinks: string[]
  
  // Testing and assessment
  testRecords: ControlTestRecord[]
  findings: ControlFinding[]
  remediation: ControlRemediation[]
  
  // Performance metrics
  metrics: ControlMetrics
  
  // Compliance and regulatory
  regulatoryRequirement: boolean
  complianceMapping: string[]
  auditTrail: ControlAuditEntry[]
  
  // Multi-entity support
  entityId: string
  
  // Automation and orchestration
  automationCapable: boolean
  automationLevel: number // 0-100%
  agentMonitored: boolean
  continuousMonitoring: boolean
  
  // Dependencies and relationships
  dependentControls: string[]
  supportingControls: string[]
  relatedPolicies: string[]
  relatedAssets: string[]
  
  // Blockchain verification
  blockchainStatus: "verified" | "pending" | "failed" | "not-applicable"
  blockchainHash?: string
  blockchainBlock?: string
  verificationTimestamp?: Date
  
  // Metadata
  tags: string[]
  customFields?: Record<string, any>
  isArchived: boolean
  createdDate: Date
  lastModifiedDate: Date
  createdBy: string
  lastModifiedBy: string
}

export interface ControlAuditEntry {
  id: string
  controlId: string
  action: "created" | "modified" | "tested" | "evidence-added" | "finding-created" | "remediation-completed"
  description: string
  performedBy: string
  performedDate: Date
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
}

export interface ControlTemplate {
  id: string
  name: string
  description: string
  controlFamily: ControlFamily
  controlType: ControlType
  templateContent: string
  implementationGuidance: string
  testingProcedures: string[]
  requiredEvidence: string[]
  applicableFrameworks: string[]
  isOfficial: boolean
  source: string
  usageCount: number
  createdAt: Date
  updatedAt: Date
  tags: string[]
}

export interface ControlAssessment {
  id: string
  controlId: string
  assessmentType: "design" | "implementation" | "operating-effectiveness"
  assessor: string
  assessmentDate: Date
  methodology: string
  scope: string
  findings: ControlFinding[]
  overallRating: "effective" | "partially-effective" | "ineffective"
  recommendations: string[]
  nextAssessmentDate: Date
  reportUrl?: string
}

export interface ControlWorkflow {
  id: string
  name: string
  description: string
  workflowType: "implementation" | "testing" | "remediation" | "assessment"
  steps: ControlWorkflowStep[]
  isActive: boolean
  entityId: string
}

export interface ControlWorkflowStep {
  id: string
  name: string
  description: string
  stepType: "review" | "approval" | "testing" | "documentation" | "automation"
  assignedTo: string[]
  requiredApprovals: number
  timeoutDays: number
  isParallel: boolean
  conditions?: Record<string, any>
}

export interface ControlDashboardMetrics {
  totalControls: number
  implementedControls: number
  testedControls: number
  effectiveControls: number
  implementationRate: number
  testingCompletionRate: number
  averageEffectivenessScore: number
  openFindings: number
  criticalFindings: number
  remediationProgress: number
  automationLevel: number
  agentMonitoredControls: number
  blockchainVerifiedControls: number
}
