"use client"

import React from "react"
import Link from "next/link"
import { Plus, Play, Pause, Al<PERSON><PERSON>riangle, Clock, TrendingUp, Users, CheckCircle, Activity } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"

import { WorkflowOperationsDashboard } from "@/components/workflows/workflow-operations-dashboard"
import { WorkflowInstanceTable } from "@/components/workflows/workflow-instance-table"
import { WorkflowTaskTable } from "@/components/workflows/workflow-task-table"
import { sampleWorkflowInstances, sampleWorkflowTasks, workflowDashboardMetrics } from "@/lib/workflows-data"

export default function WorkflowOperationsPage() {
  const [activeTab, setActiveTab] = React.useState("overview")

  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <div className="sticky top-0 z-30 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Workflow Operations</h1>
            <p className="text-muted-foreground">
              Monitor active workflows, manage pending approvals, and track execution performance
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" asChild>
              <Link href="/workflows/library">
                <Activity className="mr-2 h-4 w-4" />
                Templates
              </Link>
            </Button>
            <Button asChild>
              <Link href="/workflows/designer">
                <Plus className="mr-2 h-4 w-4" />
                New Workflow
              </Link>
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-4 mt-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Workflows</CardTitle>
              <Play className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{workflowDashboardMetrics.overview.activeWorkflows}</div>
              <p className="text-xs text-muted-foreground">
                +2 from yesterday
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{workflowDashboardMetrics.tasks.pendingApprovals}</div>
              <p className="text-xs text-muted-foreground">
                3 overdue
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed Today</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{workflowDashboardMetrics.overview.completedToday}</div>
              <p className="text-xs text-muted-foreground">
                +12% from yesterday
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{workflowDashboardMetrics.overview.successRate}%</div>
              <p className="text-xs text-muted-foreground">
                +0.5% from last week
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="flex-1 space-y-4">
        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="active">Active Workflows</TabsTrigger>
            <TabsTrigger value="tasks">Pending Tasks</TabsTrigger>
            <TabsTrigger value="failed">Failed Executions</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <WorkflowOperationsDashboard />
          </TabsContent>

          <TabsContent value="active" className="space-y-4">
            <WorkflowInstanceTable 
              instances={sampleWorkflowInstances.filter(w => w.status === "active" || w.status === "pending")} 
            />
          </TabsContent>

          <TabsContent value="tasks" className="space-y-4">
            <WorkflowTaskTable tasks={sampleWorkflowTasks} />
          </TabsContent>

          <TabsContent value="failed" className="space-y-4">
            <WorkflowInstanceTable 
              instances={sampleWorkflowInstances.filter(w => w.status === "failed")} 
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
