"use client"

import React from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { Shield, FileText, Clock, AlertTriangle, CheckCircle, Download } from "lucide-react"

export function ComplianceDashboard() {
  return (
    <div className="space-y-6">
      {/* Compliance Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Overall Compliance
            </CardTitle>
            <CardDescription>Regulatory adherence</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">94.2%</div>
            <Progress value={94} className="mt-2 h-2" />
            <p className="text-xs text-muted-foreground mt-1">
              +2.1% from last quarter
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Reports Generated
            </CardTitle>
            <CardDescription>Compliance documentation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">23</div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Avg Report Time
            </CardTitle>
            <CardDescription>Time to compliance</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2.3h</div>
            <p className="text-xs text-muted-foreground">
              -45m from target
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Open Violations
            </CardTitle>
            <CardDescription>Requiring attention</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">
              -2 from last week
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Framework Compliance */}
      <Card>
        <CardHeader>
          <CardTitle>Regulatory Framework Compliance</CardTitle>
          <CardDescription>Adherence to major compliance frameworks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { framework: "GDPR", compliance: 96, status: "compliant", lastAudit: "2024-01-10", nextDeadline: "2024-04-15" },
              { framework: "SOX", compliance: 94, status: "compliant", lastAudit: "2024-01-05", nextDeadline: "2024-03-31" },
              { framework: "HIPAA", compliance: 92, status: "compliant", lastAudit: "2023-12-20", nextDeadline: "2024-06-20" },
              { framework: "PCI DSS", compliance: 89, status: "minor-issues", lastAudit: "2024-01-12", nextDeadline: "2024-02-28" },
              { framework: "ISO 27001", compliance: 98, status: "compliant", lastAudit: "2024-01-08", nextDeadline: "2024-07-08" },
              { framework: "NIST CSF", compliance: 91, status: "compliant", lastAudit: "2024-01-15", nextDeadline: "2024-04-15" },
            ].map((framework) => (
              <div key={framework.framework} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{framework.framework}</h4>
                    <Badge variant={
                      framework.status === "compliant" ? "default" :
                      framework.status === "minor-issues" ? "secondary" : "destructive"
                    }>
                      {framework.status === "compliant" && <CheckCircle className="mr-1 h-3 w-3" />}
                      {framework.status === "minor-issues" && <AlertTriangle className="mr-1 h-3 w-3" />}
                      {framework.status === "compliant" ? "Compliant" : "Minor Issues"}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span>Last Audit: {new Date(framework.lastAudit).toLocaleDateString()}</span>
                    <span>Next Deadline: {new Date(framework.nextDeadline).toLocaleDateString()}</span>
                  </div>
                </div>
                <div className="text-right space-y-2">
                  <div className="text-lg font-bold">{framework.compliance}%</div>
                  <Progress value={framework.compliance} className="w-24 h-2" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Incident Reporting Requirements */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Incident Reporting Status</CardTitle>
            <CardDescription>Regulatory notification requirements</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { incident: "INC-2024-001", framework: "GDPR", deadline: "2024-01-18T14:15:00Z", status: "pending", severity: "critical" },
                { incident: "INC-2024-002", framework: "SOX", deadline: "2024-01-20T09:00:00Z", status: "submitted", severity: "high" },
                { incident: "INC-2024-003", framework: "HIPAA", deadline: "2024-01-25T17:00:00Z", status: "draft", severity: "medium" },
              ].map((report) => {
                const hoursUntilDeadline = Math.ceil((new Date(report.deadline).getTime() - new Date().getTime()) / (1000 * 60 * 60))
                
                return (
                  <div key={report.incident} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{report.incident}</h4>
                        <Badge variant="outline">{report.framework}</Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Deadline: {new Date(report.deadline).toLocaleString()}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {hoursUntilDeadline > 0 ? `${hoursUntilDeadline}h remaining` : "Overdue"}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={
                        report.status === "submitted" ? "default" :
                        report.status === "pending" ? "secondary" : "outline"
                      }>
                        {report.status}
                      </Badge>
                      {hoursUntilDeadline <= 24 && report.status !== "submitted" && (
                        <AlertTriangle className="h-4 w-4 text-orange-500" />
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Compliance Actions</CardTitle>
            <CardDescription>Required remediation activities</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { action: "Update data retention policies", framework: "GDPR", priority: "high", dueDate: "2024-02-01" },
                { action: "Conduct security awareness training", framework: "SOX", priority: "medium", dueDate: "2024-02-15" },
                { action: "Review access control procedures", framework: "HIPAA", priority: "medium", dueDate: "2024-02-28" },
                { action: "Update incident response plan", framework: "PCI DSS", priority: "high", dueDate: "2024-01-25" },
              ].map((action, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="space-y-1">
                    <h4 className="font-medium text-sm">{action.action}</h4>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{action.framework}</Badge>
                      <span className="text-xs text-muted-foreground">
                        Due: {new Date(action.dueDate).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  <Badge variant={action.priority === "high" ? "destructive" : "secondary"}>
                    {action.priority}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Compliance Reports */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Compliance Reports</CardTitle>
          <CardDescription>Generated compliance documentation</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[
              { title: "Q4 2023 GDPR Compliance Report", framework: "GDPR", generated: "2024-01-15", type: "quarterly" },
              { title: "SOX Controls Assessment", framework: "SOX", generated: "2024-01-12", type: "assessment" },
              { title: "HIPAA Incident Response Summary", framework: "HIPAA", generated: "2024-01-10", type: "incident" },
              { title: "PCI DSS Vulnerability Report", framework: "PCI DSS", generated: "2024-01-08", type: "vulnerability" },
            ].map((report, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="space-y-1">
                  <h4 className="font-medium">{report.title}</h4>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{report.framework}</Badge>
                    <Badge variant="secondary">{report.type}</Badge>
                    <span className="text-xs text-muted-foreground">
                      Generated: {new Date(report.generated).toLocaleDateString()}
                    </span>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
