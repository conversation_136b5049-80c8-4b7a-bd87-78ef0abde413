"use client"

import { useState } from "react"
import Link from "next/link"
import { 
  FileText, 
  Download, 
  Share2, 
  MoreHorizontal, 
  Plus,
  Filter,
  Search,
  Calendar,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { StandardModuleLayout, ModuleLayoutConfigs } from "@/components/standard-module-layout"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

import { sampleReports } from "@/lib/reports-data"
import { Report, ReportStatus } from "@/types/reports"

const statusConfig = {
  generated: { 
    label: "Generated", 
    variant: "default" as const, 
    icon: CheckCircle, 
    color: "text-green-600 dark:text-green-400" 
  },
  "in-progress": { 
    label: "In Progress", 
    variant: "secondary" as const, 
    icon: Clock, 
    color: "text-yellow-600 dark:text-yellow-400" 
  },
  failed: { 
    label: "Failed", 
    variant: "destructive" as const, 
    icon: XCircle, 
    color: "text-red-600 dark:text-red-400" 
  },
  scheduled: { 
    label: "Scheduled", 
    variant: "outline" as const, 
    icon: AlertCircle, 
    color: "text-blue-600 dark:text-blue-400" 
  }
}

const frameworkColors = {
  "NIST CSF 2.0": "bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300",
  "ISO 27001": "bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300",
  "SOC 2": "bg-purple-50 text-purple-700 dark:bg-purple-900/20 dark:text-purple-300",
  "PCI DSS": "bg-orange-50 text-orange-700 dark:bg-orange-900/20 dark:text-orange-300",
  "HIPAA": "bg-pink-50 text-pink-700 dark:bg-pink-900/20 dark:text-pink-300",
  "Custom": "bg-gray-50 text-gray-700 dark:bg-gray-800 dark:text-gray-300",
  "N/A": "bg-gray-50 text-gray-500 dark:bg-gray-800 dark:text-gray-400"
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 B"
  const k = 1024
  const sizes = ["B", "KB", "MB", "GB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i]
}

function formatRelativeTime(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) return "Just now"
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`
  
  return date.toLocaleDateString()
}

export default function ReportsPage() {
  const [reports] = useState<Report[]>(sampleReports)

  // For now, we'll display all reports. The filtering will be handled by the layout-level StickySearch
  // TODO: Implement context or event system to communicate with layout-level search
  const filteredReports = reports

  return (
    <StandardModuleLayout {...ModuleLayoutConfigs.simple}>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Reports</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{reports.length}</div>
            <p className="text-xs text-muted-foreground">
              +2 from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Generated</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {reports.filter(r => r.status === "generated").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Ready for download
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {reports.filter(r => r.status === "in-progress").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Currently generating
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
            <Calendar className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {reports.filter(r => r.isScheduled).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Automated reports
            </p>
          </CardContent>
        </Card>
      </div>



      {/* Reports Table */}
      <div className="border-0 rounded-none -mx-4">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Report Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Framework</TableHead>
              <TableHead>Generated</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Audience</TableHead>
              <TableHead>Data Freshness</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredReports.map((report) => {
              const statusInfo = statusConfig[report.status]
              const StatusIcon = statusInfo.icon
              
              return (
                <TableRow key={report.id}>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium">{report.name}</div>
                      {report.description && (
                        <div className="text-sm text-muted-foreground line-clamp-1">
                          {report.description}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="capitalize">
                      {report.reportType}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge 
                      variant="secondary" 
                      className={frameworkColors[report.framework] || frameworkColors["Custom"]}
                    >
                      {report.framework}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm">
                        {formatRelativeTime(report.generationDate)}
                      </div>
                      {report.isScheduled && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Calendar className="h-3 w-3" />
                          Scheduled
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <StatusIcon className={`h-4 w-4 ${statusInfo.color}`} />
                      <Badge variant={statusInfo.variant}>
                        {statusInfo.label}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="capitalize">
                      {report.targetAudience}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {formatRelativeTime(report.dataFreshness)}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <FileText className="mr-2 h-4 w-4" />
                          View
                        </DropdownMenuItem>
                        {report.status === "generated" && (
                          <>
                            <DropdownMenuItem>
                              <Download className="mr-2 h-4 w-4" />
                              Download
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Share2 className="mr-2 h-4 w-4" />
                              Share
                            </DropdownMenuItem>
                          </>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          Regenerate
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          Schedule
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </div>
    </StandardModuleLayout>
  )
}
