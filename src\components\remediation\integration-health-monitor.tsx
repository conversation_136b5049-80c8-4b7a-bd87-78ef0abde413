"use client"

import React from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Settings, CheckCircle, XCircle, AlertTriangle, RefreshCw } from "lucide-react"

export function IntegrationHealthMonitor() {
  const integrations = [
    { name: "SIEM Platform", status: "connected", health: 98, lastSync: "2 minutes ago" },
    { name: "SOAR Platform", status: "connected", health: 95, lastSync: "5 minutes ago" },
    { name: "EDR Solution", status: "connected", health: 92, lastSync: "1 minute ago" },
    { name: "Threat Intel Feed", status: "warning", health: 78, lastSync: "15 minutes ago" },
    { name: "Email Gateway", status: "connected", health: 99, lastSync: "30 seconds ago" },
    { name: "Firewall Management", status: "error", health: 0, lastSync: "2 hours ago" },
  ]

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Integration Health Monitor
          </CardTitle>
          <CardDescription>
            Real-time status of automation platform integrations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            {integrations.map((integration, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{integration.name}</h4>
                    <Badge variant={
                      integration.status === "connected" ? "default" :
                      integration.status === "warning" ? "secondary" : "destructive"
                    }>
                      {integration.status === "connected" && <CheckCircle className="mr-1 h-3 w-3" />}
                      {integration.status === "warning" && <AlertTriangle className="mr-1 h-3 w-3" />}
                      {integration.status === "error" && <XCircle className="mr-1 h-3 w-3" />}
                      {integration.status}
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>Health</span>
                      <span className="font-medium">{integration.health}%</span>
                    </div>
                    <Progress value={integration.health} className="h-2" />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Last sync: {integration.lastSync}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="sm">
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Connected Integrations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {integrations.filter(i => i.status === "connected").length}
            </div>
            <p className="text-xs text-muted-foreground">
              of {integrations.length} total
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Average Health</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(integrations.reduce((sum, i) => sum + i.health, 0) / integrations.length)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Overall system health
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Issues Detected</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {integrations.filter(i => i.status !== "connected").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Requiring attention
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
