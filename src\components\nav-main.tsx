"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"

import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

import { NavigationItem } from "@/lib/navigation-data"

export function NavMain({
  items,
}: {
  items: NavigationItem[]
}) {
  const pathname = usePathname()

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Platform</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => {
          const isActive = pathname === item.url || pathname.startsWith(item.url + "/")

          return (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton
                asChild
                tooltip={item.title}
                className={cn(
                  "transition-all duration-200 relative",
                  "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                  isActive && "border border-border bg-sidebar-accent text-sidebar-accent-foreground font-medium shadow-sm"
                )}
              >
                <Link href={item.url}>
                  {item.icon && (
                    <item.icon className={cn(
                      "transition-colors duration-200",
                      isActive && "text-sidebar-accent-foreground"
                    )} />
                  )}
                  <span>{item.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          )
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}
