"use client"

import React from "react"
import Link from "next/link"
import { Download, BarChart3, TrendingUp, Users, Clock, Shield, Activity, AlertTriangle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"

import { ResponseMetricsDashboard } from "@/components/remediation/response-metrics-dashboard"
import { TeamPerformanceAnalytics } from "@/components/remediation/team-performance-analytics"
import { IncidentPatternAnalysis } from "@/components/remediation/incident-pattern-analysis"
import { ComplianceDashboard } from "@/components/remediation/compliance-dashboard"
import { remediationMetrics } from "@/lib/remediation-data"

export default function RemediationAnalyticsPage() {
  const [activeTab, setActiveTab] = React.useState("metrics")

  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Remediation Analytics</h1>
            <p className="text-muted-foreground">
              Response metrics, team performance, incident patterns, and compliance reporting dashboard
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" asChild>
              <Link href="/remediation/incidents">
                <Activity className="mr-2 h-4 w-4" />
                Incidents
              </Link>
            </Button>
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export Report
            </Button>
            <Button variant="outline">
              <BarChart3 className="mr-2 h-4 w-4" />
              Custom Dashboard
            </Button>
          </div>
        </div>

        {/* Key Performance Indicators */}
        <div className="grid gap-4 md:grid-cols-4 mt-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">MTTD (Mean Time to Detect)</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">12m</div>
              <p className="text-xs text-muted-foreground">
                -3m from last quarter
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">MTTR (Mean Time to Resolve)</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">4.2h</div>
              <p className="text-xs text-muted-foreground">
                -45m from last quarter
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">SLA Compliance</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">98.1%</div>
              <p className="text-xs text-muted-foreground">
                {remediationMetrics.incidents.slaBreaches} breaches this month
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Team Efficiency</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">87%</div>
              <p className="text-xs text-muted-foreground">
                +5% from last month
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="flex-1 space-y-4">
        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="metrics">Response Metrics</TabsTrigger>
            <TabsTrigger value="team">Team Performance</TabsTrigger>
            <TabsTrigger value="patterns">Incident Patterns</TabsTrigger>
            <TabsTrigger value="compliance">Compliance</TabsTrigger>
          </TabsList>

          <TabsContent value="metrics" className="space-y-4">
            <ResponseMetricsDashboard />
          </TabsContent>

          <TabsContent value="team" className="space-y-4">
            <TeamPerformanceAnalytics />
          </TabsContent>

          <TabsContent value="patterns" className="space-y-4">
            <IncidentPatternAnalysis />
          </TabsContent>

          <TabsContent value="compliance" className="space-y-4">
            <ComplianceDashboard />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
