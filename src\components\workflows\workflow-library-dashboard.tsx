"use client"

import React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { 
  BookOpen, 
  Star, 
  TrendingUp, 
  Download,
  Upload,
  Settings,
  Activity,
  BarChart3
} from "lucide-react"

import { workflowDashboardMetrics, sampleWorkflowTemplates } from "@/lib/workflows-data"

export function WorkflowLibraryDashboard() {
  const popularTemplates = sampleWorkflowTemplates
    .sort((a, b) => b.usageCount - a.usageCount)
    .slice(0, 5)

  const recentTemplates = sampleWorkflowTemplates
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
    .slice(0, 5)

  return (
    <div className="space-y-6">
      {/* Template Statistics */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Template Library
            </CardTitle>
            <CardDescription>Template usage and performance</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Total Templates</span>
                <span className="font-medium">{workflowDashboardMetrics.templates.totalTemplates}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Active Templates</span>
                <span className="font-medium">{sampleWorkflowTemplates.filter(t => t.isActive).length}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Avg. Rating</span>
                <span className="font-medium">4.7/5.0</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Usage Analytics
            </CardTitle>
            <CardDescription>Template adoption metrics</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Total Executions</span>
                <span className="font-medium">{sampleWorkflowTemplates.reduce((sum, t) => sum + t.usageCount, 0)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>This Month</span>
                <span className="font-medium">156</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Growth Rate</span>
                <span className="font-medium text-green-600">+23%</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Template Health
            </CardTitle>
            <CardDescription>Quality and maintenance status</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Up to Date</span>
                <span className="font-medium">12/15</span>
              </div>
              <Progress value={80} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Documented</span>
                <span className="font-medium">13/15</span>
              </div>
              <Progress value={87} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Popular Templates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            Most Popular Templates
          </CardTitle>
          <CardDescription>Templates with highest usage and ratings</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {popularTemplates.map((template, index) => (
              <div key={template.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-muted-foreground">#{index + 1}</span>
                    <h4 className="font-medium">{template.name}</h4>
                    <Badge variant="secondary">{template.category}</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{template.description}</p>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span>{template.usageCount} executions</span>
                    <span>v{template.version}</span>
                    <span>by {template.author}</span>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="text-right">
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-current text-yellow-500" />
                      <span className="text-sm font-medium">{template.rating}</span>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Activity className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Updates */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Recently Updated
            </CardTitle>
            <CardDescription>Latest template modifications</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentTemplates.map((template) => (
                <div key={template.id} className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">{template.name}</p>
                    <p className="text-xs text-muted-foreground">
                      Updated: {new Date(template.updatedAt).toLocaleDateString()}
                    </p>
                  </div>
                  <Badge variant="outline">v{template.version}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Category Distribution
            </CardTitle>
            <CardDescription>Templates by category</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(
                sampleWorkflowTemplates.reduce((acc, template) => {
                  acc[template.category] = (acc[template.category] || 0) + 1
                  return acc
                }, {} as Record<string, number>)
              ).map(([category, count]) => (
                <div key={category} className="flex items-center justify-between">
                  <span className="text-sm capitalize">{category}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-16 bg-muted rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full" 
                        style={{ width: `${(count / sampleWorkflowTemplates.length) * 100}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
