"use client"

import { useState } from "react"
import { useRouter, useSearchPara<PERSON> } from "next/navigation"
import { 
  ArrowLeft, 
  Package, 
  Upload,
  Download,
  Save
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

import { AssetCategory, AssetType, CriticalityLevel, AssetStatus } from "@/types/assets"

export default function RegisterAssetPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const initialCategory = searchParams.get("category") as AssetCategory || "it"
  
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    category: initialCategory,
    assetType: "" as AssetType,
    status: "active" as AssetStatus,
    criticalityLevel: "medium" as CriticalityLevel,
    owner: "",
    responsibleParty: "",
    businessFunction: "",
    location: "",
    tags: ""
  })

  const assetTypesByCategory = {
    it: [
      { value: "server", label: "Server" },
      { value: "workstation", label: "Workstation" },
      { value: "laptop", label: "Laptop" },
      { value: "mobile-device", label: "Mobile Device" },
      { value: "network-device", label: "Network Device" },
      { value: "storage-device", label: "Storage Device" },
      { value: "printer", label: "Printer" },
      { value: "virtual-machine", label: "Virtual Machine" },
      { value: "container", label: "Container" }
    ],
    ot: [
      { value: "plc", label: "PLC" },
      { value: "scada", label: "SCADA" },
      { value: "hmi", label: "HMI" },
      { value: "dcs", label: "DCS" },
      { value: "safety-system", label: "Safety System" },
      { value: "industrial-network", label: "Industrial Network" },
      { value: "sensor", label: "Sensor" },
      { value: "actuator", label: "Actuator" }
    ],
    iot: [
      { value: "smart-sensor", label: "Smart Sensor" },
      { value: "edge-device", label: "Edge Device" },
      { value: "gateway", label: "Gateway" },
      { value: "smart-camera", label: "Smart Camera" },
      { value: "environmental-monitor", label: "Environmental Monitor" },
      { value: "access-control", label: "Access Control" }
    ],
    identity: [
      { value: "user-account", label: "User Account" },
      { value: "service-account", label: "Service Account" },
      { value: "privileged-account", label: "Privileged Account" },
      { value: "shared-account", label: "Shared Account" },
      { value: "system-account", label: "System Account" }
    ],
    application: [
      { value: "web-application", label: "Web Application" },
      { value: "mobile-app", label: "Mobile App" },
      { value: "desktop-app", label: "Desktop App" },
      { value: "cloud-service", label: "Cloud Service" },
      { value: "saas-platform", label: "SaaS Platform" },
      { value: "database", label: "Database" },
      { value: "middleware", label: "Middleware" }
    ],
    vendor: [
      { value: "technology-vendor", label: "Technology Vendor" },
      { value: "service-provider", label: "Service Provider" },
      { value: "consultant", label: "Consultant" },
      { value: "contractor", label: "Contractor" },
      { value: "supplier", label: "Supplier" }
    ],
    process: [
      { value: "business-process", label: "Business Process" },
      { value: "it-process", label: "IT Process" },
      { value: "security-process", label: "Security Process" },
      { value: "compliance-process", label: "Compliance Process" }
    ]
  }

  const handleSubmit = () => {
    // In a real implementation, this would submit the form data
    console.log("Registering asset with data:", formData)
    router.push(`/assets/${formData.category}`)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Register Asset</h1>
          <p className="text-muted-foreground">
            Add a new asset to the comprehensive asset management system
          </p>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Registration Methods */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Registration Methods
              </CardTitle>
              <CardDescription>
                Choose how to add assets to the system
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Button
                  variant="default"
                  className="w-full justify-start"
                >
                  <Package className="mr-2 h-4 w-4" />
                  Manual Entry
                </Button>
              </div>
              
              <div className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  <Upload className="mr-2 h-4 w-4" />
                  Bulk Import CSV
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Download className="mr-2 h-4 w-4" />
                  Network Discovery
                </Button>
              </div>

              <div className="pt-4 border-t">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Quick Tips</Label>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <div>• Use network discovery for IT assets</div>
                    <div>• Manual entry for OT/IoT devices</div>
                    <div>• Bulk import for large inventories</div>
                    <div>• Ensure accurate criticality levels</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Registration Form */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Asset Information</CardTitle>
              <CardDescription>
                Enter the basic information for the new asset
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="basic" className="space-y-6">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="basic">Basic Info</TabsTrigger>
                  <TabsTrigger value="classification">Classification</TabsTrigger>
                  <TabsTrigger value="ownership">Ownership</TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="space-y-4">
                  <div className="grid gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Asset Name</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter asset name"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Enter asset description"
                        rows={3}
                      />
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label>Category</Label>
                        <Select
                          value={formData.category}
                          onValueChange={(value: AssetCategory) => 
                            setFormData(prev => ({ ...prev, category: value, assetType: "" as AssetType }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="it">IT Assets</SelectItem>
                            <SelectItem value="ot">OT Assets</SelectItem>
                            <SelectItem value="iot">IoT Devices</SelectItem>
                            <SelectItem value="identity">Identities</SelectItem>
                            <SelectItem value="application">Applications</SelectItem>
                            <SelectItem value="vendor">Vendors</SelectItem>
                            <SelectItem value="process">Processes</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Asset Type</Label>
                        <Select
                          value={formData.assetType}
                          onValueChange={(value: AssetType) => 
                            setFormData(prev => ({ ...prev, assetType: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                          <SelectContent>
                            {assetTypesByCategory[formData.category]?.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                {type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="classification" className="space-y-4">
                  <div className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label>Status</Label>
                        <Select
                          value={formData.status}
                          onValueChange={(value: AssetStatus) => 
                            setFormData(prev => ({ ...prev, status: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="inactive">Inactive</SelectItem>
                            <SelectItem value="maintenance">Maintenance</SelectItem>
                            <SelectItem value="decommissioned">Decommissioned</SelectItem>
                            <SelectItem value="planned">Planned</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Criticality Level</Label>
                        <Select
                          value={formData.criticalityLevel}
                          onValueChange={(value: CriticalityLevel) => 
                            setFormData(prev => ({ ...prev, criticalityLevel: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select criticality" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="critical">Critical</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="low">Low</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor="businessFunction">Business Function</Label>
                        <Input
                          id="businessFunction"
                          value={formData.businessFunction}
                          onChange={(e) => setFormData(prev => ({ ...prev, businessFunction: e.target.value }))}
                          placeholder="e.g., Customer Portal, Manufacturing"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="location">Location</Label>
                        <Input
                          id="location"
                          value={formData.location}
                          onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                          placeholder="e.g., Data Center A, Office Floor 3"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tags">Tags</Label>
                      <Input
                        id="tags"
                        value={formData.tags}
                        onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
                        placeholder="Enter tags separated by commas"
                      />
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="ownership" className="space-y-4">
                  <div className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor="owner">Owner</Label>
                        <Input
                          id="owner"
                          value={formData.owner}
                          onChange={(e) => setFormData(prev => ({ ...prev, owner: e.target.value }))}
                          placeholder="e.g., IT Operations, Security Team"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="responsibleParty">Responsible Party</Label>
                        <Input
                          id="responsibleParty"
                          value={formData.responsibleParty}
                          onChange={(e) => setFormData(prev => ({ ...prev, responsibleParty: e.target.value }))}
                          placeholder="e.g., <EMAIL>"
                        />
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>

              <div className="flex justify-end gap-4 pt-6 border-t">
                <Button variant="outline" onClick={() => router.back()}>
                  Cancel
                </Button>
                <Button onClick={handleSubmit}>
                  <Save className="mr-2 h-4 w-4" />
                  Register Asset
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
