"use client"

import { useState } from "react"
import { 
  Scan, 
  Play, 
  Pause, 
  Settings, 
  Plus, 
  Network, 
  Cloud, 
  Server, 
  Activity,
  CheckCircle,
  AlertTriangle,
  Clock,
  Wifi
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"

export default function AssetDiscoveryPage() {
  const [activeScans, setActiveScans] = useState(2)

  const discoveryMethods = [
    {
      id: "network-scan",
      name: "Network Discovery",
      description: "Automated network scanning for IT assets",
      icon: Network,
      status: "active",
      lastRun: "2 hours ago",
      nextRun: "In 4 hours",
      assetsFound: 456,
      enabled: true
    },
    {
      id: "agent-based",
      name: "Agent-Based Discovery",
      description: "Software agents reporting asset information",
      icon: Server,
      status: "active",
      lastRun: "15 minutes ago",
      nextRun: "In 45 minutes",
      assetsFound: 234,
      enabled: true
    },
    {
      id: "cloud-api",
      name: "Cloud API Integration",
      description: "Cloud provider APIs for cloud asset discovery",
      icon: Cloud,
      status: "scheduled",
      lastRun: "6 hours ago",
      nextRun: "In 2 hours",
      assetsFound: 189,
      enabled: true
    },
    {
      id: "manual-import",
      name: "Manual Import",
      description: "CSV/Excel file imports and manual registration",
      icon: Plus,
      status: "idle",
      lastRun: "1 day ago",
      nextRun: "On demand",
      assetsFound: 67,
      enabled: false
    }
  ]

  const recentDiscoveries = [
    {
      id: "disc-001",
      method: "Network Scan",
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      assetsFound: 3,
      assetsUpdated: 12,
      status: "completed"
    },
    {
      id: "disc-002",
      method: "Agent Report",
      timestamp: new Date(Date.now() - 45 * 60 * 1000),
      assetsFound: 1,
      assetsUpdated: 8,
      status: "completed"
    },
    {
      id: "disc-003",
      method: "Cloud API",
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      assetsFound: 5,
      assetsUpdated: 23,
      status: "completed"
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <Activity className="h-4 w-4 text-green-600" />
      case "scheduled":
        return <Clock className="h-4 w-4 text-blue-600" />
      case "idle":
        return <Pause className="h-4 w-4 text-gray-600" />
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge variant="default">Active</Badge>
      case "scheduled":
        return <Badge variant="secondary">Scheduled</Badge>
      case "idle":
        return <Badge variant="outline">Idle</Badge>
      default:
        return <Badge variant="destructive">Error</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Asset Discovery</h1>
          <p className="text-muted-foreground">
            Configure and monitor automated asset discovery across your infrastructure
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Settings className="mr-2 h-4 w-4" />
            Configure
          </Button>
          <Button size="sm">
            <Scan className="mr-2 h-4 w-4" />
            Run Discovery
          </Button>
        </div>
      </div>

      {/* Discovery Overview */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{activeScans}</div>
            <div className="text-sm text-muted-foreground">Active Scans</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">1,247</div>
            <div className="text-sm text-muted-foreground">Total Assets</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">23</div>
            <div className="text-sm text-muted-foreground">New This Week</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">94%</div>
            <div className="text-sm text-muted-foreground">Discovery Health</div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="methods" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="methods">Discovery Methods</TabsTrigger>
          <TabsTrigger value="activity">Recent Activity</TabsTrigger>
          <TabsTrigger value="configuration">Configuration</TabsTrigger>
        </TabsList>

        <TabsContent value="methods" className="space-y-6">
          {/* Discovery Methods */}
          <div className="grid gap-4 md:grid-cols-2">
            {discoveryMethods.map((method) => {
              const Icon = method.icon
              return (
                <Card key={method.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Icon className="h-8 w-8 text-muted-foreground" />
                        <div>
                          <CardTitle className="text-lg">{method.name}</CardTitle>
                          <CardDescription>{method.description}</CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(method.status)}
                        {getStatusBadge(method.status)}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <div className="text-sm font-medium text-muted-foreground">Last Run</div>
                        <div className="text-sm">{method.lastRun}</div>
                      </div>
                      <div className="space-y-2">
                        <div className="text-sm font-medium text-muted-foreground">Next Run</div>
                        <div className="text-sm">{method.nextRun}</div>
                      </div>
                      <div className="space-y-2">
                        <div className="text-sm font-medium text-muted-foreground">Assets Found</div>
                        <div className="text-sm font-bold">{method.assetsFound}</div>
                      </div>
                      <div className="space-y-2">
                        <div className="text-sm font-medium text-muted-foreground">Status</div>
                        <div className="flex items-center space-x-2">
                          <Switch checked={method.enabled} />
                          <Label className="text-sm">
                            {method.enabled ? "Enabled" : "Disabled"}
                          </Label>
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" size="sm">
                        <Settings className="mr-2 h-3 w-3" />
                        Configure
                      </Button>
                      <Button size="sm">
                        <Play className="mr-2 h-3 w-3" />
                        Run Now
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          {/* Recent Discovery Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Discovery Activity</CardTitle>
              <CardDescription>
                Latest asset discovery runs and their results
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentDiscoveries.map((discovery) => (
                  <div
                    key={discovery.id}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <div>
                        <div className="font-medium">{discovery.method}</div>
                        <div className="text-sm text-muted-foreground">
                          {discovery.timestamp.toLocaleString()}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <div className="text-sm font-medium">{discovery.assetsFound}</div>
                        <div className="text-xs text-muted-foreground">New Assets</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm font-medium">{discovery.assetsUpdated}</div>
                        <div className="text-xs text-muted-foreground">Updated</div>
                      </div>
                      <Badge variant="default">Completed</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Discovery Progress */}
          <Card>
            <CardHeader>
              <CardTitle>Current Discovery Progress</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Network Scan (192.168.1.0/24)</span>
                  <span>75%</span>
                </div>
                <Progress value={75} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Agent Collection</span>
                  <span>100%</span>
                </div>
                <Progress value={100} className="h-2" />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="configuration" className="space-y-6">
          {/* Discovery Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Global Discovery Settings</CardTitle>
              <CardDescription>
                Configure global settings for asset discovery
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Automatic Discovery</Label>
                    <div className="text-sm text-muted-foreground">
                      Enable automatic asset discovery on schedule
                    </div>
                  </div>
                  <Switch defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Real-time Updates</Label>
                    <div className="text-sm text-muted-foreground">
                      Update asset information in real-time
                    </div>
                  </div>
                  <Switch defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Change Notifications</Label>
                    <div className="text-sm text-muted-foreground">
                      Send notifications when assets change
                    </div>
                  </div>
                  <Switch />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Blockchain Verification</Label>
                    <div className="text-sm text-muted-foreground">
                      Enable blockchain verification for asset integrity
                    </div>
                  </div>
                  <Switch defaultChecked />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Network Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Network Discovery Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Wifi className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Network discovery configuration interface</p>
                <p className="text-sm">Configure IP ranges, ports, and scanning parameters</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
