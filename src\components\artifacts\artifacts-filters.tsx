"use client"

import * as React from "react"
import { Filter, Calendar, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { ArtifactFilter, ArtifactType, SourceModule, BlockchainStatus, AccessLevel } from "@/lib/artifacts-types"
import { getArtifactTypeLabel, getSourceModuleLabel, getBlockchainStatusLabel, getAccessLevelLabel } from "@/lib/artifacts-data"

interface ArtifactsFiltersProps {
  filters: ArtifactFilter
  onFiltersChange: (filters: ArtifactFilter) => void
  className?: string
}

const artifactTypes: ArtifactType[] = [
  "control-evidence",
  "assessment-report", 
  "policy-document",
  "incident-report",
  "audit-evidence",
  "compliance-certificate",
  "risk-assessment",
  "security-log",
  "configuration-backup",
  "training-record"
]

const sourceModules: SourceModule[] = [
  "overview",
  "activity", 
  "assets",
  "monitor",
  "frameworks",
  "controls",
  "policies",
  "assessments",
  "workflows",
  "remediation",
  "reports",
  "portals",
  "manual-upload"
]

const blockchainStatuses: BlockchainStatus[] = ["verified", "pending", "failed", "not-verified"]
const accessLevels: AccessLevel[] = ["public", "internal", "restricted", "confidential"]

export function ArtifactsFilters({ filters, onFiltersChange, className }: ArtifactsFiltersProps) {
  const [dateRangeStart, setDateRangeStart] = React.useState("")
  const [dateRangeEnd, setDateRangeEnd] = React.useState("")

  const updateFilter = (key: keyof ArtifactFilter, value: any) => {
    onFiltersChange({ ...filters, [key]: value })
  }

  const addArrayFilter = (key: keyof ArtifactFilter, value: string) => {
    const currentArray = (filters[key] as string[]) || []
    if (!currentArray.includes(value)) {
      updateFilter(key, [...currentArray, value])
    }
  }

  const removeArrayFilter = (key: keyof ArtifactFilter, value: string) => {
    const currentArray = (filters[key] as string[]) || []
    updateFilter(key, currentArray.filter(item => item !== value))
  }

  const clearAllFilters = () => {
    onFiltersChange({})
    setDateRangeStart("")
    setDateRangeEnd("")
  }

  const applyDateRange = () => {
    if (dateRangeStart && dateRangeEnd) {
      updateFilter("dateRange", {
        start: new Date(dateRangeStart),
        end: new Date(dateRangeEnd)
      })
    }
  }

  const clearDateRange = () => {
    updateFilter("dateRange", undefined)
    setDateRangeStart("")
    setDateRangeEnd("")
  }

  const hasActiveFilters = Object.keys(filters).some(key => {
    const value = filters[key as keyof ArtifactFilter]
    return value !== undefined && value !== null && 
           (Array.isArray(value) ? value.length > 0 : true)
  })

  return (
    <div className={`flex items-center gap-3 flex-wrap ${className}`}>
      {/* Artifact Type Filter */}
      <Select onValueChange={(value) => addArrayFilter("artifactTypes", value)}>
        <SelectTrigger className="w-48">
          <SelectValue placeholder="Artifact Type" />
        </SelectTrigger>
        <SelectContent>
          {artifactTypes.map((type) => (
            <SelectItem key={type} value={type}>
              {getArtifactTypeLabel(type)}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Source Module Filter */}
      <Select onValueChange={(value) => addArrayFilter("sourceModules", value)}>
        <SelectTrigger className="w-48">
          <SelectValue placeholder="Source Module" />
        </SelectTrigger>
        <SelectContent>
          {sourceModules.map((module) => (
            <SelectItem key={module} value={module}>
              {getSourceModuleLabel(module)}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Blockchain Status Filter */}
      <Select onValueChange={(value) => addArrayFilter("blockchainStatus", value)}>
        <SelectTrigger className="w-48">
          <SelectValue placeholder="Verification Status" />
        </SelectTrigger>
        <SelectContent>
          {blockchainStatuses.map((status) => (
            <SelectItem key={status} value={status}>
              {getBlockchainStatusLabel(status)}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Access Level Filter */}
      <Select onValueChange={(value) => addArrayFilter("accessLevels", value)}>
        <SelectTrigger className="w-48">
          <SelectValue placeholder="Access Level" />
        </SelectTrigger>
        <SelectContent>
          {accessLevels.map((level) => (
            <SelectItem key={level} value={level}>
              {getAccessLevelLabel(level)}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Date Range Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Date Range
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-80 p-4">
          <DropdownMenuLabel>Filter by Date Range</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium">Start Date</label>
              <Input
                type="date"
                value={dateRangeStart}
                onChange={(e) => setDateRangeStart(e.target.value)}
                className="mt-1"
              />
            </div>
            <div>
              <label className="text-sm font-medium">End Date</label>
              <Input
                type="date"
                value={dateRangeEnd}
                onChange={(e) => setDateRangeEnd(e.target.value)}
                className="mt-1"
              />
            </div>
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={applyDateRange}
                disabled={!dateRangeStart || !dateRangeEnd}
              >
                Apply
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={clearDateRange}
              >
                Clear
              </Button>
            </div>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Archive Filter Toggle */}
      <Button
        variant={filters.isArchived ? "default" : "outline"}
        onClick={() => updateFilter("isArchived", filters.isArchived ? undefined : true)}
        className="flex items-center gap-2"
      >
        <Filter className="h-4 w-4" />
        {filters.isArchived ? "Archived Only" : "Include Archived"}
      </Button>

      {/* Clear All Filters */}
      {hasActiveFilters && (
        <Button
          variant="ghost"
          onClick={clearAllFilters}
          className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
        >
          <X className="h-4 w-4" />
          Clear All
        </Button>
      )}

      {/* Active Filters Display */}
      <div className="flex items-center gap-2 flex-wrap">
        {filters.artifactTypes?.map((type) => (
          <Badge key={type} variant="secondary" className="flex items-center gap-1">
            {getArtifactTypeLabel(type)}
            <X 
              className="h-3 w-3 cursor-pointer" 
              onClick={() => removeArrayFilter("artifactTypes", type)}
            />
          </Badge>
        ))}
        
        {filters.sourceModules?.map((module) => (
          <Badge key={module} variant="secondary" className="flex items-center gap-1">
            {getSourceModuleLabel(module)}
            <X 
              className="h-3 w-3 cursor-pointer" 
              onClick={() => removeArrayFilter("sourceModules", module)}
            />
          </Badge>
        ))}
        
        {filters.blockchainStatus?.map((status) => (
          <Badge key={status} variant="secondary" className="flex items-center gap-1">
            {getBlockchainStatusLabel(status)}
            <X 
              className="h-3 w-3 cursor-pointer" 
              onClick={() => removeArrayFilter("blockchainStatus", status)}
            />
          </Badge>
        ))}
        
        {filters.accessLevels?.map((level) => (
          <Badge key={level} variant="secondary" className="flex items-center gap-1">
            {getAccessLevelLabel(level)}
            <X 
              className="h-3 w-3 cursor-pointer" 
              onClick={() => removeArrayFilter("accessLevels", level)}
            />
          </Badge>
        ))}
        
        {filters.dateRange && (
          <Badge variant="secondary" className="flex items-center gap-1">
            {filters.dateRange.start.toLocaleDateString()} - {filters.dateRange.end.toLocaleDateString()}
            <X 
              className="h-3 w-3 cursor-pointer" 
              onClick={clearDateRange}
            />
          </Badge>
        )}
      </div>
    </div>
  )
}
