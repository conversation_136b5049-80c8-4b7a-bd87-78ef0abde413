"use client"

import * as React from "react"
import { 
  Download, 
  Shield, 
  Eye, 
  Copy, 
  ExternalLink, 
  Clock, 
  User, 
  FileText,
  CheckCircle,
  AlertTriangle,
  X
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Artifact } from "@/lib/artifacts-types"
import { 
  getArtifactTypeLabel, 
  getSourceModuleLabel, 
  getBlockchainStatusLabel, 
  getAccessLevelLabel,
  formatFileSize 
} from "@/lib/artifacts-data"

interface ArtifactDetailViewProps {
  artifact: Artifact
  onClose: () => void
  onDownload?: (artifact: Artifact) => void
  onVerify?: (artifact: Artifact) => void
  className?: string
}

export function ArtifactDetailView({
  artifact,
  onClose,
  onDownload,
  onVerify,
  className
}: ArtifactDetailViewProps) {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const getBlockchainStatusIcon = (status: string) => {
    switch (status) {
      case "verified":
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case "pending":
        return <Clock className="h-5 w-5 text-yellow-500" />
      case "failed":
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      default:
        return <X className="h-5 w-5 text-gray-400" />
    }
  }

  const getAccessLevelColor = (level: string) => {
    switch (level) {
      case "public":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      case "internal":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
      case "restricted":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
      case "confidential":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    }
  }

  return (
    <div className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 ${className}`}>
      <div className="bg-background border rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-2xl font-bold">{artifact.name}</h2>
            <p className="text-muted-foreground mt-1">{artifact.description}</p>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="p-6 space-y-6">
          {/* Quick Actions */}
          <div className="flex items-center gap-3">
            <Button onClick={() => onDownload?.(artifact)} className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Download
            </Button>
            
            {artifact.blockchainStatus !== "verified" && (
              <Button variant="outline" onClick={() => onVerify?.(artifact)} className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Verify
              </Button>
            )}
            
            <Button variant="outline" className="flex items-center gap-2">
              <ExternalLink className="h-4 w-4" />
              View on Blockchain
            </Button>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Type</label>
                  <div className="mt-1">
                    <Badge variant="outline">{getArtifactTypeLabel(artifact.artifactType)}</Badge>
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Source Module</label>
                  <div className="mt-1 text-sm">{getSourceModuleLabel(artifact.sourceModule)}</div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Access Level</label>
                  <div className="mt-1">
                    <Badge className={getAccessLevelColor(artifact.accessLevel)}>
                      {getAccessLevelLabel(artifact.accessLevel)}
                    </Badge>
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-muted-foreground">File Info</label>
                  <div className="mt-1 text-sm">
                    {formatFileSize(artifact.fileSize)} • {artifact.fileFormat}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Blockchain Verification */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Blockchain Verification
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Status</label>
                  <div className="mt-1 flex items-center gap-2">
                    {getBlockchainStatusIcon(artifact.blockchainStatus)}
                    <span className="text-sm">{getBlockchainStatusLabel(artifact.blockchainStatus)}</span>
                  </div>
                </div>
                
                {artifact.blockchainHash && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Hash</label>
                    <div className="mt-1 flex items-center gap-2">
                      <code className="text-xs bg-muted px-2 py-1 rounded flex-1 break-all">
                        {artifact.blockchainHash}
                      </code>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(artifact.blockchainHash!)}
                        className="h-8 w-8 p-0"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                )}
                
                {artifact.blockchainBlock && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Block</label>
                    <div className="mt-1 text-sm font-mono">{artifact.blockchainBlock}</div>
                  </div>
                )}
                
                {artifact.verificationTimestamp && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Verified At</label>
                    <div className="mt-1 text-sm">{artifact.verificationTimestamp.toLocaleString()}</div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Metadata */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Metadata
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Created By</label>
                  <div className="mt-1 text-sm">{artifact.createdBy}</div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Created At</label>
                  <div className="mt-1 text-sm">{artifact.createdAt.toLocaleString()}</div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                  <div className="mt-1 text-sm">{artifact.updatedAt.toLocaleString()}</div>
                </div>
                
                {artifact.tags.length > 0 && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Tags</label>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {artifact.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Compliance Mapping */}
            <Card>
              <CardHeader>
                <CardTitle>Compliance Mapping</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {artifact.relatedFrameworks.length > 0 && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Frameworks</label>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {artifact.relatedFrameworks.map((framework) => (
                        <Badge key={framework} variant="outline" className="text-xs">
                          {framework}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                
                {artifact.relatedControls.length > 0 && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Controls</label>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {artifact.relatedControls.map((control) => (
                        <Badge key={control} variant="outline" className="text-xs">
                          {control}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Chain of Custody */}
          {artifact.chainOfCustody.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Chain of Custody</CardTitle>
                <CardDescription>Complete audit trail of all actions performed on this artifact</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {artifact.chainOfCustody.map((record) => (
                    <div key={record.id} className="flex items-start gap-3 p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {record.action}
                          </Badge>
                          <span className="text-sm font-medium">{record.userName}</span>
                          <span className="text-xs text-muted-foreground">
                            {record.timestamp.toLocaleString()}
                          </span>
                        </div>
                        {record.details && (
                          <p className="text-sm text-muted-foreground mt-1">{record.details}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
