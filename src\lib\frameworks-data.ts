import { Framework, FrameworkTemplate, FrameworkAssessment, ControlHarmonization } from "@/types/frameworks"

// Sample frameworks data for demonstration
export const sampleFrameworks: Framework[] = [
  {
    id: "fw-001",
    name: "NIST Cybersecurity Framework 2.0",
    description: "Comprehensive cybersecurity framework for managing and reducing cybersecurity risk",
    frameworkType: "regulatory",
    standard: "NIST CSF 2.0",
    version: "2.0",
    implementationStatus: "implemented",
    compliancePercentage: 87,
    gapCount: 12,
    criticalGaps: 1,
    highGaps: 3,
    mediumGaps: 5,
    lowGaps: 3,
    certificationStatus: "certified",
    certificationDate: new Date("2023-11-15"),
    expirationDate: new Date("2026-11-15"),
    nextMilestone: new Date("2024-05-15"),
    milestoneDescription: "Annual assessment review",
    riskLevel: "low",
    totalControls: 108,
    implementedControls: 94,
    entityId: "auris-hq",
    createdBy: "<EMAIL>",
    createdAt: new Date("2023-01-15"),
    updatedAt: new Date("2024-01-20"),
    lastAssessment: new Date("2024-01-10"),
    isActive: true,
    tags: ["cybersecurity", "risk-management", "federal"]
  },
  {
    id: "fw-002",
    name: "ISO 27001:2022 Information Security Management",
    description: "International standard for information security management systems",
    frameworkType: "industry",
    standard: "ISO 27001",
    version: "2022",
    implementationStatus: "in-progress",
    compliancePercentage: 73,
    gapCount: 28,
    criticalGaps: 2,
    highGaps: 8,
    mediumGaps: 12,
    lowGaps: 6,
    certificationStatus: "in-process",
    nextMilestone: new Date("2024-03-30"),
    milestoneDescription: "Stage 2 certification audit",
    riskLevel: "medium",
    totalControls: 114,
    implementedControls: 83,
    entityId: "auris-hq",
    createdBy: "<EMAIL>",
    createdAt: new Date("2023-06-01"),
    updatedAt: new Date("2024-01-25"),
    lastAssessment: new Date("2024-01-15"),
    isActive: true,
    tags: ["information-security", "international", "certification"]
  },
  {
    id: "fw-003",
    name: "SOC 2 Type II Security & Availability",
    description: "Service Organization Control 2 for security, availability, and confidentiality",
    frameworkType: "industry",
    standard: "SOC 2",
    version: "2017",
    implementationStatus: "certified",
    compliancePercentage: 95,
    gapCount: 3,
    criticalGaps: 0,
    highGaps: 1,
    mediumGaps: 2,
    lowGaps: 0,
    certificationStatus: "certified",
    certificationDate: new Date("2023-12-01"),
    expirationDate: new Date("2024-12-01"),
    nextMilestone: new Date("2024-06-01"),
    milestoneDescription: "Mid-year surveillance audit",
    riskLevel: "low",
    totalControls: 67,
    implementedControls: 64,
    entityId: "auris-hq",
    createdBy: "<EMAIL>",
    createdAt: new Date("2023-03-01"),
    updatedAt: new Date("2024-01-18"),
    lastAssessment: new Date("2023-12-15"),
    isActive: true,
    tags: ["soc2", "audit", "service-organization"]
  },
  {
    id: "fw-004",
    name: "PCI DSS Payment Card Security",
    description: "Payment Card Industry Data Security Standard for cardholder data protection",
    frameworkType: "industry",
    standard: "PCI DSS",
    version: "4.0",
    implementationStatus: "in-progress",
    compliancePercentage: 68,
    gapCount: 15,
    criticalGaps: 3,
    highGaps: 5,
    mediumGaps: 4,
    lowGaps: 3,
    certificationStatus: "planning",
    nextMilestone: new Date("2024-04-15"),
    milestoneDescription: "Self-assessment questionnaire completion",
    riskLevel: "high",
    totalControls: 78,
    implementedControls: 53,
    entityId: "auris-fintech",
    createdBy: "<EMAIL>",
    createdAt: new Date("2023-09-01"),
    updatedAt: new Date("2024-01-22"),
    lastAssessment: new Date("2024-01-08"),
    isActive: true,
    tags: ["payment-cards", "fintech", "data-protection"]
  },
  {
    id: "fw-005",
    name: "HIPAA Security & Privacy Rules",
    description: "Health Insurance Portability and Accountability Act compliance framework",
    frameworkType: "regulatory",
    standard: "HIPAA",
    version: "2013",
    implementationStatus: "not-started",
    compliancePercentage: 15,
    gapCount: 42,
    criticalGaps: 8,
    highGaps: 15,
    mediumGaps: 12,
    lowGaps: 7,
    certificationStatus: "not-applicable",
    nextMilestone: new Date("2024-06-01"),
    milestoneDescription: "Implementation planning phase",
    riskLevel: "critical",
    totalControls: 54,
    implementedControls: 8,
    entityId: "auris-healthcare",
    createdBy: "<EMAIL>",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-20"),
    isActive: true,
    tags: ["healthcare", "privacy", "regulatory"]
  },
  {
    id: "fw-006",
    name: "Custom Auris Security Framework",
    description: "Organization-specific security framework combining multiple standards",
    frameworkType: "custom",
    standard: "Custom",
    version: "1.2",
    implementationStatus: "implemented",
    compliancePercentage: 92,
    gapCount: 5,
    criticalGaps: 0,
    highGaps: 2,
    mediumGaps: 3,
    lowGaps: 0,
    certificationStatus: "not-applicable",
    nextMilestone: new Date("2024-07-01"),
    milestoneDescription: "Framework version 1.3 update",
    riskLevel: "low",
    totalControls: 89,
    implementedControls: 82,
    entityId: "auris-hq",
    createdBy: "<EMAIL>",
    createdAt: new Date("2022-01-01"),
    updatedAt: new Date("2024-01-15"),
    lastAssessment: new Date("2024-01-05"),
    isActive: true,
    tags: ["custom", "hybrid", "organizational"]
  }
]

// Sample framework templates
export const sampleFrameworkTemplates: FrameworkTemplate[] = [
  {
    id: "tpl-001",
    name: "NIST CSF 2.0 Complete",
    description: "Complete NIST Cybersecurity Framework 2.0 with all functions and categories",
    standard: "NIST CSF 2.0",
    version: "2.0",
    frameworkType: "regulatory",
    controlCount: 108,
    isOfficial: true,
    source: "NIST",
    oscalProfile: "nist-csf-2.0-profile.json",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
    usageCount: 245,
    tags: ["official", "complete", "cybersecurity"]
  },
  {
    id: "tpl-002",
    name: "ISO 27001:2022 Standard",
    description: "ISO 27001:2022 Information Security Management System controls",
    standard: "ISO 27001",
    version: "2022",
    frameworkType: "industry",
    controlCount: 114,
    isOfficial: true,
    source: "ISO",
    oscalProfile: "iso-27001-2022-profile.json",
    createdAt: new Date("2023-10-01"),
    updatedAt: new Date("2023-10-01"),
    usageCount: 189,
    tags: ["official", "iso", "information-security"]
  },
  {
    id: "tpl-003",
    name: "SOC 2 Type II Template",
    description: "SOC 2 Type II controls for security, availability, and confidentiality",
    standard: "SOC 2",
    version: "2017",
    frameworkType: "industry",
    controlCount: 67,
    isOfficial: true,
    source: "AICPA",
    createdAt: new Date("2023-01-01"),
    updatedAt: new Date("2023-01-01"),
    usageCount: 156,
    tags: ["official", "soc2", "audit"]
  },
  {
    id: "tpl-004",
    name: "PCI DSS 4.0 Complete",
    description: "Payment Card Industry Data Security Standard version 4.0",
    standard: "PCI DSS",
    version: "4.0",
    frameworkType: "industry",
    controlCount: 78,
    isOfficial: true,
    source: "PCI Security Standards Council",
    createdAt: new Date("2023-03-01"),
    updatedAt: new Date("2023-03-01"),
    usageCount: 98,
    tags: ["official", "pci", "payment-security"]
  },
  {
    id: "tpl-005",
    name: "HIPAA Security & Privacy",
    description: "HIPAA Security Rule and Privacy Rule requirements",
    standard: "HIPAA",
    version: "2013",
    frameworkType: "regulatory",
    controlCount: 54,
    isOfficial: true,
    source: "HHS",
    createdAt: new Date("2023-01-01"),
    updatedAt: new Date("2023-01-01"),
    usageCount: 67,
    tags: ["official", "hipaa", "healthcare"]
  }
]

// Sample control harmonization data
export const sampleControlHarmonization: ControlHarmonization[] = [
  {
    id: "harm-001",
    commonControlId: "ACCESS-CONTROL-001",
    commonControlTitle: "User Access Management",
    description: "Manage user access to systems and data based on business requirements",
    mappedFrameworks: [
      { frameworkId: "fw-001", controlId: "PR.AC-1", mappingStrength: 95 },
      { frameworkId: "fw-002", controlId: "A.9.1.1", mappingStrength: 90 },
      { frameworkId: "fw-003", controlId: "CC6.1", mappingStrength: 88 },
      { frameworkId: "fw-004", controlId: "7.1", mappingStrength: 92 }
    ],
    implementationGuidance: "Implement role-based access control with regular access reviews",
    evidenceRequirements: ["Access control policy", "User access matrix", "Access review reports"],
    testingProcedures: ["Review access control configurations", "Test access provisioning process"],
    automationCapable: true,
    riskReduction: 85
  },
  {
    id: "harm-002",
    commonControlId: "ENCRYPTION-001",
    commonControlTitle: "Data Encryption",
    description: "Encrypt sensitive data in transit and at rest",
    mappedFrameworks: [
      { frameworkId: "fw-001", controlId: "PR.DS-1", mappingStrength: 98 },
      { frameworkId: "fw-002", controlId: "A.10.1.1", mappingStrength: 95 },
      { frameworkId: "fw-003", controlId: "CC6.7", mappingStrength: 90 },
      { frameworkId: "fw-004", controlId: "3.4", mappingStrength: 100 }
    ],
    implementationGuidance: "Use industry-standard encryption algorithms and key management",
    evidenceRequirements: ["Encryption policy", "Key management procedures", "Encryption implementation documentation"],
    testingProcedures: ["Verify encryption implementation", "Test key management processes"],
    automationCapable: true,
    riskReduction: 92
  },
  {
    id: "harm-003",
    commonControlId: "INCIDENT-RESPONSE-001",
    commonControlTitle: "Incident Response Planning",
    description: "Establish and maintain incident response capabilities",
    mappedFrameworks: [
      { frameworkId: "fw-001", controlId: "RS.RP-1", mappingStrength: 100 },
      { frameworkId: "fw-002", controlId: "A.16.1.1", mappingStrength: 95 },
      { frameworkId: "fw-003", controlId: "CC7.4", mappingStrength: 85 }
    ],
    implementationGuidance: "Develop comprehensive incident response plan with defined roles and procedures",
    evidenceRequirements: ["Incident response plan", "Training records", "Incident response testing results"],
    testingProcedures: ["Review incident response plan", "Conduct tabletop exercises"],
    automationCapable: false,
    riskReduction: 78
  }
]

// Sample assessment data
export const sampleAssessments: FrameworkAssessment[] = [
  {
    id: "assess-001",
    frameworkId: "fw-001",
    assessmentType: "compliance-check",
    status: "completed",
    startDate: new Date("2024-01-05"),
    endDate: new Date("2024-01-15"),
    assessor: "Internal Audit Team",
    scope: ["All NIST CSF functions"],
    findings: [
      {
        id: "find-001",
        controlId: "PR.AC-3",
        severity: "medium",
        finding: "Remote access controls need enhancement",
        recommendation: "Implement multi-factor authentication for all remote access",
        status: "in-progress",
        dueDate: new Date("2024-03-01"),
        assignedTo: "IT Security Team",
        evidenceRequired: ["MFA implementation documentation", "Remote access policy update"]
      }
    ],
    overallScore: 87,
    recommendations: [
      "Enhance remote access controls",
      "Update incident response procedures",
      "Improve vendor risk management"
    ],
    nextAssessmentDate: new Date("2024-07-01"),
    reportUrl: "/assessments/reports/assess-001.pdf"
  }
]

// Framework comparison data
export const frameworkComparisonData = {
  totalFrameworks: 6,
  implementedFrameworks: 4,
  certifiedFrameworks: 2,
  averageCompliance: 73,
  totalGaps: 105,
  criticalGaps: 14,
  commonControlsCount: 45,
  uniqueControlsCount: 89,
  overlapPercentage: 67
}

// Control mapping statistics
export const controlMappingStats = {
  totalMappings: 234,
  verifiedMappings: 189,
  pendingMappings: 45,
  conflictingMappings: 12,
  automatedMappings: 156,
  manualMappings: 78
}
