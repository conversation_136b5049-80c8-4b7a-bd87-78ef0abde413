"use client"

import React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Users, 
  FileText,
  ArrowRight,
  UserCheck,
  AlertTriangle
} from "lucide-react"
import { Approval } from "@/types/activity"

interface ApprovalWorkflowInterfaceProps {
  approvals: Approval[]
  pendingCount: number
}

export function ApprovalWorkflowInterface({ approvals, pendingCount }: ApprovalWorkflowInterfaceProps) {
  const [filter, setFilter] = React.useState<"all" | "pending" | "approved" | "rejected">("pending")

  const filteredApprovals = approvals.filter(approval => {
    if (filter === "all") return true
    return approval.status === filter
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved": return <CheckCircle className="h-4 w-4 text-green-500" />
      case "rejected": return <XCircle className="h-4 w-4 text-red-500" />
      case "pending": return <Clock className="h-4 w-4 text-orange-500" />
      case "delegated": return <Users className="h-4 w-4 text-blue-500" />
      case "expired": return <AlertTriangle className="h-4 w-4 text-gray-500" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "policy": return <FileText className="h-4 w-4" />
      case "control": return <CheckCircle className="h-4 w-4" />
      case "assessment": return <FileText className="h-4 w-4" />
      case "remediation": return <AlertTriangle className="h-4 w-4" />
      case "workflow": return <Users className="h-4 w-4" />
      case "exception": return <XCircle className="h-4 w-4" />
      case "risk-acceptance": return <AlertTriangle className="h-4 w-4" />
      default: return <FileText className="h-4 w-4" />
    }
  }

  const getStepStatus = (step: any) => {
    switch (step.status) {
      case "approved": return "text-green-600"
      case "rejected": return "text-red-600"
      case "pending": return "text-orange-600"
      case "delegated": return "text-blue-600"
      default: return "text-gray-600"
    }
  }

  const getDaysUntilDue = (dueDate: Date) => {
    const today = new Date()
    const due = new Date(dueDate)
    const diffTime = due.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const getApprovalProgress = (approval: Approval) => {
    const completedSteps = approval.approvers.filter(step => 
      step.status === "approved" || step.status === "rejected"
    ).length
    return (completedSteps / approval.approvers.length) * 100
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserCheck className="h-5 w-5" />
          Approval & Review Workflows
          {pendingCount > 0 && (
            <Badge variant="destructive" className="ml-2">
              {pendingCount}
            </Badge>
          )}
        </CardTitle>
        <CardDescription>
          Pending approvals queue, review assignments, and delegation capabilities
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Approval Filters */}
        <div className="flex items-center gap-1">
          {["pending", "all", "approved", "rejected"].map((filterType) => (
            <Button
              key={filterType}
              variant={filter === filterType ? "default" : "outline"}
              size="sm"
              onClick={() => setFilter(filterType as any)}
            >
              {filterType === "all" ? "All" : filterType.charAt(0).toUpperCase() + filterType.slice(1)}
            </Button>
          ))}
        </div>

        {/* Approval List */}
        <div className="space-y-4">
          {filteredApprovals.map((approval) => {
            const daysUntilDue = getDaysUntilDue(approval.requiredBy)
            const isOverdue = daysUntilDue < 0
            const isDueSoon = daysUntilDue <= 2 && daysUntilDue >= 0
            const progress = getApprovalProgress(approval)
            const currentStep = approval.approvers[approval.currentStep - 1]

            return (
              <div key={approval.id} className="p-4 border rounded-lg space-y-4">
                <div className="flex items-start justify-between">
                  <div className="space-y-2 flex-1">
                    <div className="flex items-center gap-2">
                      {getTypeIcon(approval.type)}
                      <h4 className="font-medium">{approval.title}</h4>
                      <Badge variant={
                        approval.status === "approved" ? "default" :
                        approval.status === "rejected" ? "destructive" :
                        approval.status === "pending" ? "secondary" :
                        "outline"
                      }>
                        {approval.status}
                      </Badge>
                      <Badge variant="outline">
                        {approval.type.replace("-", " ")}
                      </Badge>
                      <Badge variant={
                        approval.priority === "critical" ? "destructive" :
                        approval.priority === "high" ? "default" :
                        "secondary"
                      }>
                        {approval.priority}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {approval.description}
                    </p>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>Requested by: {approval.requestedBy.split('@')[0]}</span>
                      <span>Required by: {approval.requiredBy.toLocaleDateString()}</span>
                      {isOverdue && (
                        <Badge variant="destructive" className="text-xs">
                          {Math.abs(daysUntilDue)} days overdue
                        </Badge>
                      )}
                      {isDueSoon && (
                        <Badge variant="secondary" className="text-xs">
                          Due {daysUntilDue === 0 ? "today" : `in ${daysUntilDue} days`}
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {approval.status === "pending" && currentStep && (
                      <>
                        <Button variant="outline" size="sm">
                          Delegate
                        </Button>
                        <Button variant="destructive" size="sm">
                          Reject
                        </Button>
                        <Button size="sm">
                          Approve
                        </Button>
                      </>
                    )}
                    <Button variant="outline" size="sm">
                      View Details
                    </Button>
                  </div>
                </div>

                {/* Approval Progress */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Approval Progress</span>
                    <span>{Math.round(progress)}% Complete</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>

                {/* Approval Chain */}
                <div className="space-y-2">
                  <h5 className="text-sm font-medium">Approval Chain</h5>
                  <div className="flex items-center gap-2 overflow-x-auto">
                    {approval.approvers.map((step, index) => (
                      <React.Fragment key={step.id}>
                        <div className={`flex items-center gap-2 p-2 rounded border ${
                          step.status === "pending" && approval.currentStep === step.stepNumber 
                            ? "border-blue-300 bg-blue-50 dark:border-blue-700 dark:bg-blue-950" 
                            : ""
                        }`}>
                          {getStatusIcon(step.status)}
                          <div className="text-sm">
                            <div className="font-medium">{step.approver.split('@')[0]}</div>
                            <div className={`text-xs ${getStepStatus(step)}`}>
                              {step.status === "pending" ? "Pending" :
                               step.status === "approved" ? "Approved" :
                               step.status === "rejected" ? "Rejected" :
                               step.status === "delegated" ? "Delegated" :
                               step.status}
                            </div>
                          </div>
                        </div>
                        {index < approval.approvers.length - 1 && (
                          <ArrowRight className="h-4 w-4 text-muted-foreground" />
                        )}
                      </React.Fragment>
                    ))}
                  </div>
                </div>

                {/* Attachments */}
                {approval.attachments.length > 0 && (
                  <div className="space-y-2">
                    <h5 className="text-sm font-medium">Attachments</h5>
                    <div className="flex gap-2">
                      {approval.attachments.map((attachment) => (
                        <div key={attachment.id} className="flex items-center gap-2 p-2 border rounded text-sm">
                          <FileText className="h-4 w-4" />
                          <span>{attachment.fileName}</span>
                          {attachment.isConfidential && (
                            <Badge variant="destructive" className="text-xs">
                              Confidential
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Compliance Information */}
                {approval.complianceFramework && (
                  <div className="p-3 bg-muted rounded-lg">
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircle className="h-4 w-4" />
                      <span className="font-medium">Compliance Framework:</span>
                      <Badge variant="outline">{approval.complianceFramework}</Badge>
                    </div>
                    {approval.riskAssessment && (
                      <p className="text-xs text-muted-foreground mt-1">
                        Risk Assessment: {approval.riskAssessment}
                      </p>
                    )}
                  </div>
                )}
              </div>
            )
          })}
        </div>

        {filteredApprovals.length === 0 && (
          <div className="text-center py-8">
            <UserCheck className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-2 text-sm font-medium">No approvals found</h3>
            <p className="mt-1 text-sm text-muted-foreground">
              {filter === "pending" ? "No pending approvals at this time." : `No ${filter} approvals found.`}
            </p>
          </div>
        )}

        {/* Quick Actions */}
        {pendingCount > 0 && (
          <div className="pt-4 border-t">
            <div className="flex items-center justify-between">
              <Button variant="outline" size="sm">
                Bulk Actions
              </Button>
              <Button variant="outline" size="sm">
                Delegation Settings
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
