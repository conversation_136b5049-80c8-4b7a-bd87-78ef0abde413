"use client"

import React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { 
  Clock, 
  Activity, 
  AlertTriangle, 
  CheckCircle,
  Users,
  Shield,
  Search
} from "lucide-react"

import { Incident } from "@/types/remediation"

interface ResponseTimelineViewProps {
  incidents: Incident[]
}

export function ResponseTimelineView({ incidents }: ResponseTimelineViewProps) {
  const activeIncidents = incidents.filter(i => i.status !== "closed")

  return (
    <div className="space-y-6">
      {/* Timeline Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Response Timeline
          </CardTitle>
          <CardDescription>
            Real-time incident response timeline and activity tracking
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm">
              <Search className="mr-2 h-4 w-4" />
              Filter Timeline
            </Button>
            <Button variant="outline" size="sm">
              Last 24 Hours
            </Button>
            <Button variant="outline" size="sm">
              Last Week
            </Button>
            <Button variant="outline" size="sm">
              All Time
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Timeline Visualization */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-8">
            {activeIncidents.slice(0, 5).map((incident) => (
              <div key={incident.id} className="relative">
                {/* Timeline Line */}
                <div className="absolute left-4 top-8 bottom-0 w-0.5 bg-border"></div>
                
                {/* Incident Header */}
                <div className="flex items-start gap-4">
                  <div className="relative z-10 flex h-8 w-8 items-center justify-center rounded-full border-2 border-background bg-primary">
                    <AlertTriangle className="h-4 w-4 text-primary-foreground" />
                  </div>
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">{incident.title}</h3>
                      <Badge variant={incident.severity === "critical" ? "destructive" : "default"}>
                        {incident.severity}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{incident.id}</p>
                  </div>
                </div>

                {/* Timeline Events */}
                <div className="ml-12 mt-4 space-y-4">
                  {incident.timeline.map((event, index) => (
                    <div key={event.id} className="flex items-start gap-3">
                      <div className="relative z-10 flex h-6 w-6 items-center justify-center rounded-full border bg-background">
                        {event.type === "detection" && <AlertTriangle className="h-3 w-3" />}
                        {event.type === "acknowledgment" && <Users className="h-3 w-3" />}
                        {event.type === "investigation" && <Search className="h-3 w-3" />}
                        {event.type === "containment" && <Shield className="h-3 w-3" />}
                        {event.type === "recovery" && <CheckCircle className="h-3 w-3" />}
                        {!["detection", "acknowledgment", "investigation", "containment", "recovery"].includes(event.type) && 
                         <Activity className="h-3 w-3" />}
                      </div>
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium">{event.action}</p>
                          <span className="text-xs text-muted-foreground">
                            {new Date(event.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                        <p className="text-sm text-muted-foreground">{event.description}</p>
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-muted-foreground">by {event.actor}</span>
                          {event.automated && (
                            <Badge variant="outline" className="text-xs">
                              Automated
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                  
                  {/* Add more timeline events placeholder */}
                  <div className="flex items-start gap-3 opacity-50">
                    <div className="relative z-10 flex h-6 w-6 items-center justify-center rounded-full border bg-background">
                      <Activity className="h-3 w-3" />
                    </div>
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium">Ongoing investigation...</p>
                      <p className="text-sm text-muted-foreground">Collecting additional evidence and analyzing impact</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Timeline Statistics */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Average Response Time</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">15m</div>
            <p className="text-xs text-muted-foreground">-3m from last week</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Events Today</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">47</div>
            <p className="text-xs text-muted-foreground">+12 from yesterday</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Active Timelines</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeIncidents.length}</div>
            <p className="text-xs text-muted-foreground">incidents in progress</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
