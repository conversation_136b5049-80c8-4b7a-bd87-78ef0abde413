"use client"

import React from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Play, 
  Square, 
  Circle, 
  Diamond, 
  Hexagon,
  ArrowRight,
  Plus,
  Trash2,
  Copy,
  Settings,
  Layers,
  Grid,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Save,
  Download
} from "lucide-react"

export function WorkflowDesignerCanvas() {
  const [selectedElement, setSelectedElement] = React.useState<string | null>(null)
  const [zoomLevel, setZoomLevel] = React.useState(100)

  const bpmnElements = [
    { id: "start", type: "start-event", label: "Start Event", icon: Circle },
    { id: "task", type: "user-task", label: "User Task", icon: Square },
    { id: "service", type: "service-task", label: "Service Task", icon: Square },
    { id: "gateway", type: "exclusive-gateway", label: "Gateway", icon: Diamond },
    { id: "end", type: "end-event", label: "End Event", icon: Circle },
    { id: "subprocess", type: "subprocess", label: "Subprocess", icon: Hexagon },
  ]

  const workflowProperties = [
    { label: "Process ID", value: "new-workflow-001" },
    { label: "Process Name", value: "New Workflow" },
    { label: "Version", value: "1.0.0" },
    { label: "Category", value: "compliance" },
    { label: "Author", value: "Current User" },
  ]

  return (
    <div className="flex h-[calc(100vh-200px)] gap-4">
      {/* Left Sidebar - BPMN Palette */}
      <Card className="w-64 flex-shrink-0">
        <CardHeader>
          <CardTitle className="text-sm">BPMN Elements</CardTitle>
          <CardDescription>Drag elements to canvas</CardDescription>
        </CardHeader>
        <CardContent className="space-y-2">
          {bpmnElements.map((element) => {
            const Icon = element.icon
            return (
              <div
                key={element.id}
                className="flex items-center gap-2 p-2 border rounded cursor-pointer hover:bg-accent transition-colors"
                draggable
              >
                <Icon className="h-4 w-4" />
                <span className="text-sm">{element.label}</span>
              </div>
            )
          })}
          
          <Separator className="my-4" />
          
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Connectors</h4>
            <div className="flex items-center gap-2 p-2 border rounded cursor-pointer hover:bg-accent transition-colors">
              <ArrowRight className="h-4 w-4" />
              <span className="text-sm">Sequence Flow</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Canvas Area */}
      <div className="flex-1 flex flex-col">
        {/* Canvas Toolbar */}
        <Card className="mb-4">
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Grid className="mr-2 h-4 w-4" />
                  Grid
                </Button>
                <Button variant="outline" size="sm">
                  <Layers className="mr-2 h-4 w-4" />
                  Layers
                </Button>
                <Separator orientation="vertical" className="h-6" />
                <Button variant="outline" size="sm" onClick={() => setZoomLevel(Math.min(200, zoomLevel + 25))}>
                  <ZoomIn className="h-4 w-4" />
                </Button>
                <span className="text-sm font-medium">{zoomLevel}%</span>
                <Button variant="outline" size="sm" onClick={() => setZoomLevel(Math.max(50, zoomLevel - 25))}>
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm" onClick={() => setZoomLevel(100)}>
                  <RotateCcw className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Save className="mr-2 h-4 w-4" />
                  Save
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
                <Button size="sm">
                  <Play className="mr-2 h-4 w-4" />
                  Validate
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Canvas */}
        <Card className="flex-1">
          <CardContent className="p-0 h-full">
            <div 
              className="w-full h-full bg-grid-pattern relative overflow-auto"
              style={{ 
                backgroundImage: `radial-gradient(circle, #e5e7eb 1px, transparent 1px)`,
                backgroundSize: '20px 20px',
                transform: `scale(${zoomLevel / 100})`
              }}
            >
              {/* Canvas Content - Placeholder for BPMN Designer */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 mx-auto bg-muted rounded-lg flex items-center justify-center">
                    <Plus className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium">Start Designing</h3>
                    <p className="text-sm text-muted-foreground">
                      Drag BPMN elements from the palette to create your workflow
                    </p>
                  </div>
                  <div className="flex gap-2 justify-center">
                    <Button variant="outline" size="sm">
                      Load Template
                    </Button>
                    <Button size="sm">
                      Start from Scratch
                    </Button>
                  </div>
                </div>
              </div>

              {/* Sample BPMN Elements - Placeholder */}
              <div className="absolute top-20 left-20">
                <div className="flex items-center gap-4">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                    S
                  </div>
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                  <div className="w-16 h-12 bg-blue-500 rounded flex items-center justify-center text-white text-xs">
                    Task
                  </div>
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                  <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                    E
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Right Sidebar - Properties Panel */}
      <Card className="w-80 flex-shrink-0">
        <CardHeader>
          <CardTitle className="text-sm">Properties</CardTitle>
          <CardDescription>
            {selectedElement ? `Configure ${selectedElement}` : "Select an element to configure"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Workflow Properties */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Workflow Properties</h4>
            {workflowProperties.map((prop) => (
              <div key={prop.label} className="space-y-1">
                <label className="text-xs text-muted-foreground">{prop.label}</label>
                <div className="text-sm font-medium">{prop.value}</div>
              </div>
            ))}
          </div>

          <Separator />

          {/* Element Properties */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Element Properties</h4>
            {selectedElement ? (
              <div className="space-y-2">
                <div className="space-y-1">
                  <label className="text-xs text-muted-foreground">Element ID</label>
                  <div className="text-sm font-medium">{selectedElement}</div>
                </div>
                <div className="space-y-1">
                  <label className="text-xs text-muted-foreground">Name</label>
                  <div className="text-sm">Task Name</div>
                </div>
                <div className="space-y-1">
                  <label className="text-xs text-muted-foreground">Type</label>
                  <Badge variant="outline">User Task</Badge>
                </div>
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">
                No element selected
              </p>
            )}
          </div>

          <Separator />

          {/* Actions */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Actions</h4>
            <div className="grid grid-cols-2 gap-2">
              <Button variant="outline" size="sm" disabled={!selectedElement}>
                <Copy className="mr-2 h-3 w-3" />
                Copy
              </Button>
              <Button variant="outline" size="sm" disabled={!selectedElement}>
                <Trash2 className="mr-2 h-3 w-3" />
                Delete
              </Button>
              <Button variant="outline" size="sm" disabled={!selectedElement}>
                <Settings className="mr-2 h-3 w-3" />
                Configure
              </Button>
              <Button variant="outline" size="sm">
                <Plus className="mr-2 h-3 w-3" />
                Add
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
