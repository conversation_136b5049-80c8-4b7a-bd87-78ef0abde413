"use client"

import { useState } from "react"
import { Upload, Palette } from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { StakeholderType } from "@/types/portal"

interface GeneralSettingsProps {
  data: {
    name: string
    description: string
    stakeholderType: StakeholderType
    branding: {
      logo?: string
      primaryColor: string
      secondaryColor: string
    }
  }
  onChange: (data: any) => void
}

const stakeholderTypes: Array<{
  value: StakeholderType
  label: string
  description: string
}> = [
  {
    value: 'auditor',
    label: 'Auditor',
    description: 'External auditors and compliance assessors'
  },
  {
    value: 'customer',
    label: 'Customer',
    description: 'Customers and prospects seeking trust information'
  },
  {
    value: 'vendor',
    label: 'Vendor',
    description: 'Third-party vendors and suppliers'
  },
  {
    value: 'regulator',
    label: 'Regulator',
    description: 'Regulatory bodies and compliance authorities'
  }
]

const colorPresets = [
  { name: 'GRCOS Blue', primary: '#3b82f6', secondary: '#1e40af' },
  { name: 'Trust Green', primary: '#10b981', secondary: '#059669' },
  { name: 'Security Orange', primary: '#f97316', secondary: '#ea580c' },
  { name: 'Compliance Purple', primary: '#8b5cf6', secondary: '#7c3aed' },
  { name: 'Neutral Gray', primary: '#6b7280', secondary: '#4b5563' },
]

export function GeneralSettings({ data, onChange }: GeneralSettingsProps) {
  const [logoFile, setLogoFile] = useState<File | null>(null)

  const handleInputChange = (field: string, value: any) => {
    onChange({
      ...data,
      [field]: value
    })
  }

  const handleBrandingChange = (field: string, value: string) => {
    onChange({
      ...data,
      branding: {
        ...data.branding,
        [field]: value
      }
    })
  }

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setLogoFile(file)
      // In a real app, you would upload the file and get a URL
      const logoUrl = URL.createObjectURL(file)
      handleBrandingChange('logo', logoUrl)
    }
  }

  const applyColorPreset = (preset: typeof colorPresets[0]) => {
    onChange({
      ...data,
      branding: {
        ...data.branding,
        primaryColor: preset.primary,
        secondaryColor: preset.secondary
      }
    })
  }

  return (
    <div className="space-y-6 w-full max-w-full overflow-hidden">
      {/* Basic Information */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
          <CardDescription>
            Configure the basic details for your portal
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="portal-name">Portal Name</Label>
            <Input
              id="portal-name"
              value={data.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Enter portal name"
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="portal-description">Description</Label>
            <Input
              id="portal-description"
              value={data.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Brief description of the portal's purpose"
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <Label>Stakeholder Type</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-start">
                  {stakeholderTypes.find(type => type.value === data.stakeholderType)?.label || 'Select stakeholder type'}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[300px] max-w-[90vw]">
                {stakeholderTypes.map((type) => (
                  <DropdownMenuItem
                    key={type.value}
                    onClick={() => handleInputChange('stakeholderType', type.value)}
                  >
                    <div>
                      <div className="font-medium">{type.label}</div>
                      <div className="text-sm text-muted-foreground">{type.description}</div>
                    </div>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardContent>
      </Card>

      {/* Branding */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Branding & Appearance</CardTitle>
          <CardDescription>
            Customize the visual appearance of your portal
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Logo Upload */}
          <div className="space-y-2">
            <Label>Portal Logo</Label>
            <div className="flex flex-col lg:flex-row lg:items-center gap-4">
              {data.branding.logo && (
                <div className="w-16 h-16 border rounded-lg overflow-hidden bg-muted flex items-center justify-center shrink-0">
                  <img
                    src={data.branding.logo}
                    alt="Portal logo"
                    className="w-full h-full object-contain"
                  />
                </div>
              )}
              <div className="flex-1 min-w-0">
                <Button
                  variant="outline"
                  onClick={() => document.getElementById('logo-upload')?.click()}
                  className="gap-2 w-full lg:w-auto"
                >
                  <Upload className="h-4 w-4" />
                  {data.branding.logo ? 'Change Logo' : 'Upload Logo'}
                </Button>
                <input
                  id="logo-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleLogoUpload}
                  className="hidden"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Recommended: 200x200px, PNG or SVG
                </p>
              </div>
            </div>
          </div>

          {/* Color Scheme */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Color Scheme</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="gap-2">
                    <Palette className="h-4 w-4" />
                    Presets
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  {colorPresets.map((preset) => (
                    <DropdownMenuItem
                      key={preset.name}
                      onClick={() => applyColorPreset(preset)}
                    >
                      <div className="flex items-center gap-2">
                        <div className="flex gap-1">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: preset.primary }}
                          />
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: preset.secondary }}
                          />
                        </div>
                        <span>{preset.name}</span>
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 w-full">
              <div className="space-y-2 min-w-0">
                <Label htmlFor="primary-color">Primary Color</Label>
                <div className="flex gap-2 w-full">
                  <Input
                    id="primary-color"
                    type="color"
                    value={data.branding.primaryColor}
                    onChange={(e) => handleBrandingChange('primaryColor', e.target.value)}
                    className="w-16 h-10 p-1 border rounded shrink-0"
                  />
                  <Input
                    value={data.branding.primaryColor}
                    onChange={(e) => handleBrandingChange('primaryColor', e.target.value)}
                    placeholder="#3b82f6"
                    className="flex-1 min-w-0"
                  />
                </div>
              </div>

              <div className="space-y-2 min-w-0">
                <Label htmlFor="secondary-color">Secondary Color</Label>
                <div className="flex gap-2 w-full">
                  <Input
                    id="secondary-color"
                    type="color"
                    value={data.branding.secondaryColor}
                    onChange={(e) => handleBrandingChange('secondaryColor', e.target.value)}
                    className="w-16 h-10 p-1 border rounded shrink-0"
                  />
                  <Input
                    value={data.branding.secondaryColor}
                    onChange={(e) => handleBrandingChange('secondaryColor', e.target.value)}
                    placeholder="#1e40af"
                    className="flex-1 min-w-0"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Preview */}
          <div className="space-y-2">
            <Label>Preview</Label>
            <div 
              className="p-4 border rounded-lg"
              style={{ 
                backgroundColor: data.branding.primaryColor + '10',
                borderColor: data.branding.primaryColor + '30'
              }}
            >
              <div className="flex items-center gap-3">
                {data.branding.logo && (
                  <img
                    src={data.branding.logo}
                    alt="Logo preview"
                    className="w-8 h-8 object-contain"
                  />
                )}
                <div>
                  <h3 
                    className="font-semibold"
                    style={{ color: data.branding.primaryColor }}
                  >
                    {data.name || 'Portal Name'}
                  </h3>
                  <p 
                    className="text-sm"
                    style={{ color: data.branding.secondaryColor }}
                  >
                    {data.description || 'Portal description'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
