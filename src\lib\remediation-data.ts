import { 
  Incident, 
  Investigation, 
  Evidence, 
  IOC, 
  RemediationPlaybook, 
  PlaybookExecution,
  RemediationMetrics,
  TeamMember 
} from "@/types/remediation"

export const sampleIncidents: Incident[] = [
  {
    id: "INC-2024-001",
    title: "Suspected Data Exfiltration via Compromised Admin Account",
    description: "Unusual data access patterns detected from admin account with large file downloads during off-hours",
    severity: "critical",
    status: "investigating",
    category: "data-breach",
    source: "monitoring",
    createdAt: "2024-01-16T14:30:00Z",
    updatedAt: "2024-01-16T16:45:00Z",
    detectedAt: "2024-01-16T14:15:00Z",
    acknowledgedAt: "2024-01-16T14:35:00Z",
    assignedTo: "<EMAIL>",
    assignedTeam: "SOC Team Alpha",
    reporter: "SIEM System",
    affectedSystems: ["file-server-01", "database-prod", "vpn-gateway"],
    affectedUsers: 1250,
    businessImpact: "significant",
    technicalImpact: "severe",
    containmentStatus: "partial",
    timeline: [
      {
        id: "tl-001",
        timestamp: "2024-01-16T14:15:00Z",
        actor: "SIEM System",
        action: "Detection",
        description: "Anomalous data access pattern detected",
        type: "detection",
        automated: true
      },
      {
        id: "tl-002",
        timestamp: "2024-01-16T14:35:00Z",
        actor: "<EMAIL>",
        action: "Acknowledgment",
        description: "Incident acknowledged and investigation initiated",
        type: "acknowledgment",
        automated: false
      }
    ],
    tags: ["data-breach", "insider-threat", "high-priority"],
    artifacts: ["network-logs-20240116.pcap", "file-access-audit.csv"],
    relatedIncidents: [],
    playbooks: ["PB-DATA-BREACH-001", "PB-CONTAINMENT-002"],
    estimatedCost: 75000,
    postMortemRequired: true,
    postMortemCompleted: false,
    complianceImpact: {
      frameworks: ["GDPR", "SOX", "HIPAA"],
      reportingRequired: true,
      notificationDeadlines: ["2024-01-18T14:15:00Z"],
      regulatoryBodies: ["Data Protection Authority", "SEC"]
    }
  },
  {
    id: "INC-2024-002",
    title: "Phishing Campaign Targeting Finance Department",
    description: "Multiple employees in finance received sophisticated phishing emails with malicious attachments",
    severity: "high",
    status: "contained",
    category: "phishing",
    source: "user-report",
    createdAt: "2024-01-15T09:20:00Z",
    updatedAt: "2024-01-16T11:30:00Z",
    detectedAt: "2024-01-15T09:15:00Z",
    acknowledgedAt: "2024-01-15T09:25:00Z",
    resolvedAt: "2024-01-16T11:30:00Z",
    assignedTo: "<EMAIL>",
    assignedTeam: "SOC Team Beta",
    reporter: "<EMAIL>",
    affectedSystems: ["email-server", "workstation-finance"],
    affectedUsers: 15,
    businessImpact: "moderate",
    technicalImpact: "moderate",
    containmentStatus: "full",
    timeline: [
      {
        id: "tl-003",
        timestamp: "2024-01-15T09:15:00Z",
        actor: "<EMAIL>",
        action: "Report",
        description: "Suspicious email reported by finance team member",
        type: "detection",
        automated: false
      }
    ],
    tags: ["phishing", "finance", "email-security"],
    artifacts: ["phishing-email.eml", "malware-sample.zip"],
    relatedIncidents: [],
    playbooks: ["PB-PHISHING-001"],
    estimatedCost: 12000,
    actualCost: 8500,
    postMortemRequired: false,
    postMortemCompleted: false,
    complianceImpact: {
      frameworks: ["SOX"],
      reportingRequired: false,
      notificationDeadlines: [],
      regulatoryBodies: []
    }
  },
  {
    id: "INC-2024-003",
    title: "DDoS Attack on Public Web Services",
    description: "Distributed denial of service attack causing service degradation on customer-facing applications",
    severity: "high",
    status: "resolved",
    category: "ddos",
    source: "monitoring",
    createdAt: "2024-01-14T16:45:00Z",
    updatedAt: "2024-01-14T22:15:00Z",
    detectedAt: "2024-01-14T16:40:00Z",
    acknowledgedAt: "2024-01-14T16:50:00Z",
    resolvedAt: "2024-01-14T21:30:00Z",
    closedAt: "2024-01-14T22:15:00Z",
    assignedTo: "<EMAIL>",
    assignedTeam: "Network Security",
    reporter: "Network Monitoring",
    affectedSystems: ["web-app-prod", "load-balancer", "cdn"],
    affectedUsers: 50000,
    businessImpact: "significant",
    technicalImpact: "significant",
    containmentStatus: "eradicated",
    timeline: [],
    tags: ["ddos", "availability", "customer-impact"],
    artifacts: ["traffic-analysis.pcap", "mitigation-logs.txt"],
    relatedIncidents: [],
    playbooks: ["PB-DDOS-001"],
    estimatedCost: 25000,
    actualCost: 22000,
    lessons: "Need to improve DDoS detection thresholds and automated mitigation",
    postMortemRequired: true,
    postMortemCompleted: true,
    complianceImpact: {
      frameworks: [],
      reportingRequired: false,
      notificationDeadlines: [],
      regulatoryBodies: []
    }
  }
]

export const sampleInvestigations: Investigation[] = [
  {
    id: "INV-2024-001",
    incidentId: "INC-2024-001",
    title: "Data Exfiltration Forensic Investigation",
    description: "Comprehensive forensic analysis of suspected data breach incident",
    status: "active",
    priority: "critical",
    investigator: "<EMAIL>",
    team: "Digital Forensics",
    createdAt: "2024-01-16T15:00:00Z",
    updatedAt: "2024-01-16T16:45:00Z",
    startedAt: "2024-01-16T15:15:00Z",
    methodology: ["NIST SP 800-86", "ISO 27037"],
    scope: {
      systems: ["file-server-01", "database-prod", "admin-workstation"],
      timeRange: {
        start: "2024-01-15T00:00:00Z",
        end: "2024-01-16T23:59:59Z"
      },
      dataTypes: ["file-access-logs", "network-traffic", "system-logs", "database-logs"],
      locations: ["on-premises", "cloud-storage"]
    },
    findings: [
      {
        id: "finding-001",
        title: "Unauthorized Administrative Access",
        description: "Evidence of compromised admin credentials used for unauthorized system access",
        severity: "high",
        confidence: "high",
        evidence: ["EVD-001", "EVD-002"],
        recommendations: ["Reset all admin passwords", "Enable MFA", "Review access logs"]
      }
    ],
    evidence: [],
    iocs: [],
    timeline: [],
    recommendations: [
      "Implement privileged access management (PAM) solution",
      "Enable comprehensive audit logging",
      "Deploy user behavior analytics (UBA)"
    ],
    reportGenerated: false,
    chainOfCustody: []
  }
]

export const sampleEvidence: Evidence[] = [
  {
    id: "EVD-001",
    investigationId: "INV-2024-001",
    name: "Admin Workstation Memory Dump",
    description: "Full memory dump from compromised admin workstation",
    type: "memory-dump",
    source: "admin-workstation-01",
    collectedAt: "2024-01-16T15:30:00Z",
    collectedBy: "<EMAIL>",
    hash: "sha256:a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456",
    size: 8589934592, // 8GB
    location: "/evidence/memory-dumps/admin-ws-01-20240116.mem",
    tags: ["memory", "admin", "critical"],
    metadata: {
      hostname: "admin-workstation-01",
      os: "Windows 11 Enterprise",
      collectionTool: "FTK Imager",
      timezone: "UTC-5"
    },
    chainOfCustody: [
      {
        timestamp: "2024-01-16T15:30:00Z",
        actor: "<EMAIL>",
        action: "Collection",
        location: "Data Center Floor 3",
        notes: "Memory dump collected using FTK Imager"
      }
    ]
  }
]

export const sampleIOCs: IOC[] = [
  {
    id: "IOC-001",
    type: "ip",
    value: "**************",
    description: "Suspicious internal IP with unusual outbound traffic",
    confidence: "high",
    severity: "high",
    source: "Network Analysis",
    firstSeen: "2024-01-16T14:15:00Z",
    lastSeen: "2024-01-16T16:30:00Z",
    tags: ["internal", "data-exfiltration", "suspicious"],
    relatedIOCs: ["IOC-002"],
    investigations: ["INV-2024-001"],
    blocked: true,
    falsePositive: false,
    context: {
      bytes_transferred: 2147483648,
      connections: 15,
      protocols: ["HTTPS", "FTP"]
    }
  },
  {
    id: "IOC-002",
    type: "domain",
    value: "suspicious-cloud-storage.com",
    description: "External domain used for data exfiltration",
    confidence: "confirmed",
    severity: "critical",
    source: "DNS Analysis",
    firstSeen: "2024-01-16T14:20:00Z",
    lastSeen: "2024-01-16T16:25:00Z",
    tags: ["external", "cloud-storage", "malicious"],
    relatedIOCs: ["IOC-001"],
    investigations: ["INV-2024-001"],
    blocked: true,
    falsePositive: false,
    context: {
      registrar: "Unknown Registrar",
      creation_date: "2024-01-10",
      reputation_score: 10
    }
  }
]

export const samplePlaybooks: RemediationPlaybook[] = [
  {
    id: "PB-DATA-BREACH-001",
    name: "Data Breach Response",
    description: "Comprehensive data breach incident response playbook",
    category: "incident-response",
    version: "2.1.0",
    author: "Security Team",
    createdAt: "2023-12-01T00:00:00Z",
    updatedAt: "2024-01-10T00:00:00Z",
    isActive: true,
    automated: false,
    approvalRequired: true,
    estimatedDuration: 480, // 8 hours
    steps: [
      {
        id: "step-001",
        name: "Initial Assessment",
        description: "Assess the scope and severity of the data breach",
        type: "manual",
        order: 1,
        required: true,
        estimatedDuration: 60,
        instructions: "Review initial alerts and determine affected systems and data"
      },
      {
        id: "step-002",
        name: "Containment",
        description: "Implement immediate containment measures",
        type: "automated",
        order: 2,
        required: true,
        estimatedDuration: 30,
        automation: {
          tool: "SOAR",
          command: "isolate_systems",
          parameters: {
            systems: ["affected_systems"],
            method: "network_isolation"
          }
        }
      }
    ],
    triggers: [
      {
        type: "incident-category",
        conditions: {
          category: "data-breach",
          severity: ["high", "critical"]
        }
      }
    ],
    variables: [
      {
        name: "affected_systems",
        type: "array",
        required: true,
        description: "List of affected systems to isolate"
      }
    ],
    integrations: ["SOAR", "SIEM", "Ticketing"],
    usageCount: 12,
    successRate: 95.5,
    tags: ["data-breach", "compliance", "critical"]
  }
]

export const samplePlaybookExecutions: PlaybookExecution[] = [
  {
    id: "EXEC-001",
    playbookId: "PB-DATA-BREACH-001",
    incidentId: "INC-2024-001",
    status: "running",
    startedAt: "2024-01-16T14:45:00Z",
    executedBy: "<EMAIL>",
    approvedBy: "<EMAIL>",
    variables: {
      affected_systems: ["file-server-01", "database-prod"]
    },
    steps: [
      {
        stepId: "step-001",
        status: "completed",
        startedAt: "2024-01-16T14:45:00Z",
        completedAt: "2024-01-16T15:30:00Z",
        output: "Scope assessment completed - 2 systems affected"
      },
      {
        stepId: "step-002",
        status: "running",
        startedAt: "2024-01-16T15:30:00Z"
      }
    ],
    logs: [
      {
        timestamp: "2024-01-16T14:45:00Z",
        level: "info",
        message: "Playbook execution started",
        context: { playbookId: "PB-DATA-BREACH-001" }
      }
    ],
    errors: [],
    metrics: {
      totalDuration: 0,
      stepDurations: {
        "step-001": 2700 // 45 minutes
      },
      resourceUsage: {}
    }
  }
]

export const remediationMetrics: RemediationMetrics = {
  incidents: {
    total: 156,
    open: 8,
    inProgress: 12,
    resolved: 128,
    closed: 120,
    avgResolutionTime: 14400000, // 4 hours in ms
    avgResponseTime: 900000, // 15 minutes in ms
    slaBreaches: 3
  },
  investigations: {
    total: 45,
    active: 5,
    completed: 38,
    avgDuration: 172800000, // 2 days in ms
    evidenceCollected: 234,
    iocsIdentified: 89
  },
  playbooks: {
    total: 25,
    automated: 15,
    executed: 156,
    successRate: 94.2,
    avgExecutionTime: 7200000 // 2 hours in ms
  },
  team: {
    activeAnalysts: 12,
    workload: 75, // percentage
    avgCaseLoad: 3.2,
    certifications: 45
  }
}

export const sampleTeamMembers: TeamMember[] = [
  {
    id: "tm-001",
    name: "Alice Chen",
    email: "<EMAIL>",
    role: "senior-analyst",
    skills: ["Digital Forensics", "Malware Analysis", "Incident Response"],
    certifications: ["GCIH", "GCFA", "CISSP"],
    availability: "busy",
    currentCases: 4,
    maxCaseLoad: 5,
    shiftSchedule: {
      timezone: "UTC-5",
      shifts: [
        { day: "Monday", start: "08:00", end: "17:00" },
        { day: "Tuesday", start: "08:00", end: "17:00" }
      ]
    },
    contactMethods: [
      { type: "email", value: "<EMAIL>", priority: 1 },
      { type: "slack", value: "@alice.chen", priority: 2 }
    ],
    lastActive: "2024-01-16T16:45:00Z"
  },
  {
    id: "tm-002",
    name: "Bob Martinez",
    email: "<EMAIL>",
    role: "analyst",
    skills: ["Network Security", "Threat Hunting", "SIEM"],
    certifications: ["Security+", "CySA+"],
    availability: "available",
    currentCases: 2,
    maxCaseLoad: 4,
    shiftSchedule: {
      timezone: "UTC-5",
      shifts: [
        { day: "Monday", start: "09:00", end: "18:00" },
        { day: "Tuesday", start: "09:00", end: "18:00" }
      ]
    },
    contactMethods: [
      { type: "email", value: "<EMAIL>", priority: 1 },
      { type: "phone", value: "******-0123", priority: 2 }
    ],
    lastActive: "2024-01-16T16:30:00Z"
  }
]
