import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Remediation - Incident Response & Digital Forensics | GRCOS",
  description: "Comprehensive incident response, digital forensics investigation, automated remediation, and security analytics platform",
}

export default function RemediationLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // The sub-navigation is automatically handled by the ConditionalLayout component
  // which reads the navigation data and displays sub-modules for the Remediation section
  return children
}
