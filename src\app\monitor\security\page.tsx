export default function SecurityDashboardPage() {
  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Security Dashboard</h1>
        <p className="text-muted-foreground">
          Real-time security monitoring across all asset categories
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">IT Assets</h3>
          <p className="text-2xl font-bold text-green-600">Secure</p>
          <p className="text-sm text-muted-foreground">1,247 assets monitored</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">OT Assets</h3>
          <p className="text-2xl font-bold text-green-600">Secure</p>
          <p className="text-sm text-muted-foreground">89 systems monitored</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">IoT Devices</h3>
          <p className="text-2xl font-bold text-yellow-600">Warning</p>
          <p className="text-sm text-muted-foreground">3,456 devices, 3 alerts</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Network Traffic</h3>
          <p className="text-2xl font-bold text-green-600">Normal</p>
          <p className="text-sm text-muted-foreground">No anomalies detected</p>
        </div>
      </div>
    </div>
  )
}
