"use client"

import React from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  BookOpen, 
  Search, 
  Star, 
  Eye, 
  Clock,
  FileText,
  Lightbulb,
  HelpCircle,
  Award,
  Link
} from "lucide-react"
import { KnowledgeEntry, Task } from "@/types/activity"

interface KnowledgeContextPanelProps {
  knowledgeEntries: KnowledgeEntry[]
  relatedTasks: Task[]
}

export function KnowledgeContextPanel({ knowledgeEntries, relatedTasks }: KnowledgeContextPanelProps) {
  const [searchQuery, setSearchQuery] = React.useState("")
  const [filter, setFilter] = React.useState<"all" | "procedure" | "guideline" | "best-practice" | "faq">("all")

  const filteredEntries = knowledgeEntries.filter(entry => {
    const matchesFilter = filter === "all" || entry.type === filter
    const matchesSearch = entry.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         entry.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         entry.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    return matchesFilter && matchesSearch
  })

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "procedure": return <FileText className="h-4 w-4" />
      case "guideline": return <BookOpen className="h-4 w-4" />
      case "faq": return <HelpCircle className="h-4 w-4" />
      case "best-practice": return <Award className="h-4 w-4" />
      case "lesson-learned": return <Lightbulb className="h-4 w-4" />
      case "template": return <FileText className="h-4 w-4" />
      default: return <BookOpen className="h-4 w-4" />
    }
  }

  const getAccessLevelColor = (level: string) => {
    switch (level) {
      case "public": return "default"
      case "internal": return "secondary"
      case "restricted": return "outline"
      case "confidential": return "destructive"
      default: return "outline"
    }
  }

  const getUsefulnessStars = (rating: number) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 >= 0.5

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={i} className="h-3 w-3 fill-yellow-400 text-yellow-400" />)
    }
    if (hasHalfStar) {
      stars.push(<Star key="half" className="h-3 w-3 fill-yellow-200 text-yellow-400" />)
    }
    const remainingStars = 5 - Math.ceil(rating)
    for (let i = 0; i < remainingStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="h-3 w-3 text-gray-300" />)
    }
    return stars
  }

  const getRelatedTasksForEntry = (entryId: string) => {
    return relatedTasks.filter(task => 
      task.tags.some(tag => 
        knowledgeEntries.find(entry => entry.id === entryId)?.tags.includes(tag)
      )
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BookOpen className="h-5 w-5" />
          Knowledge & Context
        </CardTitle>
        <CardDescription>
          Contextual information and knowledge base entries relevant to your work
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search and Filters */}
        <div className="space-y-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input 
              placeholder="Search knowledge base..." 
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-1 overflow-x-auto">
            {["all", "procedure", "guideline", "best-practice", "faq"].map((filterType) => (
              <Button
                key={filterType}
                variant={filter === filterType ? "default" : "outline"}
                size="sm"
                onClick={() => setFilter(filterType as any)}
              >
                {filterType === "all" ? "All" : 
                 filterType === "best-practice" ? "Best Practices" :
                 filterType.charAt(0).toUpperCase() + filterType.slice(1)}
              </Button>
            ))}
          </div>
        </div>

        {/* Quick Access - Most Relevant */}
        <div className="p-3 bg-muted rounded-lg">
          <h4 className="text-sm font-medium mb-2">Quick Access</h4>
          <div className="space-y-1">
            {knowledgeEntries.slice(0, 3).map((entry) => (
              <div key={entry.id} className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  {getTypeIcon(entry.type)}
                  <span className="truncate">{entry.title}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Eye className="h-3 w-3" />
                  <span className="text-muted-foreground">{entry.viewCount}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Knowledge Entries */}
        <div className="space-y-3">
          {filteredEntries.map((entry) => {
            const relatedTasksCount = getRelatedTasksForEntry(entry.id).length
            const isRecentlyUpdated = (new Date().getTime() - entry.lastUpdated.getTime()) < (7 * 24 * 60 * 60 * 1000)
            const needsReview = entry.nextReview < new Date()

            return (
              <div key={entry.id} className="p-3 border rounded-lg space-y-2">
                <div className="flex items-start justify-between">
                  <div className="space-y-1 flex-1">
                    <div className="flex items-center gap-2">
                      {getTypeIcon(entry.type)}
                      <h4 className="font-medium text-sm">{entry.title}</h4>
                      <Badge variant="outline" className="text-xs">
                        {entry.type.replace("-", " ")}
                      </Badge>
                      <Badge variant={getAccessLevelColor(entry.accessLevel)} className="text-xs">
                        {entry.accessLevel}
                      </Badge>
                    </div>
                    
                    <p className="text-xs text-muted-foreground line-clamp-2">
                      {entry.content.substring(0, 100)}...
                    </p>
                    
                    <div className="flex items-center gap-3 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Eye className="h-3 w-3" />
                        <span>{entry.viewCount} views</span>
                      </div>
                      <div className="flex items-center gap-1">
                        {getUsefulnessStars(entry.usefulness)}
                        <span>({entry.usefulness.toFixed(1)})</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>Updated {entry.lastUpdated.toLocaleDateString()}</span>
                      </div>
                    </div>

                    {/* Status Indicators */}
                    <div className="flex items-center gap-2">
                      {isRecentlyUpdated && (
                        <Badge variant="default" className="text-xs">
                          Recently Updated
                        </Badge>
                      )}
                      {needsReview && (
                        <Badge variant="destructive" className="text-xs">
                          Needs Review
                        </Badge>
                      )}
                      {entry.complianceRelevant && (
                        <Badge variant="outline" className="text-xs">
                          Compliance
                        </Badge>
                      )}
                      {entry.auditEvidence && (
                        <Badge variant="outline" className="text-xs">
                          Audit Evidence
                        </Badge>
                      )}
                    </div>

                    {/* Tags */}
                    {entry.tags.length > 0 && (
                      <div className="flex gap-1">
                        {entry.tags.slice(0, 3).map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {entry.tags.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{entry.tags.length - 3}
                          </Badge>
                        )}
                      </div>
                    )}

                    {/* Related Items */}
                    {(relatedTasksCount > 0 || entry.relatedEntries.length > 0) && (
                      <div className="flex items-center gap-2 text-xs">
                        <span className="text-muted-foreground">Related:</span>
                        {relatedTasksCount > 0 && (
                          <Badge variant="outline" className="text-xs">
                            {relatedTasksCount} tasks
                          </Badge>
                        )}
                        {entry.relatedEntries.length > 0 && (
                          <Badge variant="outline" className="text-xs">
                            {entry.relatedEntries.length} articles
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="flex items-center gap-1">
                    <Button variant="ghost" size="sm">
                      <Link className="h-3 w-3" />
                    </Button>
                    <Button variant="outline" size="sm">
                      View
                    </Button>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {filteredEntries.length === 0 && (
          <div className="text-center py-8">
            <BookOpen className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-2 text-sm font-medium">No knowledge entries found</h3>
            <p className="mt-1 text-sm text-muted-foreground">
              {searchQuery ? "Try adjusting your search criteria" : "No entries match the current filter."}
            </p>
          </div>
        )}

        {/* Knowledge Stats */}
        <div className="pt-4 border-t">
          <div className="grid gap-3 md:grid-cols-3 text-center">
            <div>
              <div className="text-lg font-bold">{knowledgeEntries.length}</div>
              <p className="text-xs text-muted-foreground">Total Entries</p>
            </div>
            <div>
              <div className="text-lg font-bold">
                {knowledgeEntries.filter(e => e.complianceRelevant).length}
              </div>
              <p className="text-xs text-muted-foreground">Compliance Related</p>
            </div>
            <div>
              <div className="text-lg font-bold">
                {knowledgeEntries.filter(e => e.nextReview < new Date()).length}
              </div>
              <p className="text-xs text-muted-foreground">Need Review</p>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="pt-4 border-t">
          <div className="flex items-center justify-between">
            <Button variant="outline" size="sm">
              <Lightbulb className="mr-2 h-4 w-4" />
              Suggest Content
            </Button>
            <Button variant="outline" size="sm">
              <BookOpen className="mr-2 h-4 w-4" />
              Browse All
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
