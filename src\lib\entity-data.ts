import { 
  Building2, 
  Building, 
  Users, 
  MapPin, 
  Briefcase, 
  Target,
  Globe,
  FolderOpen
} from "lucide-react"
import { Entity, EntityType } from "./entity-types"

// Sample entities for demonstration
export const sampleEntities: Entity[] = [
  {
    id: "auris-hq",
    name: "Auris Compliance",
    type: "headquarters",
    status: "active",
    plan: "enterprise",
    logo: Building2,
    description: "Global headquarters and primary entity",
    createdAt: new Date("2023-01-01"),
    updatedAt: new Date("2024-01-15"),
    settings: {
      timezone: "UTC",
      currency: "USD",
      locale: "en-US",
      complianceFrameworks: ["NIST CSF 2.0", "ISO 27001", "SOC 2"],
      riskTolerance: "medium"
    },
    stats: {
      totalAssets: 1247,
      complianceScore: 87,
      activeRisks: 12,
      lastAssessment: new Date("2024-01-10")
    }
  },
  {
    id: "auris-emea",
    name: "Auris EMEA",
    type: "region",
    status: "active", 
    plan: "professional",
    logo: Globe,
    description: "Europe, Middle East, and Africa operations",
    parentEntityId: "auris-hq",
    createdAt: new Date("2023-03-15"),
    updatedAt: new Date("2024-01-12"),
    settings: {
      timezone: "Europe/London",
      currency: "EUR",
      locale: "en-GB",
      complianceFrameworks: ["ISO 27001", "GDPR"],
      riskTolerance: "low"
    },
    stats: {
      totalAssets: 456,
      complianceScore: 92,
      activeRisks: 3,
      lastAssessment: new Date("2024-01-08")
    }
  },
  {
    id: "auris-security",
    name: "Security Department",
    type: "department",
    status: "active",
    plan: "professional",
    logo: Users,
    description: "Information Security and Cybersecurity operations",
    parentEntityId: "auris-hq",
    createdAt: new Date("2023-02-01"),
    updatedAt: new Date("2024-01-14"),
    settings: {
      timezone: "UTC",
      currency: "USD", 
      locale: "en-US",
      complianceFrameworks: ["NIST CSF 2.0", "ISO 27001"],
      riskTolerance: "low"
    },
    stats: {
      totalAssets: 234,
      complianceScore: 95,
      activeRisks: 2,
      lastAssessment: new Date("2024-01-12")
    }
  },
  {
    id: "auris-fintech",
    name: "FinTech Division",
    type: "division",
    status: "active",
    plan: "enterprise",
    logo: Briefcase,
    description: "Financial technology products and services",
    parentEntityId: "auris-hq",
    createdAt: new Date("2023-06-01"),
    updatedAt: new Date("2024-01-13"),
    settings: {
      timezone: "America/New_York",
      currency: "USD",
      locale: "en-US", 
      complianceFrameworks: ["PCI DSS", "SOC 2", "NIST CSF 2.0"],
      riskTolerance: "medium"
    },
    stats: {
      totalAssets: 789,
      complianceScore: 89,
      activeRisks: 8,
      lastAssessment: new Date("2024-01-09")
    }
  },
  {
    id: "auris-london",
    name: "London Branch",
    type: "branch",
    status: "active",
    plan: "standard",
    logo: MapPin,
    description: "UK operations and compliance",
    parentEntityId: "auris-emea",
    createdAt: new Date("2023-04-01"),
    updatedAt: new Date("2024-01-11"),
    settings: {
      timezone: "Europe/London",
      currency: "GBP",
      locale: "en-GB",
      complianceFrameworks: ["ISO 27001", "GDPR"],
      riskTolerance: "low"
    },
    stats: {
      totalAssets: 156,
      complianceScore: 94,
      activeRisks: 1,
      lastAssessment: new Date("2024-01-07")
    }
  },
  {
    id: "project-alpha",
    name: "Project Alpha",
    type: "project",
    status: "setup",
    plan: "basic",
    logo: Target,
    description: "New product development initiative",
    parentEntityId: "auris-fintech",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-15"),
    settings: {
      timezone: "America/New_York",
      currency: "USD",
      locale: "en-US",
      complianceFrameworks: ["SOC 2"],
      riskTolerance: "high"
    },
    stats: {
      totalAssets: 23,
      complianceScore: 45,
      activeRisks: 5,
      lastAssessment: undefined
    }
  }
]

// Entity type icons mapping
export const ENTITY_TYPE_ICONS: Record<EntityType, typeof Building2> = {
  "headquarters": Building2,
  "subsidiary": Building,
  "department": Users,
  "branch": MapPin,
  "division": Briefcase,
  "business-unit": FolderOpen,
  "region": Globe,
  "project": Target
}

// Get entities by parent
export function getChildEntities(parentId: string): Entity[] {
  return sampleEntities.filter(entity => entity.parentEntityId === parentId)
}

// Get root entities (no parent)
export function getRootEntities(): Entity[] {
  return sampleEntities.filter(entity => !entity.parentEntityId)
}

// Get entity by ID
export function getEntityById(id: string): Entity | undefined {
  return sampleEntities.find(entity => entity.id === id)
}

// Get entity hierarchy
export function getEntityHierarchy(): Entity[] {
  const buildHierarchy = (parentId?: string): Entity[] => {
    return sampleEntities
      .filter(entity => entity.parentEntityId === parentId)
      .sort((a, b) => a.name.localeCompare(b.name))
  }
  
  return buildHierarchy()
}
