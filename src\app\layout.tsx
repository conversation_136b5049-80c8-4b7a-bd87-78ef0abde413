import type { Metadata } from "next";
import { Wix_Madefor_Text, Wix_Madefor_Display } from "next/font/google";
import "./globals.css";
import { ConditionalLayout } from "@/components/conditional-layout";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "sonner";

const wixMadeforText = Wix_Madefor_Text({
  variable: "--font-wix-madefor",
  subsets: ["latin"],
  display: "swap",
});

const wixMadeforDisplay = Wix_Madefor_Display({
  variable: "--font-wix-madefor-display",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "GRCOS - GRC Orchestration System",
  description: "Comprehensive GRC operations platform based on the GRC Ops Framework",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${wixMadeforText.variable} ${wixMadeforDisplay.variable} antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <ConditionalLayout>
            {children}
          </ConditionalLayout>
          <Toaster
            theme="system"
            position="top-center"
            expand={false}
            richColors
            closeButton
          />
        </ThemeProvider>
      </body>
    </html>
  );
}
