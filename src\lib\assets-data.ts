import { Asset, ITAsset, OTAsset, IoTAsset, IdentityAsset, ApplicationAsset, VendorAsset, ProcessAsset, AssetDiscovery } from "@/types/assets"

// Sample IT Assets
export const sampleITAssets: ITAsset[] = [
  {
    id: "it-001",
    name: "PROD-WEB-01",
    description: "Primary production web server",
    category: "it",
    assetType: "server",
    status: "active",
    criticalityLevel: "critical",
    securityStatus: "compliant",
    lastSeen: new Date("2024-01-30T14:30:00Z"),
    lastUpdated: new Date("2024-01-30T14:30:00Z"),
    riskScore: 25,
    complianceStatus: {
      "NIST CSF 2.0": { status: "compliant", lastAssessment: new Date("2024-01-15"), gaps: 0 },
      "ISO 27001": { status: "compliant", lastAssessment: new Date("2024-01-10"), gaps: 1 },
      "SOC 2": { status: "compliant", lastAssessment: new Date("2024-01-20"), gaps: 0 }
    },
    owner: "IT Operations",
    responsibleParty: "<EMAIL>",
    businessFunction: "Customer Portal",
    location: "Data Center A",
    entityId: "auris-hq",
    discoveryMethod: "automated",
    tags: ["production", "web-server", "critical"],
    createdAt: new Date("2023-06-01"),
    updatedAt: new Date("2024-01-30"),
    createdBy: "system",
    hardware: {
      manufacturer: "Dell",
      model: "PowerEdge R750",
      serialNumber: "DL7501234567",
      assetTag: "AT-001",
      warrantyExpiration: new Date("2026-06-01"),
      purchaseDate: new Date("2023-06-01"),
      cost: 15000
    },
    software: {
      operatingSystem: "Ubuntu Server",
      osVersion: "22.04 LTS",
      installedSoftware: ["nginx", "nodejs", "postgresql", "redis"],
      licenses: ["Ubuntu Pro", "PostgreSQL Enterprise"],
      patches: ["security-patch-2024-01", "kernel-update-5.15.0"],
      lastPatchDate: new Date("2024-01-25")
    },
    network: {
      ipAddress: "*********",
      macAddress: "00:1B:21:12:34:56",
      hostname: "prod-web-01.auris.local",
      domain: "auris.local",
      networkSegment: "DMZ",
      ports: [80, 443, 22]
    },
    specifications: {
      cpu: "Intel Xeon Silver 4314 (16 cores)",
      memory: "64GB DDR4",
      storage: "2TB NVMe SSD",
      networkInterfaces: ["1Gbps Ethernet", "10Gbps Fiber"]
    }
  },
  {
    id: "it-002",
    name: "DEV-WORKSTATION-05",
    description: "Developer workstation for security team",
    category: "it",
    assetType: "workstation",
    status: "active",
    criticalityLevel: "medium",
    securityStatus: "compliant",
    lastSeen: new Date("2024-01-30T09:15:00Z"),
    lastUpdated: new Date("2024-01-30T09:15:00Z"),
    riskScore: 35,
    complianceStatus: {
      "NIST CSF 2.0": { status: "compliant", lastAssessment: new Date("2024-01-15"), gaps: 2 },
      "ISO 27001": { status: "non-compliant", lastAssessment: new Date("2024-01-10"), gaps: 3 }
    },
    owner: "Security Team",
    responsibleParty: "<EMAIL>",
    businessFunction: "Security Development",
    location: "Office Floor 3",
    entityId: "auris-hq",
    discoveryMethod: "agent",
    tags: ["development", "security", "workstation"],
    createdAt: new Date("2023-09-15"),
    updatedAt: new Date("2024-01-30"),
    createdBy: "<EMAIL>",
    hardware: {
      manufacturer: "Lenovo",
      model: "ThinkStation P360",
      serialNumber: "LN3601234567",
      assetTag: "AT-002"
    },
    software: {
      operatingSystem: "Windows 11 Pro",
      osVersion: "22H2",
      installedSoftware: ["Visual Studio Code", "Docker Desktop", "Burp Suite", "Wireshark"],
      licenses: ["Windows 11 Pro", "Burp Suite Professional"],
      patches: ["*********", "*********"],
      lastPatchDate: new Date("2024-01-28")
    },
    network: {
      ipAddress: "*********",
      macAddress: "00:1B:21:98:76:54",
      hostname: "dev-ws-05.auris.local",
      domain: "auris.local",
      networkSegment: "Internal"
    }
  }
]

// Sample OT Assets
export const sampleOTAssets: OTAsset[] = [
  {
    id: "ot-001",
    name: "PLC-LINE-A-01",
    description: "Primary PLC controlling production line A",
    category: "ot",
    assetType: "plc",
    status: "active",
    criticalityLevel: "critical",
    securityStatus: "non-compliant",
    lastSeen: new Date("2024-01-30T12:00:00Z"),
    lastUpdated: new Date("2024-01-30T12:00:00Z"),
    riskScore: 75,
    complianceStatus: {
      "NIST CSF 2.0": { status: "non-compliant", lastAssessment: new Date("2024-01-15"), gaps: 8 },
      "IEC 62443": { status: "non-compliant", lastAssessment: new Date("2024-01-10"), gaps: 12 }
    },
    owner: "Manufacturing Operations",
    responsibleParty: "<EMAIL>",
    businessFunction: "Production Line A",
    location: "Manufacturing Floor 1",
    entityId: "auris-manufacturing",
    discoveryMethod: "manual",
    tags: ["production", "plc", "critical", "legacy"],
    createdAt: new Date("2020-03-01"),
    updatedAt: new Date("2024-01-30"),
    createdBy: "<EMAIL>",
    industrial: {
      protocol: "Modbus TCP",
      firmwareVersion: "v2.1.5",
      vendor: "Schneider Electric",
      model: "Modicon M580",
      serialNumber: "SE580123456",
      installationDate: new Date("2020-03-01"),
      maintenanceSchedule: "Quarterly",
      safetyRating: "SIL 2",
      operatingConditions: "24/7 continuous operation"
    },
    connectivity: {
      networkType: "Industrial Ethernet",
      communicationProtocol: "Modbus TCP/IP",
      remoteAccess: true,
      encryptionEnabled: false
    },
    operational: {
      productionLine: "Line A",
      facility: "Manufacturing Plant 1",
      operationalHours: "24/7",
      maintenanceWindow: "Sunday 2-6 AM"
    }
  }
]

// Sample IoT Assets
export const sampleIoTAssets: IoTAsset[] = [
  {
    id: "iot-001",
    name: "TEMP-SENSOR-DC-A-01",
    description: "Temperature sensor in data center A",
    category: "iot",
    assetType: "environmental-monitor",
    status: "active",
    criticalityLevel: "medium",
    securityStatus: "compliant",
    lastSeen: new Date("2024-01-30T14:25:00Z"),
    lastUpdated: new Date("2024-01-30T14:25:00Z"),
    riskScore: 20,
    complianceStatus: {
      "NIST CSF 2.0": { status: "compliant", lastAssessment: new Date("2024-01-15"), gaps: 0 }
    },
    owner: "Facilities Management",
    responsibleParty: "<EMAIL>",
    businessFunction: "Environmental Monitoring",
    location: "Data Center A - Rack 15",
    entityId: "auris-hq",
    discoveryMethod: "automated",
    tags: ["sensor", "temperature", "datacenter"],
    createdAt: new Date("2023-08-15"),
    updatedAt: new Date("2024-01-30"),
    createdBy: "<EMAIL>",
    device: {
      manufacturer: "Sensirion",
      model: "SHT85",
      firmwareVersion: "v1.2.3",
      hardwareVersion: "v2.0",
      deviceId: "SHT85-001",
      batteryLevel: 85,
      lastCommunication: new Date("2024-01-30T14:25:00Z")
    },
    connectivity: {
      connectionType: "WiFi",
      signalStrength: -45,
      dataUsage: 2.5,
      encryptionEnabled: true
    },
    sensors: {
      sensorTypes: ["temperature", "humidity"],
      dataCollectionFrequency: "Every 5 minutes",
      dataRetention: "1 year"
    }
  }
]

// Sample Identity Assets
export const sampleIdentityAssets: IdentityAsset[] = [
  {
    id: "id-001",
    name: "admin.user",
    description: "System administrator account",
    category: "identity",
    assetType: "privileged-account",
    status: "active",
    criticalityLevel: "critical",
    securityStatus: "compliant",
    lastSeen: new Date("2024-01-30T08:30:00Z"),
    lastUpdated: new Date("2024-01-30T08:30:00Z"),
    riskScore: 45,
    complianceStatus: {
      "NIST CSF 2.0": { status: "compliant", lastAssessment: new Date("2024-01-15"), gaps: 0 },
      "ISO 27001": { status: "compliant", lastAssessment: new Date("2024-01-10"), gaps: 0 }
    },
    owner: "IT Security",
    responsibleParty: "<EMAIL>",
    businessFunction: "System Administration",
    entityId: "auris-hq",
    discoveryMethod: "automated",
    tags: ["privileged", "admin", "critical"],
    createdAt: new Date("2023-01-01"),
    updatedAt: new Date("2024-01-30"),
    createdBy: "system",
    account: {
      username: "admin.user",
      accountType: "privileged",
      domain: "auris.local",
      lastLogin: new Date("2024-01-30T08:30:00Z"),
      passwordLastChanged: new Date("2024-01-15T10:00:00Z"),
      mfaEnabled: true,
      accountLocked: false
    },
    permissions: {
      roles: ["Domain Admin", "Enterprise Admin"],
      groups: ["IT Administrators", "Security Team"],
      privileges: ["Full Control", "Backup Operator", "Log on as Service"],
      accessLevel: "Administrative",
      dataAccess: ["All Systems", "Security Logs", "Configuration Data"]
    },
    activity: {
      loginHistory: [
        new Date("2024-01-30T08:30:00Z"),
        new Date("2024-01-29T09:15:00Z"),
        new Date("2024-01-28T14:20:00Z")
      ],
      failedLogins: 0,
      suspiciousActivity: false,
      lastPasswordChange: new Date("2024-01-15T10:00:00Z")
    }
  }
]

// Sample Application Assets
export const sampleApplicationAssets: ApplicationAsset[] = [
  {
    id: "app-001",
    name: "Customer Portal",
    description: "Main customer-facing web application",
    category: "application",
    assetType: "web-application",
    status: "active",
    criticalityLevel: "critical",
    securityStatus: "compliant",
    lastSeen: new Date("2024-01-30T14:30:00Z"),
    lastUpdated: new Date("2024-01-30T14:30:00Z"),
    riskScore: 30,
    complianceStatus: {
      "NIST CSF 2.0": { status: "compliant", lastAssessment: new Date("2024-01-15"), gaps: 1 },
      "SOC 2": { status: "compliant", lastAssessment: new Date("2024-01-20"), gaps: 0 }
    },
    owner: "Product Team",
    responsibleParty: "<EMAIL>",
    businessFunction: "Customer Service",
    entityId: "auris-hq",
    discoveryMethod: "manual",
    tags: ["customer-facing", "web-app", "production"],
    createdAt: new Date("2023-03-01"),
    updatedAt: new Date("2024-01-30"),
    createdBy: "<EMAIL>",
    application: {
      version: "v2.1.4",
      vendor: "Internal Development",
      licenseType: "Proprietary",
      deploymentType: "cloud",
      url: "https://portal.auris.com",
      apiEndpoints: ["/api/v1/users", "/api/v1/orders", "/api/v1/support"]
    },
    security: {
      authenticationMethod: "OAuth 2.0 + MFA",
      encryptionInTransit: true,
      encryptionAtRest: true,
      vulnerabilities: ["CVE-2024-0001"],
      lastSecurityScan: new Date("2024-01-25"),
      securityRating: "A"
    },
    dependencies: {
      databases: ["PostgreSQL Primary", "Redis Cache"],
      services: ["Authentication Service", "Payment Gateway"],
      integrations: ["Salesforce", "Stripe", "SendGrid"],
      thirdPartyComponents: ["React", "Node.js", "Express"]
    },
    performance: {
      availability: 99.9,
      responseTime: 250,
      throughput: 1000,
      errorRate: 0.1
    }
  }
]

// Sample Vendor Assets
export const sampleVendorAssets: VendorAsset[] = [
  {
    id: "vendor-001",
    name: "CloudSecure Inc.",
    description: "Cloud security services provider",
    category: "vendor",
    assetType: "service-provider",
    status: "active",
    criticalityLevel: "high",
    securityStatus: "compliant",
    lastSeen: new Date("2024-01-30T10:00:00Z"),
    lastUpdated: new Date("2024-01-30T10:00:00Z"),
    riskScore: 25,
    complianceStatus: {
      "NIST CSF 2.0": { status: "compliant", lastAssessment: new Date("2024-01-15"), gaps: 0 },
      "SOC 2": { status: "compliant", lastAssessment: new Date("2024-01-10"), gaps: 0 }
    },
    owner: "Procurement",
    responsibleParty: "<EMAIL>",
    businessFunction: "Security Services",
    entityId: "auris-hq",
    discoveryMethod: "manual",
    tags: ["security", "cloud", "critical-vendor"],
    createdAt: new Date("2023-05-01"),
    updatedAt: new Date("2024-01-30"),
    createdBy: "<EMAIL>",
    vendor: {
      companyName: "CloudSecure Inc.",
      contactPerson: "John Smith",
      email: "<EMAIL>",
      phone: "******-0123",
      address: "123 Security Blvd, Tech City, TC 12345",
      website: "https://cloudsecure.com",
      vendorType: "Technology Vendor"
    },
    contract: {
      contractNumber: "CS-2023-001",
      startDate: new Date("2023-05-01"),
      endDate: new Date("2025-04-30"),
      renewalDate: new Date("2025-02-01"),
      contractValue: 250000,
      paymentTerms: "Net 30",
      slaRequirements: ["99.9% uptime", "4-hour response time", "24/7 support"]
    },
    risk: {
      riskAssessmentDate: new Date("2024-01-15"),
      riskLevel: "low",
      riskFactors: ["Third-party access", "Data processing"],
      mitigationMeasures: ["Regular audits", "Contractual controls", "Monitoring"],
      insuranceCoverage: true,
      backgroundCheckCompleted: true
    },
    compliance: {
      certifications: ["SOC 2 Type II", "ISO 27001", "FedRAMP"],
      auditDate: new Date("2024-01-10"),
      complianceGaps: [],
      remediation: []
    }
  }
]

// Sample Process Assets
export const sampleProcessAssets: ProcessAsset[] = [
  {
    id: "process-001",
    name: "Incident Response Process",
    description: "Security incident response and management process",
    category: "process",
    assetType: "security-process",
    status: "active",
    criticalityLevel: "critical",
    securityStatus: "compliant",
    lastSeen: new Date("2024-01-30T16:00:00Z"),
    lastUpdated: new Date("2024-01-30T16:00:00Z"),
    riskScore: 20,
    complianceStatus: {
      "NIST CSF 2.0": { status: "compliant", lastAssessment: new Date("2024-01-15"), gaps: 0 },
      "ISO 27001": { status: "compliant", lastAssessment: new Date("2024-01-10"), gaps: 1 }
    },
    owner: "CISO",
    responsibleParty: "<EMAIL>",
    businessFunction: "Security Operations",
    entityId: "auris-hq",
    discoveryMethod: "manual",
    tags: ["security", "incident-response", "critical"],
    createdAt: new Date("2023-02-01"),
    updatedAt: new Date("2024-01-30"),
    createdBy: "<EMAIL>",
    process: {
      processType: "Security Process",
      processOwner: "CISO",
      processDescription: "Comprehensive incident response process for security events",
      inputs: ["Security alerts", "Threat intelligence", "User reports"],
      outputs: ["Incident reports", "Remediation actions", "Lessons learned"],
      controls: ["Access controls", "Logging", "Escalation procedures"],
      risks: ["Delayed response", "Inadequate containment", "Data loss"]
    },
    workflow: {
      steps: [
        {
          id: "step-001",
          name: "Detection",
          description: "Identify and validate security incident",
          order: 1,
          responsible: "SOC Analyst",
          duration: 15,
          automated: true,
          controls: ["SIEM monitoring", "Alert validation"]
        },
        {
          id: "step-002",
          name: "Classification",
          description: "Classify incident severity and type",
          order: 2,
          responsible: "Security Engineer",
          duration: 30,
          automated: false,
          controls: ["Classification matrix", "Escalation criteria"]
        }
      ],
      approvals: ["Security Manager", "CISO"],
      automation: true,
      slaRequirements: ["15-minute detection", "1-hour response", "24-hour resolution"]
    },
    compliance: {
      applicableFrameworks: ["NIST CSF 2.0", "ISO 27001", "SOC 2"],
      controlMappings: ["RS.RP-1", "RS.CO-1", "RS.AN-1"],
      auditFrequency: "Annual",
      lastAudit: new Date("2024-01-15")
    },
    dependencies: {
      upstreamProcesses: ["Threat Detection", "Vulnerability Management"],
      downstreamProcesses: ["Forensic Analysis", "Business Continuity"],
      supportingAssets: ["SIEM System", "Incident Management Tool"],
      criticalDependencies: ["Security Team", "IT Operations"]
    }
  }
]

// Combine all assets for easy access
export const sampleAssets: Asset[] = [
  ...sampleITAssets,
  ...sampleOTAssets,
  ...sampleIoTAssets,
  ...sampleIdentityAssets,
  ...sampleApplicationAssets,
  ...sampleVendorAssets,
  ...sampleProcessAssets
]

// Sample asset discovery data
export const sampleAssetDiscovery: AssetDiscovery[] = [
  {
    id: "discovery-001",
    discoveryType: "network-scan",
    status: "completed",
    startTime: new Date("2024-01-30T02:00:00Z"),
    endTime: new Date("2024-01-30T02:45:00Z"),
    assetsDiscovered: 15,
    assetsUpdated: 8,
    errors: [],
    configuration: {
      scanRange: "10.0.0.0/16",
      ports: [22, 80, 443, 3389],
      timeout: 5000
    },
    nextRun: new Date("2024-01-31T02:00:00Z")
  },
  {
    id: "discovery-002",
    discoveryType: "agent-based",
    status: "running",
    startTime: new Date("2024-01-30T14:00:00Z"),
    assetsDiscovered: 0,
    assetsUpdated: 0,
    errors: [],
    configuration: {
      agentVersion: "v2.1.0",
      reportingInterval: 300,
      dataCollection: ["hardware", "software", "network"]
    }
  }
]

// Asset statistics for dashboard
export const assetStats = {
  totalAssets: sampleAssets.length,
  assetsByCategory: {
    it: sampleITAssets.length,
    ot: sampleOTAssets.length,
    iot: sampleIoTAssets.length,
    identity: sampleIdentityAssets.length,
    application: sampleApplicationAssets.length,
    vendor: sampleVendorAssets.length,
    process: sampleProcessAssets.length
  },
  criticalAssets: sampleAssets.filter(a => a.criticalityLevel === "critical").length,
  nonCompliantAssets: sampleAssets.filter(a => a.securityStatus === "non-compliant").length,
  highRiskAssets: sampleAssets.filter(a => a.riskScore >= 70).length,
  averageRiskScore: Math.round(sampleAssets.reduce((sum, a) => sum + a.riskScore, 0) / sampleAssets.length),
  lastDiscovery: new Date("2024-01-30T02:45:00Z"),
  discoveryHealth: 95
}
