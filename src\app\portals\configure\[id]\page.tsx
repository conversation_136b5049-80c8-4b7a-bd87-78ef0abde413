"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"

import { ConfiguratorLayout, ConfigSection } from "@/components/portals/configurator/configurator-layout"
import { GeneralSettings } from "@/components/portals/configurator/general-settings"
import { AccessSecurity } from "@/components/portals/configurator/access-security"
import { PortalConfiguration } from "@/types/portal"

// Mock initial configuration data
const getInitialConfig = (id: string): PortalConfiguration => {
  if (id === 'new') {
    return {
      id: '',
      portalId: '',
      general: {
        name: '',
        description: '',
        stakeholderType: 'customer',
        branding: {
          primaryColor: '#3b82f6',
          secondaryColor: '#1e40af'
        }
      },
      access: {
        authMethod: 'email',
        allowedDomains: [],
        sessionTimeout: 3600,
        mfaRequired: false
      },
      content: {
        frameworks: [],
        controls: [],
        exposedData: [],
        customFields: {}
      },
      dashboard: {
        layout: 'grid',
        widgets: [],
        customizations: {}
      },
      chatbot: {
        enabled: false,
        model: 'gpt-4',
        personality: 'professional',
        knowledgeBase: []
      },
      api: {
        enabled: false,
        endpoints: [],
        rateLimit: 100
      },
      questionnaire: {
        enabled: false,
        templates: [],
        autoSubmit: false
      },
      stakeholders: {
        users: [],
        groups: []
      }
    }
  }

  // Mock existing portal configuration
  return {
    id: id,
    portalId: id,
    general: {
      name: 'SOC 2 Auditor Portal',
      description: 'Dedicated portal for SOC 2 compliance auditors',
      stakeholderType: 'auditor',
      branding: {
        logo: '/logos/soc2-portal.png',
        primaryColor: '#3b82f6',
        secondaryColor: '#1e40af'
      }
    },
    access: {
      authMethod: 'sso',
      allowedDomains: ['auditor.com', 'compliance.org'],
      sessionTimeout: 7200,
      mfaRequired: true
    },
    content: {
      frameworks: ['SOC 2', 'ISO 27001'],
      controls: ['AC-1', 'AC-2', 'SC-1'],
      exposedData: ['controls', 'evidence', 'reports'],
      customFields: {}
    },
    dashboard: {
      layout: 'grid',
      widgets: ['compliance-status', 'recent-evidence', 'audit-timeline'],
      customizations: {}
    },
    chatbot: {
      enabled: true,
      model: 'gpt-4',
      personality: 'professional',
      knowledgeBase: ['soc2-guide', 'control-library']
    },
    api: {
      enabled: true,
      endpoints: ['/api/controls', '/api/evidence'],
      rateLimit: 1000
    },
    questionnaire: {
      enabled: false,
      templates: [],
      autoSubmit: false
    },
    stakeholders: {
      users: [
        { email: '<EMAIL>', role: 'auditor', permissions: ['read', 'download'] }
      ],
      groups: [
        { name: 'External Auditors', permissions: ['read', 'download', 'comment'] }
      ]
    }
  }
}

export default function PortalConfiguratorPage() {
  const params = useParams()
  const router = useRouter()
  const portalId = params.id as string
  
  const [currentSection, setCurrentSection] = useState<ConfigSection>('general')
  const [configuration, setConfiguration] = useState<PortalConfiguration>(() => 
    getInitialConfig(portalId)
  )
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [validationErrors, setValidationErrors] = useState<string[]>([])

  useEffect(() => {
    // Validate configuration whenever it changes
    const errors: string[] = []

    if (!configuration.general.name.trim()) {
      errors.push('Portal name is required')
    }

    if (!configuration.general.description.trim()) {
      errors.push('Portal description is required')
    }

    if (configuration.access.sessionTimeout < 300) {
      errors.push('Session timeout must be at least 5 minutes')
    }

    if (configuration.access.sessionTimeout > 86400) {
      errors.push('Session timeout cannot exceed 24 hours')
    }

    setValidationErrors(errors)
  }, [configuration])

  const handleConfigurationChange = (section: keyof PortalConfiguration, data: any) => {
    setConfiguration(prev => ({
      ...prev,
      [section]: data
    }))
    setHasUnsavedChanges(true)
  }

  const handleSave = async () => {
    try {
      // In a real app, this would make an API call
      console.log('Saving configuration:', configuration)
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setHasUnsavedChanges(false)
    } catch (error) {
      console.error('Failed to save configuration:', error)
    }
  }

  const handlePreview = () => {
    // In a real app, this would open a preview of the portal
    console.log('Opening preview for:', configuration)
    window.open(`/portals/preview/${portalId}`, '_blank')
  }

  const handleDeploy = async () => {
    if (validationErrors.length > 0) {
      return
    }

    try {
      // In a real app, this would deploy the portal
      console.log('Deploying portal:', configuration)
      
      // Simulate deployment
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Redirect to portal dashboard
      router.push('/portals')
    } catch (error) {
      console.error('Failed to deploy portal:', error)
    }
  }

  const renderCurrentSection = () => {
    switch (currentSection) {
      case 'general':
        return (
          <GeneralSettings
            data={configuration.general}
            onChange={(data) => handleConfigurationChange('general', data)}
          />
        )
      case 'access':
        return (
          <AccessSecurity
            data={configuration.access}
            onChange={(data) => handleConfigurationChange('access', data)}
          />
        )
      case 'content':
        return (
          <div className="p-8 text-center text-muted-foreground">
            <h3 className="text-lg font-medium mb-2">Content & Data Configuration</h3>
            <p>This section is under development.</p>
          </div>
        )
      case 'dashboard':
        return (
          <div className="p-8 text-center text-muted-foreground">
            <h3 className="text-lg font-medium mb-2">Dashboard Design</h3>
            <p>This section is under development.</p>
          </div>
        )
      case 'chatbot':
        return (
          <div className="p-8 text-center text-muted-foreground">
            <h3 className="text-lg font-medium mb-2">Chatbot Configuration</h3>
            <p>This section is under development.</p>
          </div>
        )
      case 'api':
        return (
          <div className="p-8 text-center text-muted-foreground">
            <h3 className="text-lg font-medium mb-2">API Settings</h3>
            <p>This section is under development.</p>
          </div>
        )
      case 'questionnaire':
        return (
          <div className="p-8 text-center text-muted-foreground">
            <h3 className="text-lg font-medium mb-2">Questionnaire Automation</h3>
            <p>This section is under development.</p>
          </div>
        )
      case 'stakeholders':
        return (
          <div className="p-8 text-center text-muted-foreground">
            <h3 className="text-lg font-medium mb-2">Stakeholder Management</h3>
            <p>This section is under development.</p>
          </div>
        )
      default:
        return null
    }
  }

  return (
    <ConfiguratorLayout
      portalId={portalId}
      portalName={configuration.general.name || 'New Portal'}
      currentSection={currentSection}
      onSectionChange={setCurrentSection}
      isDraft={portalId === 'new'}
      hasUnsavedChanges={hasUnsavedChanges}
      validationErrors={validationErrors}
      onSave={handleSave}
      onPreview={handlePreview}
      onDeploy={handleDeploy}
    >
      {renderCurrentSection()}
    </ConfiguratorLayout>
  )
}
