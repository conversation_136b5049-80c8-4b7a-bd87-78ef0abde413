"use client"

import React from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { 
  Users, 
  Clock, 
  Activity, 
  Award,
  Phone,
  Mail,
  MessageSquare,
  Calendar
} from "lucide-react"

import { sampleTeamMembers, remediationMetrics } from "@/lib/remediation-data"

const availabilityConfig = {
  available: { label: "Available", variant: "default" as const, color: "text-green-600" },
  busy: { label: "Busy", variant: "secondary" as const, color: "text-yellow-600" },
  away: { label: "Away", variant: "outline" as const, color: "text-gray-600" },
  offline: { label: "Offline", variant: "outline" as const, color: "text-red-600" },
}

const roleConfig = {
  analyst: { label: "Analyst", color: "bg-blue-100 text-blue-800" },
  "senior-analyst": { label: "Senior Analyst", color: "bg-purple-100 text-purple-800" },
  lead: { label: "Lead", color: "bg-green-100 text-green-800" },
  manager: { label: "Manager", color: "bg-orange-100 text-orange-800" },
  director: { label: "Director", color: "bg-red-100 text-red-800" },
  specialist: { label: "Specialist", color: "bg-indigo-100 text-indigo-800" },
}

export function TeamResourceStatus() {
  return (
    <div className="space-y-6">
      {/* Team Overview */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Active Analysts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{remediationMetrics.team.activeAnalysts}</div>
            <p className="text-xs text-muted-foreground">
              {sampleTeamMembers.filter(m => m.availability === "available").length} available
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Team Workload</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{remediationMetrics.team.workload}%</div>
            <Progress value={remediationMetrics.team.workload} className="mt-2 h-2" />
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Avg Case Load</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{remediationMetrics.team.avgCaseLoad}</div>
            <p className="text-xs text-muted-foreground">cases per analyst</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Certifications</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{remediationMetrics.team.certifications}</div>
            <p className="text-xs text-muted-foreground">total team certs</p>
          </CardContent>
        </Card>
      </div>

      {/* Team Members */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Team Members
          </CardTitle>
          <CardDescription>Current team status and availability</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {sampleTeamMembers.map((member) => {
              const availabilityInfo = availabilityConfig[member.availability]
              const roleInfo = roleConfig[member.role]
              const workloadPercentage = (member.currentCases / member.maxCaseLoad) * 100
              
              return (
                <div key={member.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{member.name}</h4>
                        <Badge className={roleInfo.color}>
                          {roleInfo.label}
                        </Badge>
                        <Badge variant={availabilityInfo.variant} className={availabilityInfo.color}>
                          {availabilityInfo.label}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{member.email}</p>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>Cases: {member.currentCases}/{member.maxCaseLoad}</span>
                        <span>Last active: {new Date(member.lastActive).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    {/* Workload Progress */}
                    <div className="space-y-1">
                      <div className="text-sm font-medium">{Math.round(workloadPercentage)}%</div>
                      <Progress value={workloadPercentage} className="w-20 h-2" />
                    </div>
                    
                    {/* Contact Methods */}
                    <div className="flex items-center gap-1">
                      {member.contactMethods.map((contact, index) => (
                        <Button key={index} variant="ghost" size="sm" className="h-8 w-8 p-0">
                          {contact.type === "email" && <Mail className="h-3 w-3" />}
                          {contact.type === "phone" && <Phone className="h-3 w-3" />}
                          {contact.type === "slack" && <MessageSquare className="h-3 w-3" />}
                          {contact.type === "teams" && <MessageSquare className="h-3 w-3" />}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Skills and Certifications */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Team Skills
            </CardTitle>
            <CardDescription>Core competencies and expertise areas</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Array.from(new Set(sampleTeamMembers.flatMap(m => m.skills))).map((skill) => {
                const memberCount = sampleTeamMembers.filter(m => m.skills.includes(skill)).length
                const percentage = (memberCount / sampleTeamMembers.length) * 100
                
                return (
                  <div key={skill} className="flex items-center justify-between">
                    <span className="text-sm">{skill}</span>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-muted rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full" 
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium">{memberCount}</span>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Certifications
            </CardTitle>
            <CardDescription>Professional certifications held by team</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Array.from(new Set(sampleTeamMembers.flatMap(m => m.certifications))).map((cert) => {
                const memberCount = sampleTeamMembers.filter(m => m.certifications.includes(cert)).length
                
                return (
                  <div key={cert} className="flex items-center justify-between">
                    <span className="text-sm">{cert}</span>
                    <Badge variant="outline">{memberCount}</Badge>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Shift Schedule */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Shift Coverage
          </CardTitle>
          <CardDescription>Current shift schedule and coverage status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-7">
            {["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"].map((day) => (
              <div key={day} className="space-y-2">
                <h4 className="text-sm font-medium">{day}</h4>
                <div className="space-y-1">
                  {sampleTeamMembers.filter(m => 
                    m.shiftSchedule.shifts.some(s => s.day === day)
                  ).map((member) => {
                    const shift = member.shiftSchedule.shifts.find(s => s.day === day)
                    return (
                      <div key={member.id} className="text-xs p-2 bg-muted rounded">
                        <div className="font-medium">{member.name}</div>
                        <div className="text-muted-foreground">
                          {shift?.start} - {shift?.end}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
