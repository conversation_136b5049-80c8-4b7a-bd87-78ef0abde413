@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {

    --background: 0 0% 100%;

    --foreground: 0 0% 5%;

    --card: 0 0% 100%;

    --card-foreground: 0 0% 5%;

    --popover: 0 0% 100%;

    --popover-foreground: 0 0% 5%;

    --primary: 0 0% 15%;

    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96%;

    --secondary-foreground: 0 0% 15%;

    --muted: 0 0% 96%;

    --muted-foreground: 0 0% 45%;

    --accent: 0 0% 96%;

    --accent-foreground: 0 0% 15%;

    --destructive: 0 84.2% 60.2%;

    --destructive-foreground: 210 40% 98%;

    --border: 0 0% 90%;

    --input: 0 0% 90%;

    --ring: 0 0% 60%;

    --chart-1: 12 76% 61%;

    --chart-2: 173 58% 39%;

    --chart-3: 197 37% 24%;

    --chart-4: 43 74% 66%;

    --chart-5: 27 87% 67%;

    --radius: 0.5rem
  ;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 0 0% 25%;

    --sidebar-primary: 0 0% 15%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 0 0% 95%;

    --sidebar-accent-foreground: 0 0% 15%;

    --sidebar-border: 0 0% 90%;

    --sidebar-ring: 0 0% 60%}
  .dark {

    --background: 0 0% 5%;

    --foreground: 0 0% 98%;

    --card: 0 0% 5%;

    --card-foreground: 0 0% 98%;

    --popover: 0 0% 5%;

    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 85%;

    --primary-foreground: 0 0% 15%;

    --secondary: 0 0% 15%;

    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 15%;

    --muted-foreground: 0 0% 65%;

    --accent: 0 0% 15%;

    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;

    --destructive-foreground: 210 40% 98%;

    --border: 0 0% 20%;

    --input: 0 0% 20%;

    --ring: 0 0% 70%;

    --chart-1: 220 70% 50%;

    --chart-2: 160 60% 45%;

    --chart-3: 30 80% 55%;

    --chart-4: 280 65% 60%;

    --chart-5: 340 75% 55%
  ;

    --sidebar-background: 0 0% 8%;

    --sidebar-foreground: 0 0% 95%;

    --sidebar-primary: 0 0% 85%;

    --sidebar-primary-foreground: 0 0% 15%;

    --sidebar-accent: 0 0% 20%;

    --sidebar-accent-foreground: 0 0% 90%;

    --sidebar-border: 0 0% 25%;

    --sidebar-ring: 0 0% 60%}
  .theme {

    --animate-shine: shine var(--duration) infinite linear
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-text;
  }

  /* Apply display font to headings */
  h1, h2, h3, h4, h5, h6 {
    @apply font-display;
  }

  /* Apply display font to specific UI elements */
  .font-display {
    font-family: var(--font-wix-madefor-display), system-ui, sans-serif;
  }

  .font-text {
    font-family: var(--font-wix-madefor), system-ui, sans-serif;
  }
}

/* Custom greytone theme enhancements */
@layer components {
  /* Subtle active states with greytone */
  .sidebar-active {
    @apply bg-gray-100 text-gray-900 border-l-2 border-gray-400;
  }

  .dark .sidebar-active {
    @apply bg-gray-800/60 text-gray-100 border-l-2 border-gray-500;
  }

  /* Subtle hover states */
  .sidebar-hover {
    @apply hover:bg-gray-50;
  }

  .dark .sidebar-hover {
    @apply hover:bg-gray-800/30;
  }

  /* Enhanced card styling for both themes */
  .card-enhanced {
    @apply bg-white/80 border-gray-200 shadow-sm;
  }

  .dark .card-enhanced {
    @apply bg-gray-900/60 border-gray-700 shadow-lg;
  }

  /* Refined button styling */
  .btn-primary {
    @apply bg-gray-900 hover:bg-gray-800 text-white;
  }

  .dark .btn-primary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-900;
  }

  /* Hide scrollbars while maintaining scroll functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }
}



/* Global Scrollbar Styling - Floating Design */
@layer base {
  /* Firefox scrollbar styling - floating effect */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.4) transparent;
  }

  /* Webkit browsers (Chrome, Safari, Edge) - floating scrollbar styling */
  *::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    /* Create floating effect by removing background from layout */
    background: transparent;
  }

  *::-webkit-scrollbar-track {
    /* Transparent track for floating effect */
    background: transparent;
    border-radius: 0;
    margin: 2px;
  }

  *::-webkit-scrollbar-thumb {
    /* Floating thumb with shadow and transparency */
    background: hsl(var(--muted-foreground) / 0.5);
    border-radius: 6px;
    border: none;
    /* Floating shadow effect */
    box-shadow:
      0 2px 4px hsl(var(--foreground) / 0.1),
      0 1px 2px hsl(var(--foreground) / 0.2),
      inset 0 1px 0 hsl(var(--background) / 0.1);
    /* Smooth transitions for interactions */
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    /* Create spacing from content edge */
    margin: 1px;
    min-height: 20px;
  }

  *::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.7);
    /* Enhanced shadow on hover */
    box-shadow:
      0 4px 8px hsl(var(--foreground) / 0.15),
      0 2px 4px hsl(var(--foreground) / 0.25),
      inset 0 1px 0 hsl(var(--background) / 0.2);
    /* Slight scale effect for floating feel */
    transform: scaleX(1.1);
  }

  *::-webkit-scrollbar-thumb:active {
    background: hsl(var(--muted-foreground) / 0.8);
    /* Pressed state shadow */
    box-shadow:
      0 1px 2px hsl(var(--foreground) / 0.2),
      inset 0 1px 2px hsl(var(--foreground) / 0.1);
    transform: scaleX(1.05);
  }

  *::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* Horizontal scrollbar specific styling */
  *::-webkit-scrollbar:horizontal {
    height: 6px;
  }

  *::-webkit-scrollbar-thumb:horizontal {
    min-width: 20px;
  }

  *::-webkit-scrollbar-thumb:horizontal:hover {
    transform: scaleY(1.1);
  }

  *::-webkit-scrollbar-thumb:horizontal:active {
    transform: scaleY(1.05);
  }

  /* Dark theme specific adjustments for floating scrollbars */
  .dark *::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.4);
    /* Enhanced shadow for dark theme */
    box-shadow:
      0 2px 4px hsl(0 0% 0% / 0.3),
      0 1px 2px hsl(0 0% 0% / 0.4),
      inset 0 1px 0 hsl(var(--background) / 0.1);
  }

  .dark *::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.6);
    /* Enhanced dark theme hover shadow */
    box-shadow:
      0 4px 8px hsl(0 0% 0% / 0.4),
      0 2px 4px hsl(0 0% 0% / 0.5),
      inset 0 1px 0 hsl(var(--background) / 0.2);
    transform: scaleX(1.1);
  }

  .dark *::-webkit-scrollbar-thumb:active {
    background: hsl(var(--muted-foreground) / 0.7);
    /* Dark theme pressed state */
    box-shadow:
      0 1px 2px hsl(0 0% 0% / 0.4),
      inset 0 1px 2px hsl(0 0% 0% / 0.2);
    transform: scaleX(1.05);
  }

  .dark *::-webkit-scrollbar-thumb:horizontal:hover {
    transform: scaleY(1.1);
  }

  .dark *::-webkit-scrollbar-thumb:horizontal:active {
    transform: scaleY(1.05);
  }

  /* Firefox dark theme - floating effect */
  .dark * {
    scrollbar-color: hsl(var(--muted-foreground) / 0.4) transparent;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@theme inline {
  @keyframes shine {
  0% {
    background-position: 0% 0%;
    }
  50% {
    background-position: 100% 100%;
    }
  to {
    background-position: 0% 0%;
    }
  }
}