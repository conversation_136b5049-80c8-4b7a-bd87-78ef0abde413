"use client"

import { useState } from "react"
import Link from "next/link"
import { Plus, Calendar, Clock, CheckCircle, AlertT<PERSON>gle, Target } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { StandardModuleLayout, ModuleLayoutConfigs, createSearchConfig } from "@/components/standard-module-layout"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

// Mock testing data
const mockTests = [
  {
    id: "T001",
    controlName: "User Access Review",
    testType: "Quarterly Review",
    frequency: "Quarterly",
    lastTested: "2023-12-15",
    nextDue: "2024-03-15",
    status: "Scheduled",
    owner: "IT Security Team",
    result: null,
    evidence: null,
  },
  {
    id: "T002",
    controlName: "Backup Verification",
    testType: "Automated Test",
    frequency: "Monthly",
    lastTested: "2024-01-01",
    nextDue: "2024-02-01",
    status: "Overdue",
    owner: "Operations Team",
    result: "Failed",
    evidence: "backup-test-results.pdf",
  },
  {
    id: "T003",
    controlName: "Vulnerability Scanning",
    testType: "Automated Scan",
    frequency: "Weekly",
    lastTested: "2024-01-22",
    nextDue: "2024-01-29",
    status: "In Progress",
    owner: "Security Team",
    result: null,
    evidence: null,
  },
]

export default function ControlTestingPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [currentSort, setCurrentSort] = useState("")

  // Filter and sort options
  const filterOptions = [
    { label: "Scheduled", value: "scheduled" },
    { label: "In Progress", value: "in-progress" },
    { label: "Completed", value: "completed" },
    { label: "Overdue", value: "overdue" },
    { label: "Failed", value: "failed" },
    { label: "Passed", value: "passed" },
    { label: "Weekly", value: "weekly" },
    { label: "Monthly", value: "monthly" },
    { label: "Quarterly", value: "quarterly" },
    { label: "Annual", value: "annual" },
  ]

  const sortOptions = [
    { label: "Due Date (Soonest)", value: "due-asc" },
    { label: "Due Date (Latest)", value: "due-desc" },
    { label: "Last Tested (Newest)", value: "tested-desc" },
    { label: "Last Tested (Oldest)", value: "tested-asc" },
    { label: "Control Name (A-Z)", value: "control-asc" },
    { label: "Status", value: "status" },
    { label: "Owner", value: "owner" },
  ]

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "scheduled": return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300"
      case "in progress": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300"
      case "completed": return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300"
      case "overdue": return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300"
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "scheduled": return <Calendar className="h-4 w-4 text-blue-600" />
      case "in progress": return <Clock className="h-4 w-4 text-yellow-600" />
      case "completed": return <CheckCircle className="h-4 w-4 text-green-600" />
      case "overdue": return <AlertTriangle className="h-4 w-4 text-red-600" />
      default: return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const isOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date()
  }

  const searchConfig = createSearchConfig("controls", {
    placeholder: "Search tests by control name, owner, or test type...",
    actionButton: (
      <Button asChild>
        <Link href="/controls/create">
          <Plus className="mr-2 h-4 w-4" />
          Create Control
        </Link>
      </Button>
    ),
    filterOptions: filterOptions,
    sortOptions: sortOptions
  })

  return (
    <StandardModuleLayout
      {...ModuleLayoutConfigs.list}
      searchConfig={searchConfig}
    >
        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Tests</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">156</div>
              <p className="text-xs text-muted-foreground">
                Across all controls
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Due This Week</CardTitle>
              <Calendar className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">12</div>
              <p className="text-xs text-muted-foreground">
                Scheduled tests
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Overdue</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">5</div>
              <p className="text-xs text-muted-foreground">
                Require attention
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pass Rate</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">94.2%</div>
              <p className="text-xs text-muted-foreground">
                Last 30 days
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Testing Schedule Table */}
        <Card>
          <CardHeader>
            <CardTitle>Control Testing Schedule</CardTitle>
            <CardDescription>
              Testing schedules, results, and evidence management
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Test ID</TableHead>
                  <TableHead>Control</TableHead>
                  <TableHead>Test Type</TableHead>
                  <TableHead>Frequency</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Tested</TableHead>
                  <TableHead>Next Due</TableHead>
                  <TableHead>Owner</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockTests.map((test) => (
                  <TableRow key={test.id}>
                    <TableCell className="font-medium">{test.id}</TableCell>
                    <TableCell>
                      <div className="font-medium">{test.controlName}</div>
                    </TableCell>
                    <TableCell>{test.testType}</TableCell>
                    <TableCell>
                      <Badge variant="outline">{test.frequency}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(test.status)}
                        <Badge className={getStatusColor(test.status)}>
                          {test.status}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      {test.lastTested ? new Date(test.lastTested).toLocaleDateString() : "Never"}
                    </TableCell>
                    <TableCell>
                      <div className={isOverdue(test.nextDue) ? "text-red-600 font-medium" : ""}>
                        {new Date(test.nextDue).toLocaleDateString()}
                        {isOverdue(test.nextDue) && (
                          <div className="text-xs text-red-600">Overdue</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{test.owner}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Testing Calendar */}
        <Card>
          <CardHeader>
            <CardTitle>Upcoming Tests</CardTitle>
            <CardDescription>
              Tests scheduled for the next 30 days
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockTests
                .filter(test => new Date(test.nextDue) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000))
                .map((test) => (
                  <div key={test.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      {getStatusIcon(test.status)}
                      <div>
                        <div className="font-medium">{test.controlName}</div>
                        <div className="text-sm text-muted-foreground">{test.testType} • {test.owner}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{new Date(test.nextDue).toLocaleDateString()}</div>
                      <Badge className={getStatusColor(test.status)} variant="outline">
                        {test.status}
                      </Badge>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
    </StandardModuleLayout>
  )
}
