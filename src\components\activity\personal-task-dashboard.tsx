"use client"

import React from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { 
  CheckSquare, 
  Clock, 
  AlertTriangle, 
  Search, 
  Filter, 
  Plus,
  Calendar,
  User,
  Tag
} from "lucide-react"
import { Task, ActivityMetrics } from "@/types/activity"

interface PersonalTaskDashboardProps {
  tasks: Task[]
  metrics: ActivityMetrics
  upcomingDeadlines: Array<{
    task: Task
    daysUntilDue: number
  }>
}

export function PersonalTaskDashboard({ tasks, metrics, upcomingDeadlines }: PersonalTaskDashboardProps) {
  const [filter, setFilter] = React.useState<"all" | "pending" | "in-progress" | "overdue">("all")
  const [searchQuery, setSearchQuery] = React.useState("")

  const filteredTasks = tasks.filter(task => {
    const matchesFilter = filter === "all" || task.status === filter
    const matchesSearch = task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         task.description.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesFilter && matchesSearch
  })

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "critical": return "destructive"
      case "high": return "default"
      case "medium": return "secondary"
      case "low": return "outline"
      default: return "outline"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "default"
      case "in-progress": return "secondary"
      case "overdue": return "destructive"
      case "pending": return "outline"
      default: return "outline"
    }
  }

  const getDaysUntilDue = (dueDate: Date) => {
    const today = new Date()
    const due = new Date(dueDate)
    const diffTime = due.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckSquare className="h-5 w-5" />
          Personal Task Management
        </CardTitle>
        <CardDescription>
          Centralized view of your assignments with priority ranking and progress tracking
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Task Metrics Overview */}
        <div className="grid gap-4 md:grid-cols-4">
          <div className="space-y-2">
            <div className="text-2xl font-bold">{metrics.tasksAssigned}</div>
            <p className="text-xs text-muted-foreground">Total Assigned</p>
          </div>
          <div className="space-y-2">
            <div className="text-2xl font-bold">{metrics.tasksCompleted}</div>
            <p className="text-xs text-muted-foreground">Completed</p>
          </div>
          <div className="space-y-2">
            <div className="text-2xl font-bold">{metrics.tasksOverdue}</div>
            <p className="text-xs text-muted-foreground">Overdue</p>
          </div>
          <div className="space-y-2">
            <div className="text-2xl font-bold">{metrics.completionRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">Completion Rate</p>
          </div>
        </div>

        {/* Workload Distribution */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Workload Distribution</h4>
          <div className="space-y-2">
            {Object.entries(metrics.workloadDistribution).map(([category, percentage]) => (
              percentage > 0 && (
                <div key={category} className="flex items-center justify-between">
                  <span className="text-sm capitalize">{category.replace('-', ' ')}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-20 bg-muted rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full" 
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium w-8">{percentage}%</span>
                  </div>
                </div>
              )
            ))}
          </div>
        </div>

        {/* Upcoming Deadlines Alert */}
        {upcomingDeadlines.length > 0 && (
          <div className="p-4 border border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              <h4 className="font-medium text-orange-800 dark:text-orange-200">Upcoming Deadlines</h4>
            </div>
            <div className="space-y-2">
              {upcomingDeadlines.slice(0, 3).map(({ task, daysUntilDue }) => (
                <div key={task.id} className="flex items-center justify-between text-sm">
                  <span className="text-orange-700 dark:text-orange-300">{task.title}</span>
                  <Badge variant="outline" className="text-orange-600">
                    {daysUntilDue === 0 ? "Today" : 
                     daysUntilDue === 1 ? "Tomorrow" : 
                     `${daysUntilDue} days`}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Task Filters and Search */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input 
                placeholder="Search tasks..." 
                className="pl-8 w-64"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-1">
              {["all", "pending", "in-progress", "overdue"].map((status) => (
                <Button
                  key={status}
                  variant={filter === status ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilter(status as any)}
                >
                  {status === "all" ? "All" : status.replace("-", " ")}
                </Button>
              ))}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>
            <Button size="sm">
              <Plus className="mr-2 h-4 w-4" />
              New Task
            </Button>
          </div>
        </div>

        {/* Task List */}
        <div className="space-y-4">
          {filteredTasks.map((task) => {
            const daysUntilDue = getDaysUntilDue(task.dueDate)
            const isOverdue = daysUntilDue < 0
            const isDueSoon = daysUntilDue <= 2 && daysUntilDue >= 0

            return (
              <div key={task.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50">
                <div className="space-y-2 flex-1">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{task.title}</h4>
                    <Badge variant={getPriorityColor(task.priority)}>
                      {task.priority}
                    </Badge>
                    <Badge variant={getStatusColor(task.status)}>
                      {task.status.replace("-", " ")}
                    </Badge>
                    {task.sourceModule && (
                      <Badge variant="outline">
                        {task.sourceModule}
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {task.description}
                  </p>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span>Due: {task.dueDate.toLocaleDateString()}</span>
                      {isOverdue && (
                        <Badge variant="destructive" className="text-xs">
                          {Math.abs(daysUntilDue)} days overdue
                        </Badge>
                      )}
                      {isDueSoon && (
                        <Badge variant="secondary" className="text-xs">
                          Due {daysUntilDue === 0 ? "today" : `in ${daysUntilDue} days`}
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-1">
                      <User className="h-3 w-3" />
                      <span>By: {task.assignedBy.split('@')[0]}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{task.estimatedHours}h estimated</span>
                    </div>
                  </div>
                  {task.progress > 0 && (
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>Progress</span>
                        <span>{task.progress}%</span>
                      </div>
                      <Progress value={task.progress} className="h-2" />
                    </div>
                  )}
                  {task.tags.length > 0 && (
                    <div className="flex items-center gap-1">
                      <Tag className="h-3 w-3 text-muted-foreground" />
                      <div className="flex gap-1">
                        {task.tags.slice(0, 3).map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {task.tags.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{task.tags.length - 3}
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    View
                  </Button>
                  {task.status === "pending" && (
                    <Button size="sm">
                      Start
                    </Button>
                  )}
                </div>
              </div>
            )
          })}
        </div>

        {filteredTasks.length === 0 && (
          <div className="text-center py-8">
            <CheckSquare className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-2 text-sm font-medium">No tasks found</h3>
            <p className="mt-1 text-sm text-muted-foreground">
              {searchQuery ? "Try adjusting your search criteria" : "All caught up! No tasks match the current filter."}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
