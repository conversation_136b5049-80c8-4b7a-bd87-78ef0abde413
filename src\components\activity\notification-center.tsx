"use client"

import React from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { 
  Bell, 
  AlertTriangle, 
  Clock, 
  CheckCircle, 
  X,
  <PERSON><PERSON>s,
  Filter,
  MoreHorizontal
} from "lucide-react"
import { Notification } from "@/types/activity"

interface NotificationCenterProps {
  notifications: Notification[]
  unreadCount: number
}

export function NotificationCenter({ notifications, unreadCount }: NotificationCenterProps) {
  const [filter, setFilter] = React.useState<"all" | "unread" | "urgent" | "regulatory">("all")

  const filteredNotifications = notifications.filter(notification => {
    switch (filter) {
      case "unread":
        return !notification.readAt
      case "urgent":
        return notification.priority === "urgent"
      case "regulatory":
        return notification.type === "regulatory-deadline"
      default:
        return true
    }
  })

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "urgent": return <AlertTriangle className="h-4 w-4 text-red-500" />
      case "high": return <AlertTriangle className="h-4 w-4 text-orange-500" />
      case "medium": return <Bell className="h-4 w-4 text-blue-500" />
      case "low": return <Bell className="h-4 w-4 text-gray-500" />
      default: return <Bell className="h-4 w-4" />
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "deadline-reminder": return <Clock className="h-4 w-4" />
      case "approval-request": return <CheckCircle className="h-4 w-4" />
      case "regulatory-deadline": return <AlertTriangle className="h-4 w-4" />
      case "escalation": return <AlertTriangle className="h-4 w-4" />
      default: return <Bell className="h-4 w-4" />
    }
  }

  const getTimeAgo = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMins < 1) return "Just now"
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    return `${diffDays}d ago`
  }

  const markAsRead = (notificationId: string) => {
    // Implementation would update the notification status
    console.log("Mark as read:", notificationId)
  }

  const dismissNotification = (notificationId: string) => {
    // Implementation would dismiss the notification
    console.log("Dismiss notification:", notificationId)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Notification Center
          {unreadCount > 0 && (
            <Badge variant="destructive" className="ml-2">
              {unreadCount}
            </Badge>
          )}
        </CardTitle>
        <CardDescription>
          Intelligent alert filtering with escalation management and regulatory deadline alerts
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Notification Filters */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            {["all", "unread", "urgent", "regulatory"].map((filterType) => (
              <Button
                key={filterType}
                variant={filter === filterType ? "default" : "outline"}
                size="sm"
                onClick={() => setFilter(filterType as any)}
              >
                {filterType === "all" ? "All" : 
                 filterType === "unread" ? "Unread" :
                 filterType === "urgent" ? "Urgent" :
                 "Regulatory"}
              </Button>
            ))}
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </Button>
          </div>
        </div>

        {/* Notification List */}
        <div className="space-y-3">
          {filteredNotifications.map((notification) => (
            <div 
              key={notification.id} 
              className={`p-4 border rounded-lg ${
                !notification.readAt ? "bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800" : ""
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3 flex-1">
                  <div className="flex items-center gap-1 mt-1">
                    {getPriorityIcon(notification.priority)}
                    {getTypeIcon(notification.type)}
                  </div>
                  <div className="space-y-2 flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{notification.title}</h4>
                      <Badge variant={
                        notification.priority === "urgent" ? "destructive" :
                        notification.priority === "high" ? "default" :
                        "secondary"
                      }>
                        {notification.priority}
                      </Badge>
                      <Badge variant="outline">
                        {notification.type.replace("-", " ")}
                      </Badge>
                      {notification.regulatoryFramework && (
                        <Badge variant="outline">
                          {notification.regulatoryFramework}
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {notification.message}
                    </p>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>{getTimeAgo(notification.createdAt)}</span>
                      {notification.sourceModule && (
                        <span>From: {notification.sourceModule}</span>
                      )}
                      {notification.escalationLevel > 0 && (
                        <Badge variant="destructive" className="text-xs">
                          Escalation Level {notification.escalationLevel}
                        </Badge>
                      )}
                      {notification.complianceDeadline && (
                        <span className="text-red-600">
                          Deadline: {notification.complianceDeadline.toLocaleDateString()}
                        </span>
                      )}
                    </div>
                    {notification.tags.length > 0 && (
                      <div className="flex gap-1">
                        {notification.tags.slice(0, 3).map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {notification.tags.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{notification.tags.length - 3}
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {!notification.readAt && (
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => markAsRead(notification.id)}
                    >
                      <CheckCircle className="h-4 w-4" />
                    </Button>
                  )}
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => dismissNotification(notification.id)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              {notification.actionRequired && notification.actionUrl && (
                <div className="mt-3 pt-3 border-t">
                  <Button size="sm" className="w-full">
                    {notification.actionLabel || "Take Action"}
                  </Button>
                </div>
              )}
            </div>
          ))}
        </div>

        {filteredNotifications.length === 0 && (
          <div className="text-center py-8">
            <Bell className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-2 text-sm font-medium">No notifications</h3>
            <p className="mt-1 text-sm text-muted-foreground">
              {filter === "all" ? "You're all caught up!" : `No ${filter} notifications found.`}
            </p>
          </div>
        )}

        {/* Quick Actions */}
        <div className="pt-4 border-t">
          <div className="flex items-center justify-between">
            <Button variant="outline" size="sm">
              Mark All Read
            </Button>
            <Button variant="outline" size="sm">
              Clear Dismissed
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
