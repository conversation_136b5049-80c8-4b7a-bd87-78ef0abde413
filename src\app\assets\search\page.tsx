"use client"

import { useState } from "react"
import { Search, Filter, Package, Building, Network, Users, Workflow } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"

import { sampleAssets } from "@/lib/assets-data"
import { Asset } from "@/types/assets"

const categoryIcons = {
  it: Building,
  ot: Workflow,
  iot: Network,
  identity: Users,
  application: Package,
  vendor: Building,
  process: Workflow
}

export default function AssetSearchPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")

  const filteredAssets = sampleAssets.filter(asset => {
    const matchesSearch = asset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         asset.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         asset.owner.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         asset.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    
    const matchesCategory = selectedCategory === "all" || asset.category === selectedCategory

    return matchesSearch && matchesCategory
  })

  const assetsByCategory = sampleAssets.reduce((acc, asset) => {
    acc[asset.category] = (acc[asset.category] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Asset Search</h1>
          <p className="text-muted-foreground">
            Search across all asset categories with advanced filtering and discovery
          </p>
        </div>
      </div>

      {/* Search Interface */}
      <Card>
        <CardHeader>
          <CardTitle>Global Asset Search</CardTitle>
          <CardDescription>
            Search across all {sampleAssets.length} assets in the system
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search assets by name, description, owner, or tags..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Advanced Filters
            </Button>
          </div>

          {/* Category Filters */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={selectedCategory === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory("all")}
            >
              All Categories ({sampleAssets.length})
            </Button>
            {Object.entries(assetsByCategory).map(([category, count]) => {
              const Icon = categoryIcons[category as keyof typeof categoryIcons]
              return (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className="capitalize"
                >
                  <Icon className="mr-2 h-3 w-3" />
                  {category} ({count})
                </Button>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Search Results */}
      <div className="border-0 rounded-none -mx-4">
        <div className="px-4 py-6 border-b">
          <h3 className="text-lg font-semibold">Search Results</h3>
          <p className="text-sm text-muted-foreground">
            {filteredAssets.length} assets found
            {searchQuery && ` for "${searchQuery}"`}
            {selectedCategory !== "all" && ` in ${selectedCategory} category`}
          </p>
        </div>
        <div className="px-4 py-6">
          {filteredAssets.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No assets found matching your search criteria</p>
              <p className="text-sm">Try adjusting your search terms or filters</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredAssets.map((asset) => {
                const Icon = categoryIcons[asset.category]
                return (
                  <div
                    key={asset.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <Icon className="h-8 w-8 text-muted-foreground" />
                      <div className="space-y-1">
                        <div className="font-medium">{asset.name}</div>
                        {asset.description && (
                          <div className="text-sm text-muted-foreground line-clamp-1">
                            {asset.description}
                          </div>
                        )}
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="capitalize">
                            {asset.category}
                          </Badge>
                          <Badge variant="outline" className="capitalize">
                            {asset.assetType.replace("-", " ")}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            Owner: {asset.owner}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge
                        variant={asset.securityStatus === "compliant" ? "default" : "destructive"}
                      >
                        {asset.securityStatus}
                      </Badge>
                      <Button variant="outline" size="sm" asChild>
                        <a href={`/assets/${asset.category}/${asset.id}`}>
                          View Details
                        </a>
                      </Button>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </div>

      {/* Quick Stats */}
      {searchQuery && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{filteredAssets.length}</div>
              <div className="text-sm text-muted-foreground">Total Results</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">
                {filteredAssets.filter(a => a.securityStatus === "compliant").length}
              </div>
              <div className="text-sm text-muted-foreground">Compliant</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">
                {filteredAssets.filter(a => a.criticalityLevel === "critical").length}
              </div>
              <div className="text-sm text-muted-foreground">Critical</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">
                {Math.round(filteredAssets.reduce((sum, a) => sum + a.riskScore, 0) / filteredAssets.length) || 0}
              </div>
              <div className="text-sm text-muted-foreground">Avg Risk Score</div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
