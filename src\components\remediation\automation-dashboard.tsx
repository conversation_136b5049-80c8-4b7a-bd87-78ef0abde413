"use client"

import React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { Play, Zap, CheckCircle, Clock, Settings, Activity } from "lucide-react"

export function AutomationDashboard() {
  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Play className="h-5 w-5" />
              Active Playbooks
            </CardTitle>
            <CardDescription>Currently executing automation</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-2xl font-bold">8</div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Execution Progress</span>
                <span className="font-medium">75%</span>
              </div>
              <Progress value={75} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Success Rate
            </CardTitle>
            <CardDescription>Automation reliability</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-2xl font-bold">94.2%</div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>This Month</span>
                <span className="font-medium">156/166</span>
              </div>
              <Progress value={94} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Avg Execution Time
            </CardTitle>
            <CardDescription>Performance metrics</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-2xl font-bold">2.1h</div>
            <div className="text-xs text-muted-foreground">
              -15m from last month
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Executions</CardTitle>
          <CardDescription>Latest playbook automation runs</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { name: "Data Breach Response", status: "completed", duration: "45m", success: true },
              { name: "Malware Containment", status: "running", duration: "12m", success: null },
              { name: "Phishing Response", status: "completed", duration: "23m", success: true },
              { name: "DDoS Mitigation", status: "failed", duration: "8m", success: false },
            ].map((execution, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="space-y-1">
                  <h4 className="font-medium">{execution.name}</h4>
                  <p className="text-sm text-muted-foreground">Duration: {execution.duration}</p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={
                    execution.status === "completed" ? "outline" :
                    execution.status === "running" ? "default" : "destructive"
                  }>
                    {execution.status}
                  </Badge>
                  {execution.success === true && <CheckCircle className="h-4 w-4 text-green-600" />}
                  {execution.success === false && <Activity className="h-4 w-4 text-red-600" />}
                  {execution.success === null && <Clock className="h-4 w-4 text-blue-600" />}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
