"use client"

import * as React from "react"
import { Download, Share, Shield, Package, Trash2, Archive, MoreHorizontal } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Artifact } from "@/lib/artifacts-types"

interface ArtifactsBulkActionsProps {
  selectedArtifacts: Artifact[]
  onClearSelection: () => void
  onBulkExport?: (artifacts: Artifact[]) => void
  onBulkShare?: (artifacts: Artifact[]) => void
  onBulkVerify?: (artifacts: Artifact[]) => void
  onBulkArchive?: (artifacts: Artifact[]) => void
  onBulkDelete?: (artifacts: Artifact[]) => void
  onCreateEvidencePackage?: (artifacts: Artifact[]) => void
  className?: string
}

export function ArtifactsBulkActions({
  selectedArtifacts,
  onClearSelection,
  onBulkExport,
  onBulkShare,
  onBulkVerify,
  onBulkArchive,
  onBulkDelete,
  onCreateEvidencePackage,
  className
}: ArtifactsBulkActionsProps) {
  const selectedCount = selectedArtifacts.length

  if (selectedCount === 0) {
    return null
  }

  const handleBulkExport = () => {
    onBulkExport?.(selectedArtifacts)
  }

  const handleBulkShare = () => {
    onBulkShare?.(selectedArtifacts)
  }

  const handleBulkVerify = () => {
    onBulkVerify?.(selectedArtifacts)
  }

  const handleBulkArchive = () => {
    onBulkArchive?.(selectedArtifacts)
  }

  const handleBulkDelete = () => {
    onBulkDelete?.(selectedArtifacts)
  }

  const handleCreateEvidencePackage = () => {
    onCreateEvidencePackage?.(selectedArtifacts)
  }

  // Calculate verification status of selected items
  const verifiedCount = selectedArtifacts.filter(a => a.blockchainStatus === "verified").length
  const pendingCount = selectedArtifacts.filter(a => a.blockchainStatus === "pending").length
  const failedCount = selectedArtifacts.filter(a => a.blockchainStatus === "failed").length
  const notVerifiedCount = selectedArtifacts.filter(a => a.blockchainStatus === "not-verified").length

  // Calculate total file size
  const totalSize = selectedArtifacts.reduce((sum, artifact) => sum + artifact.fileSize, 0)
  const formatFileSize = (bytes: number): string => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 Bytes'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  return (
    <div className={`flex items-center gap-3 p-4 bg-muted/50 border rounded-lg ${className}`}>
      {/* Selection Summary */}
      <div className="flex items-center gap-3">
        <Badge variant="secondary" className="flex items-center gap-1">
          {selectedCount} selected
        </Badge>
        
        <div className="text-sm text-muted-foreground">
          {formatFileSize(totalSize)} total
        </div>

        {/* Verification Status Summary */}
        <div className="flex items-center gap-2">
          {verifiedCount > 0 && (
            <Badge variant="outline" className="text-green-600 border-green-600 text-xs">
              {verifiedCount} verified
            </Badge>
          )}
          {pendingCount > 0 && (
            <Badge variant="outline" className="text-yellow-600 border-yellow-600 text-xs">
              {pendingCount} pending
            </Badge>
          )}
          {failedCount > 0 && (
            <Badge variant="outline" className="text-red-600 border-red-600 text-xs">
              {failedCount} failed
            </Badge>
          )}
          {notVerifiedCount > 0 && (
            <Badge variant="outline" className="text-gray-600 border-gray-600 text-xs">
              {notVerifiedCount} not verified
            </Badge>
          )}
        </div>
      </div>

      <div className="flex-1" />

      {/* Action Buttons */}
      <div className="flex items-center gap-2">
        {/* Export */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleBulkExport}
          className="flex items-center gap-2"
        >
          <Download className="h-4 w-4" />
          Export
        </Button>

        {/* Share */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleBulkShare}
          className="flex items-center gap-2"
        >
          <Share className="h-4 w-4" />
          Share
        </Button>

        {/* Verify */}
        {(pendingCount > 0 || failedCount > 0 || notVerifiedCount > 0) && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleBulkVerify}
            className="flex items-center gap-2"
          >
            <Shield className="h-4 w-4" />
            Verify ({pendingCount + failedCount + notVerifiedCount})
          </Button>
        )}

        {/* Evidence Package */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleCreateEvidencePackage}
          className="flex items-center gap-2"
        >
          <Package className="h-4 w-4" />
          Evidence Package
        </Button>

        {/* More Actions */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Bulk Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            
            <DropdownMenuItem onClick={handleBulkArchive}>
              <Archive className="h-4 w-4 mr-2" />
              Archive Selected
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem 
              onClick={handleBulkDelete}
              className="text-red-600 focus:text-red-600"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Selected
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Clear Selection */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onClearSelection}
          className="text-muted-foreground hover:text-foreground"
        >
          Clear
        </Button>
      </div>
    </div>
  )
}
