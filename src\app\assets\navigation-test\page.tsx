"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { CheckCircle, ExternalLink, Navigation } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { navigationData } from "@/lib/navigation-data"

export default function NavigationTestPage() {
  const pathname = usePathname()
  
  // Find the Assets navigation item
  const assetsNavItem = navigationData.navMain.find(item => item.title === "Assets")
  
  const testPages = [
    { title: "Assets Overview", url: "/assets", description: "Main assets dashboard" },
    { title: "IT Assets", url: "/assets/it", description: "IT assets category page" },
    { title: "IT Asset Detail", url: "/assets/it/it-001", description: "Individual IT asset detail view" },
    { title: "OT Assets", url: "/assets/ot", description: "OT assets category page" },
    { title: "OT Asset Detail", url: "/assets/ot/ot-001", description: "Individual OT asset detail view" },
    { title: "IoT Devices", url: "/assets/iot", description: "IoT devices category page" },
    { title: "IoT Device Detail", url: "/assets/iot/iot-001", description: "Individual IoT device detail view" },
    { title: "Identities", url: "/assets/identities", description: "Identity assets category page" },
    { title: "Identity Detail", url: "/assets/identities/id-001", description: "Individual identity detail view" },
    { title: "Applications", url: "/assets/applications", description: "Application assets category page" },
    { title: "Application Detail", url: "/assets/applications/app-001", description: "Individual application detail view" },
    { title: "Vendors", url: "/assets/vendors", description: "Vendor assets category page" },
    { title: "Vendor Detail", url: "/assets/vendors/vendor-001", description: "Individual vendor detail view" },
    { title: "Processes", url: "/assets/processes", description: "Process assets category page" },
    { title: "Process Detail", url: "/assets/processes/proc-001", description: "Individual process detail view" },
    { title: "Asset Search", url: "/assets/search", description: "Global asset search interface" },
    { title: "Asset Topology", url: "/assets/topology", description: "Asset relationship visualization" },
    { title: "Asset Discovery", url: "/assets/discovery", description: "Discovery configuration and monitoring" },
    { title: "Asset Registration", url: "/assets/register", description: "Manual asset registration form" },
    { title: "Navigation Test", url: "/assets/navigation-test", description: "This navigation test page" },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Assets Sub-Navigation Test</h1>
          <p className="text-muted-foreground">
            Verify that sub-navigation is working correctly across all asset pages
          </p>
        </div>
      </div>

      {/* Current Navigation State */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Navigation className="h-5 w-5" />
            Current Navigation State
          </CardTitle>
          <CardDescription>
            Current pathname and detected navigation configuration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">Current Pathname</div>
              <div className="font-mono text-sm bg-muted p-2 rounded">{pathname}</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">Assets Base URL</div>
              <div className="font-mono text-sm bg-muted p-2 rounded">{assetsNavItem?.url}</div>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="text-sm font-medium text-muted-foreground">Sub-Navigation Items</div>
            <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
              {assetsNavItem?.subModules?.map((subModule) => {
                const isActive = pathname === subModule.url || 
                  (subModule.url !== "/assets" && pathname.startsWith(subModule.url + "/"))
                
                return (
                  <div
                    key={subModule.url}
                    className={`p-3 border rounded-lg ${
                      isActive 
                        ? "border-primary bg-primary/5" 
                        : "border-muted"
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="font-medium text-sm">{subModule.title}</div>
                      {isActive && <CheckCircle className="h-4 w-4 text-primary" />}
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      {subModule.url}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Navigation Links */}
      <Card>
        <CardHeader>
          <CardTitle>Test Navigation Links</CardTitle>
          <CardDescription>
            Click these links to test sub-navigation persistence across different asset pages
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
            {testPages.map((page) => {
              const isCurrentPage = pathname === page.url
              
              return (
                <Button
                  key={page.url}
                  variant={isCurrentPage ? "default" : "outline"}
                  size="sm"
                  asChild
                  className="justify-start h-auto p-3"
                >
                  <Link href={page.url}>
                    <div className="text-left">
                      <div className="font-medium flex items-center gap-2">
                        {page.title}
                        <ExternalLink className="h-3 w-3" />
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {page.description}
                      </div>
                    </div>
                  </Link>
                </Button>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Sub-Navigation Requirements */}
      <Card>
        <CardHeader>
          <CardTitle>Sub-Navigation Requirements</CardTitle>
          <CardDescription>
            Verification checklist for sub-navigation functionality
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <div className="font-medium">Sub-navigation appears on all asset pages</div>
                <div className="text-sm text-muted-foreground">
                  The sub-navigation bar should be visible at the top of every asset-related page
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <div className="font-medium">Correct tab highlighted for category pages</div>
                <div className="text-sm text-muted-foreground">
                  When on /assets/it, the "IT Assets" tab should be highlighted
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <div className="font-medium">Correct tab highlighted for detail pages</div>
                <div className="text-sm text-muted-foreground">
                  When on /assets/it/it-001, the "IT Assets" tab should still be highlighted
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <div className="font-medium">Utility pages have their own tabs</div>
                <div className="text-sm text-muted-foreground">
                  Search, Topology, Discovery, and Register pages have dedicated tabs
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <div className="font-medium">Navigation persists across all sub-pages</div>
                <div className="text-sm text-muted-foreground">
                  Sub-navigation remains visible and functional on all asset-related pages
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div>
              <strong>1. Visual Verification:</strong> Confirm that the sub-navigation bar appears at the top of this page and all other asset pages.
            </div>
            <div>
              <strong>2. Navigation Testing:</strong> Click through the test links above and verify that:
              <ul className="list-disc list-inside ml-4 mt-2 space-y-1">
                <li>The sub-navigation remains visible on every page</li>
                <li>The correct tab is highlighted for each page type</li>
                <li>Detail pages highlight their parent category tab</li>
                <li>All tabs are clickable and functional</li>
              </ul>
            </div>
            <div>
              <strong>3. Consistency Check:</strong> Verify that the sub-navigation styling and behavior is consistent across all pages.
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
