"use client"

import React from "react"
import Link from "next/link"
import { 
  Shield, 
  AlertTriangle, 
  Clock, 
  TrendingUp, 
  Users, 
  CheckCircle,
  Calendar,
  BarChart3,
  Activity,
  Target,
  Zap,
  FileCheck
} from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"

import { sampleControls, controlsDashboardMetrics, controlsByFamily, controlsByStatus } from "@/lib/controls-data"
import { Control } from "@/types/controls"

export function ControlDashboard() {
  // Calculate upcoming tests
  const upcomingTests = sampleControls.filter(control => {
    if (!control.nextTestDate) return false
    const testDate = new Date(control.nextTestDate)
    const now = new Date()
    const thirtyDaysFromNow = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000))
    return testDate <= thirtyDaysFromNow && testDate >= now
  })

  // Calculate recent activity
  const recentActivity = sampleControls
    .sort((a, b) => new Date(b.lastModifiedDate).getTime() - new Date(a.lastModifiedDate).getTime())
    .slice(0, 5)

  // Control family distribution
  const familyData = Object.entries(controlsByFamily)
    .filter(([_, count]) => count > 0)
    .sort(([_, a], [__, b]) => b - a)

  // Status distribution
  const statusData = Object.entries(controlsByStatus)
    .filter(([_, count]) => count > 0)

  // High-risk controls
  const highRiskControls = sampleControls.filter(c => c.riskLevel === "high" || c.riskLevel === "critical")

  return (
    <div className="space-y-6">
      {/* Control Portfolio Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Implementation Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Implementation Rate</span>
                <span className="font-medium">{controlsDashboardMetrics.implementationRate.toFixed(1)}%</span>
              </div>
              <Progress value={controlsDashboardMetrics.implementationRate} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Testing Completion</span>
                <span className="font-medium">{controlsDashboardMetrics.testingCompletionRate.toFixed(1)}%</span>
              </div>
              <Progress value={controlsDashboardMetrics.testingCompletionRate} className="h-2" />
            </div>

            <div className="pt-2 border-t">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Effective Controls</span>
                <Badge variant="default">
                  {controlsDashboardMetrics.effectiveControls}/{controlsDashboardMetrics.totalControls}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Risk & Compliance Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <span className="text-sm">High-Risk Failures</span>
              </div>
              <Badge variant={highRiskControls.filter(c => c.testingStatus === "failed").length > 0 ? "destructive" : "secondary"}>
                {highRiskControls.filter(c => c.testingStatus === "failed").length}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <span className="text-sm">Open Findings</span>
              </div>
              <Badge variant={controlsDashboardMetrics.openFindings > 0 ? "outline" : "secondary"}>
                {controlsDashboardMetrics.openFindings}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span className="text-sm">Remediation Progress</span>
              </div>
              <span className="text-sm font-medium">
                {controlsDashboardMetrics.remediationProgress.toFixed(1)}%
              </span>
            </div>

            <div className="pt-2 border-t">
              <Button variant="outline" size="sm" className="w-full" asChild>
                <Link href="/controls?tab=remediation">
                  View Remediation Tracker
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Testing Calendar</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Tests Due (30 days)</span>
              </div>
              <Badge variant={upcomingTests.length > 0 ? "destructive" : "secondary"}>
                {upcomingTests.length}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Overdue Tests</span>
              </div>
              <Badge variant="outline">
                {sampleControls.filter(c => c.nextTestDate && new Date(c.nextTestDate) < new Date()).length}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Automated Tests</span>
              </div>
              <Badge variant="outline">
                {sampleControls.filter(c => c.testingMethod === "automated").length}
              </Badge>
            </div>

            <div className="pt-2 border-t">
              <Button variant="outline" size="sm" className="w-full" asChild>
                <Link href="/controls?tab=testing">
                  View Testing Schedule
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Performance Analytics</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Avg Effectiveness</span>
                <span className="font-medium">{controlsDashboardMetrics.averageEffectivenessScore.toFixed(1)}%</span>
              </div>
              <Progress value={controlsDashboardMetrics.averageEffectivenessScore} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Automation Level</span>
                <span className="font-medium">{controlsDashboardMetrics.automationLevel.toFixed(1)}%</span>
              </div>
              <Progress value={controlsDashboardMetrics.automationLevel} className="h-2" />
            </div>

            <div className="pt-2 border-t">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Agent Monitored</span>
                <Badge variant="outline">
                  {controlsDashboardMetrics.agentMonitoredControls}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Control Distribution and Recent Activity */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Control Distribution by Family</CardTitle>
            <CardDescription>
              Breakdown of controls across different control families
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {familyData.slice(0, 8).map(([family, count]) => (
                <div key={family} className="flex items-center justify-between">
                  <span className="text-sm capitalize">{family.replace('-', ' ')}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-20 bg-muted rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full" 
                        style={{ width: `${(count / controlsDashboardMetrics.totalControls) * 100}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium w-8 text-right">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Recent Control Activity</CardTitle>
            <CardDescription>
              Latest control updates and modifications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentActivity.map((control) => (
                <div key={control.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-2 h-2 rounded-full ${
                      control.testingStatus === "passed" ? "bg-green-500" :
                      control.testingStatus === "failed" ? "bg-red-500" :
                      "bg-yellow-500"
                    }`}></div>
                    <div>
                      <Link 
                        href={`/controls/${control.id}`}
                        className="text-sm font-medium hover:text-primary cursor-pointer"
                      >
                        {control.controlId} - {control.title}
                      </Link>
                      <p className="text-xs text-muted-foreground">
                        {control.implementationStatus === "implemented" ? "Updated" : "Modified"}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant={
                      control.testingStatus === "passed" ? "default" :
                      control.testingStatus === "failed" ? "destructive" :
                      "outline"
                    } className="text-xs">
                      {control.testingStatus}
                    </Badge>
                    <p className="text-xs text-muted-foreground mt-1">
                      {new Date(control.lastModifiedDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="pt-3 border-t">
              <Button variant="outline" size="sm" className="w-full" asChild>
                <Link href="/controls?tab=controls">
                  View All Controls
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Framework Compliance Rates */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Framework Compliance Rates</CardTitle>
          <CardDescription>
            Control implementation status across different compliance frameworks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">NIST CSF 2.0</span>
                <span className="text-sm">92%</span>
              </div>
              <Progress value={92} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">ISO 27001</span>
                <span className="text-sm">87%</span>
              </div>
              <Progress value={87} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">SOC 2</span>
                <span className="text-sm">95%</span>
              </div>
              <Progress value={95} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Implementation Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Implementation Status Overview</CardTitle>
          <CardDescription>
            Current implementation status distribution across all controls
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-5">
            {statusData.map(([status, count]) => (
              <div key={status} className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold">{count}</div>
                <div className="text-sm text-muted-foreground capitalize">
                  {status.replace('-', ' ')}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
