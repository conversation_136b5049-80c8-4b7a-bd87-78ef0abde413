# GRCOS Flat Navigation Structure

## Overview

GRCOS has been revamped to implement a flat, agent-orchestrated architecture that eliminates the traditional module-based structure in favor of a streamlined navigation system. This new structure provides direct access to core GRC functions while maintaining contextual top navigation for sub-sections.

## New Navigation Structure

### Core Navigation Sections

1. **Overview** (`/overview`)
   - Executive dashboard with System Agent intelligence summaries
   - Key performance indicators across all GRC domains
   - Real-time agent activity status and orchestration health
   - Cross-domain risk posture and compliance status

2. **Activity** (`/activity`)
   - Unified event stream from all system components
   - Agent-initiated actions and automated responses
   - Security events, compliance activities, and system operations
   - Audit trail with blockchain verification links

3. **Assets** (`/assets`)
   - Blockchain-Secured CMS Foundation
   - IT Assets: Servers, workstations, network infrastructure
   - OT Assets: Industrial control systems, SCADA, manufacturing equipment
   - IoT Devices: Connected sensors, smart devices, edge computing
   - Identities: Users, service accounts, privileged access management
   - Applications: Software inventory, cloud services, SaaS platforms
   - Vendors: Third-party relationships, supplier risk management
   - Processes: Business workflows, operational procedures, governance processes

4. **Monitor** (`/monitor`)
   - Real-time security monitoring across all asset categories
   - AI-powered anomaly detection and behavioral analysis
   - Unified SIEM dashboard with Wazuh integration
   - Cross-environment threat correlation (IT/OT/IoT)
   - System Agent coordinated alert prioritization

5. **Frameworks** (`/frameworks`)
   - Compliance framework library (NIST, ISO 27001, PCI DSS, HIPAA, SOC 2)
   - OSCAL-based framework modeling and management
   - Compliance Agent automated framework analysis and gap identification
   - Multi-framework harmonization and control mapping

6. **Controls** (`/controls`)
   - Security control implementation and testing
   - OSCAL standardized control definitions and procedures
   - OPA policy enforcement integration
   - Compliance Agent automated control assessment and validation

7. **Policies** (`/policies`)
   - Policy management and enforcement across all environments
   - OPA-based policy-as-code implementation
   - Compliance Agent policy translation and application
   - Cross-environment policy consistency verification

8. **Assessments** (`/assessments`)
   - Automated risk assessments and gap analysis
   - Assessment Agent orchestrated control testing
   - Quantitative risk analysis with Open Source Risk Engine
   - Continuous assessment scheduling and execution

9. **Workflows** (`/workflows`)
   - Business process automation with Flowable engine
   - Workflow Agent orchestrated process optimization
   - Custom workflow design and template management
   - Integration with compliance and security processes

10. **Remediation** (`/remediation`)
    - Incident response and security remediation coordination
    - Remediation Agent automated response orchestration
    - DFIR-IRIS integration for structured investigation
    - Cross-environment remediation tracking and validation

11. **Reports** (`/reports`)
    - Reporting Agent generated compliance documentation
    - Interactive dashboards and static report artifacts
    - Executive summaries and regulatory submission preparation
    - Real-time compliance status and trend analysis

12. **Artifacts** (`/artifacts`)
    - Blockchain-secured evidence repository
    - Immutable compliance documentation storage
    - Cryptographic verification of all security artifacts
    - Audit-ready evidence with tamper-proof integrity

13. **Portals** (`/portals`)
    - Stakeholder-specific trust portals and interfaces
    - AI-powered chatbots for audit facilitation
    - Self-service compliance questionnaire completion
    - Secure stakeholder communication and evidence sharing

## Key Features

### Flat Architecture Benefits
- **Simplified Navigation**: Direct access to core functions without nested module hierarchies
- **Agent-Orchestrated**: Each section leverages specialized AI agents for automation
- **Contextual Top Navigation**: Sub-sections appear in the top navigation bar when relevant
- **Consistent User Experience**: Uniform card-based interface across all sections

### Technology Integration
- **Blockchain Security**: Hyperledger Fabric for asset and evidence verification
- **OSCAL Standardization**: Open Security Controls Assessment Language for compliance
- **OPA Policy Engine**: Open Policy Agent for unified policy enforcement
- **Flowable Workflows**: Business process automation engine
- **Wazuh SIEM**: Security information and event management integration

### Navigation Behavior
- **Auto-Navigation**: Automatically navigates to first sub-section when main section is accessed
- **Contextual Top Bar**: Shows relevant sub-sections in the top navigation
- **Responsive Design**: Optimized for desktop-focused usage
- **Theme Support**: Full dark/light theme compatibility

## Migration from Module Structure

The previous module-based structure (Dashboard, LightHouse, ComplianceCentre, ActionCentre, TrustCentre) has been flattened into the new 13-section structure. This provides:

- More direct access to specific functions
- Reduced navigation complexity
- Better alignment with agent-orchestrated workflows
- Improved user experience for GRC professionals

## Implementation Details

### File Structure
```
src/app/
├── overview/
├── activity/
├── assets/
├── monitor/
├── frameworks/
├── controls/
├── policies/
├── assessments/
├── workflows/
├── remediation/
├── reports/
├── artifacts/
└── portals/
```

### Navigation Data
The navigation structure is defined in `src/lib/navigation-data.ts` with:
- Main navigation items with icons and URLs
- Sub-modules for contextual top navigation
- Descriptions and metadata for each section

### Routing
- Main sections route to overview pages with cards for sub-sections
- Sub-sections appear in contextual top navigation
- Auto-redirect to first sub-section when appropriate
- Consistent layout and metadata across all sections
