import { Artifact, StorageMetrics, ArtifactType, SourceModule, BlockchainStatus, AccessLevel } from "./artifacts-types"

// Sample artifacts data for demonstration
export const sampleArtifacts: Artifact[] = [
  {
    id: "art-001",
    name: "SOC 2 Type II Audit Report 2024",
    description: "Annual SOC 2 Type II audit report covering security, availability, and confidentiality controls",
    artifactType: "audit-evidence",
    sourceModule: "assessments",
    createdAt: new Date("2024-01-15T10:30:00Z"),
    updatedAt: new Date("2024-01-15T10:30:00Z"),
    createdBy: "<EMAIL>",
    fileSize: 2457600, // 2.4 MB
    fileFormat: "PDF",
    downloadUrl: "/artifacts/download/art-001",
    blockchainStatus: "verified",
    blockchainHash: "0x7d865e959b2466918c9863afca942d0fb89d7c9ac0c99bafc3749504ded97730",
    blockchainBlock: "0x1a2b3c",
    verificationTimestamp: new Date("2024-01-15T10:35:00Z"),
    accessLevel: "restricted",
    tags: ["soc2", "audit", "annual", "security"],
    relatedControls: ["AC-1", "AC-2", "SC-1"],
    relatedFrameworks: ["SOC 2", "NIST CSF 2.0"],
    isArchived: false,
    accessHistory: [
      {
        id: "acc-001",
        userId: "user-001",
        userName: "John Doe",
        action: "download",
        timestamp: new Date("2024-01-16T09:15:00Z"),
        ipAddress: "*************"
      }
    ],
    chainOfCustody: [
      {
        id: "coc-001",
        action: "created",
        userId: "user-audit",
        userName: "Audit Team",
        timestamp: new Date("2024-01-15T10:30:00Z"),
        details: "Initial upload from external auditor"
      }
    ]
  },
  {
    id: "art-002",
    name: "Network Security Policy v3.2",
    description: "Updated network security policy including zero-trust architecture requirements",
    artifactType: "policy-document",
    sourceModule: "policies",
    createdAt: new Date("2024-01-10T14:20:00Z"),
    updatedAt: new Date("2024-01-12T16:45:00Z"),
    createdBy: "<EMAIL>",
    fileSize: 1048576, // 1 MB
    fileFormat: "DOCX",
    downloadUrl: "/artifacts/download/art-002",
    blockchainStatus: "verified",
    blockchainHash: "0x9f4e2a8b7c6d5e4f3a2b1c0d9e8f7a6b5c4d3e2f1a0b9c8d7e6f5a4b3c2d1e0f",
    blockchainBlock: "0x2b3c4d",
    verificationTimestamp: new Date("2024-01-12T16:50:00Z"),
    accessLevel: "internal",
    tags: ["policy", "network", "security", "zero-trust"],
    relatedControls: ["SC-7", "SC-8", "AC-4"],
    relatedFrameworks: ["NIST CSF 2.0", "ISO 27001"],
    isArchived: false,
    accessHistory: [],
    chainOfCustody: [
      {
        id: "coc-002",
        action: "created",
        userId: "user-sec",
        userName: "Security Team",
        timestamp: new Date("2024-01-10T14:20:00Z"),
        details: "Initial policy creation"
      },
      {
        id: "coc-003",
        action: "modified",
        userId: "user-sec",
        userName: "Security Team",
        timestamp: new Date("2024-01-12T16:45:00Z"),
        details: "Updated zero-trust requirements",
        previousHash: "0x8e3d1a7b6c5d4e3f2a1b0c9d8e7f6a5b4c3d2e1f0a9b8c7d6e5f4a3b2c1d0e9f"
      }
    ]
  },
  {
    id: "art-003",
    name: "Vulnerability Assessment Q1 2024",
    description: "Quarterly vulnerability assessment report for all critical systems",
    artifactType: "assessment-report",
    sourceModule: "assessments",
    createdAt: new Date("2024-01-20T11:00:00Z"),
    updatedAt: new Date("2024-01-20T11:00:00Z"),
    createdBy: "<EMAIL>",
    fileSize: 5242880, // 5 MB
    fileFormat: "PDF",
    downloadUrl: "/artifacts/download/art-003",
    blockchainStatus: "pending",
    accessLevel: "confidential",
    tags: ["vulnerability", "assessment", "quarterly", "critical"],
    relatedControls: ["RA-5", "SI-2", "CA-2"],
    relatedFrameworks: ["NIST CSF 2.0"],
    isArchived: false,
    accessHistory: [],
    chainOfCustody: [
      {
        id: "coc-004",
        action: "created",
        userId: "system-scanner",
        userName: "Vulnerability Scanner",
        timestamp: new Date("2024-01-20T11:00:00Z"),
        details: "Automated report generation"
      }
    ]
  },
  {
    id: "art-004",
    name: "Incident Response Plan 2024",
    description: "Updated incident response procedures and contact information",
    artifactType: "policy-document",
    sourceModule: "workflows",
    createdAt: new Date("2024-01-05T09:30:00Z"),
    updatedAt: new Date("2024-01-05T09:30:00Z"),
    createdBy: "<EMAIL>",
    fileSize: 2097152, // 2 MB
    fileFormat: "PDF",
    downloadUrl: "/artifacts/download/art-004",
    blockchainStatus: "verified",
    blockchainHash: "0xa5b4c3d2e1f0a9b8c7d6e5f4a3b2c1d0e9f8a7b6c5d4e3f2a1b0c9d8e7f6a5b4",
    blockchainBlock: "0x3c4d5e",
    verificationTimestamp: new Date("2024-01-05T09:35:00Z"),
    accessLevel: "restricted",
    tags: ["incident", "response", "procedures", "contacts"],
    relatedControls: ["IR-1", "IR-4", "IR-8"],
    relatedFrameworks: ["NIST CSF 2.0", "ISO 27035"],
    isArchived: false,
    accessHistory: [],
    chainOfCustody: [
      {
        id: "coc-005",
        action: "created",
        userId: "user-ir",
        userName: "Incident Response Team",
        timestamp: new Date("2024-01-05T09:30:00Z"),
        details: "Annual plan update"
      }
    ]
  },
  {
    id: "art-005",
    name: "Security Training Completion Records",
    description: "Employee security awareness training completion certificates",
    artifactType: "training-record",
    sourceModule: "manual-upload",
    createdAt: new Date("2024-01-18T15:45:00Z"),
    updatedAt: new Date("2024-01-18T15:45:00Z"),
    createdBy: "<EMAIL>",
    fileSize: 3145728, // 3 MB
    fileFormat: "XLSX",
    downloadUrl: "/artifacts/download/art-005",
    blockchainStatus: "failed",
    accessLevel: "internal",
    tags: ["training", "security", "awareness", "certificates"],
    relatedControls: ["AT-2", "AT-3"],
    relatedFrameworks: ["NIST CSF 2.0"],
    isArchived: false,
    accessHistory: [],
    chainOfCustody: [
      {
        id: "coc-006",
        action: "created",
        userId: "user-hr",
        userName: "HR Training Team",
        timestamp: new Date("2024-01-18T15:45:00Z"),
        details: "Bulk upload of training records"
      }
    ]
  }
]

// Note: StorageMetrics removed as part of streamlined artifacts interface

// Helper functions
export function getArtifactTypeLabel(type: ArtifactType): string {
  const labels: Record<ArtifactType, string> = {
    "control-evidence": "Control Evidence",
    "assessment-report": "Assessment Report",
    "policy-document": "Policy Document",
    "incident-report": "Incident Report",
    "audit-evidence": "Audit Evidence",
    "compliance-certificate": "Compliance Certificate",
    "risk-assessment": "Risk Assessment",
    "security-log": "Security Log",
    "configuration-backup": "Configuration Backup",
    "training-record": "Training Record"
  }
  return labels[type]
}

export function getSourceModuleLabel(module: SourceModule): string {
  const labels: Record<SourceModule, string> = {
    "overview": "Overview",
    "activity": "Activity",
    "assets": "Assets",
    "monitor": "Monitor",
    "frameworks": "Frameworks",
    "controls": "Controls",
    "policies": "Policies",
    "assessments": "Assessments",
    "workflows": "Workflows",
    "remediation": "Remediation",
    "reports": "Reports",
    "portals": "Portals",
    "manual-upload": "Manual Upload"
  }
  return labels[module]
}

export function getBlockchainStatusLabel(status: BlockchainStatus): string {
  const labels: Record<BlockchainStatus, string> = {
    "verified": "Verified",
    "pending": "Pending",
    "failed": "Failed",
    "not-verified": "Not Verified"
  }
  return labels[status]
}

export function getAccessLevelLabel(level: AccessLevel): string {
  const labels: Record<AccessLevel, string> = {
    "public": "Public",
    "internal": "Internal",
    "restricted": "Restricted",
    "confidential": "Confidential"
  }
  return labels[level]
}

export function formatFileSize(bytes: number): string {
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  if (bytes === 0) return '0 Bytes'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}
