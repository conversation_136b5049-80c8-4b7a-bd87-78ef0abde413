"use client"

import { useState } from "react"
import Link from "next/link"
import { Plus, TrendingUp, TrendingDown, BarChart3, Target, AlertTriangle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { StandardModuleLayout, ModuleLayoutConfigs, createSearchConfig } from "@/components/standard-module-layout"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

// Mock effectiveness data
const mockEffectiveness = [
  {
    id: "C001",
    controlName: "Multi-Factor Authentication",
    category: "Access Control",
    effectivenessScore: 95,
    trend: "up",
    lastAssessed: "2024-01-15",
    findings: 0,
    recommendations: 1,
    status: "Effective",
  },
  {
    id: "C002",
    controlName: "Data Encryption",
    category: "Data Protection",
    effectivenessScore: 88,
    trend: "stable",
    lastAssessed: "2024-01-10",
    findings: 1,
    recommendations: 2,
    status: "Effective",
  },
  {
    id: "C003",
    controlName: "Incident Response Plan",
    category: "Incident Management",
    effectivenessScore: 72,
    trend: "down",
    lastAssessed: "2024-01-08",
    findings: 3,
    recommendations: 4,
    status: "Partially Effective",
  },
]

export default function ControlEffectivenessPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [currentSort, setCurrentSort] = useState("")

  // Filter and sort options
  const filterOptions = [
    { label: "Effective", value: "effective" },
    { label: "Partially Effective", value: "partially-effective" },
    { label: "Ineffective", value: "ineffective" },
    { label: "Not Assessed", value: "not-assessed" },
    { label: "Access Control", value: "access-control" },
    { label: "Data Protection", value: "data-protection" },
    { label: "Network Security", value: "network-security" },
    { label: "Incident Management", value: "incident-management" },
    { label: "Trending Up", value: "trending-up" },
    { label: "Trending Down", value: "trending-down" },
    { label: "Stable", value: "stable" },
  ]

  const sortOptions = [
    { label: "Effectiveness (High-Low)", value: "effectiveness-desc" },
    { label: "Effectiveness (Low-High)", value: "effectiveness-asc" },
    { label: "Control Name (A-Z)", value: "name-asc" },
    { label: "Control Name (Z-A)", value: "name-desc" },
    { label: "Last Assessed (Newest)", value: "assessed-desc" },
    { label: "Last Assessed (Oldest)", value: "assessed-asc" },
    { label: "Findings (High-Low)", value: "findings-desc" },
    { label: "Category", value: "category" },
  ]

  const getEffectivenessColor = (score: number) => {
    if (score >= 90) return "text-green-600"
    if (score >= 75) return "text-yellow-600"
    return "text-red-600"
  }

  const getEffectivenessStatus = (score: number) => {
    if (score >= 90) return "Effective"
    if (score >= 75) return "Partially Effective"
    return "Ineffective"
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up": return <TrendingUp className="h-4 w-4 text-green-600" />
      case "down": return <TrendingDown className="h-4 w-4 text-red-600" />
      default: return <BarChart3 className="h-4 w-4 text-gray-600" />
    }
  }

  const searchConfig = createSearchConfig("controls", {
    placeholder: "Search controls by name, category, or effectiveness...",
    actionButton: (
      <Button asChild>
        <Link href="/controls/create">
          <Plus className="mr-2 h-4 w-4" />
          Create Control
        </Link>
      </Button>
    ),
    filterOptions: filterOptions,
    sortOptions: sortOptions
  })

  return (
    <StandardModuleLayout
      {...ModuleLayoutConfigs.list}
      searchConfig={searchConfig}
    >
        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Effectiveness</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">85.2%</div>
              <p className="text-xs text-muted-foreground">
                +2.3% from last month
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Effective Controls</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">298</div>
              <p className="text-xs text-muted-foreground">
                87% of total controls
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Needs Improvement</CardTitle>
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">32</div>
              <p className="text-xs text-muted-foreground">
                Partially effective
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Ineffective</CardTitle>
              <TrendingDown className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">12</div>
              <p className="text-xs text-muted-foreground">
                Require immediate action
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Effectiveness by Category */}
        <Card>
          <CardHeader>
            <CardTitle>Effectiveness by Category</CardTitle>
            <CardDescription>
              Control effectiveness metrics across different categories
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { name: "Access Controls", score: 92, total: 89, trend: "up" },
                { name: "Data Protection", score: 88, total: 67, trend: "stable" },
                { name: "Network Security", score: 83, total: 54, trend: "up" },
                { name: "Incident Response", score: 79, total: 32, trend: "down" },
                { name: "Business Continuity", score: 86, total: 28, trend: "up" },
              ].map((category) => (
                <div key={category.name} className="flex items-center space-x-4">
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium">{category.name}</p>
                      <div className="flex items-center gap-2">
                        {getTrendIcon(category.trend)}
                        <span className="text-sm text-muted-foreground">
                          {category.score}% ({category.total} controls)
                        </span>
                      </div>
                    </div>
                    <Progress value={category.score} className="h-2" />
                  </div>
                  <Badge variant="outline" className={getEffectivenessColor(category.score)}>
                    {category.score}%
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Effectiveness Details Table */}
        <div className="border-0 rounded-none -mx-4">
          <div className="px-4 py-6 border-b">
            <h3 className="text-lg font-semibold">Control Effectiveness Details</h3>
            <p className="text-sm text-muted-foreground">
              Detailed effectiveness assessment for individual controls
            </p>
          </div>
          <div className="px-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Control ID</TableHead>
                  <TableHead>Control Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Effectiveness</TableHead>
                  <TableHead>Trend</TableHead>
                  <TableHead>Findings</TableHead>
                  <TableHead>Last Assessed</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockEffectiveness.map((control) => (
                  <TableRow key={control.id}>
                    <TableCell className="font-medium">{control.id}</TableCell>
                    <TableCell>
                      <div className="font-medium">{control.controlName}</div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{control.category}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className={`text-lg font-bold ${getEffectivenessColor(control.effectivenessScore)}`}>
                          {control.effectivenessScore}%
                        </div>
                        <Progress value={control.effectivenessScore} className="w-16 h-2" />
                      </div>
                    </TableCell>
                    <TableCell>
                      {getTrendIcon(control.trend)}
                    </TableCell>
                    <TableCell>
                      <div className="text-center">
                        <div className="font-medium">{control.findings}</div>
                        <div className="text-xs text-muted-foreground">
                          {control.recommendations} recommendations
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {new Date(control.lastAssessed).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant="outline" 
                        className={getEffectivenessColor(control.effectivenessScore)}
                      >
                        {control.status}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* Improvement Recommendations */}
        <Card>
          <CardHeader>
            <CardTitle>Improvement Recommendations</CardTitle>
            <CardDescription>
              Priority recommendations to enhance control effectiveness
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                {
                  priority: "High",
                  control: "Incident Response Plan",
                  recommendation: "Update incident response procedures and conduct tabletop exercises",
                  impact: "Improve response time by 40%"
                },
                {
                  priority: "Medium",
                  control: "Data Encryption",
                  recommendation: "Implement key rotation automation",
                  impact: "Reduce manual key management overhead"
                },
                {
                  priority: "Low",
                  control: "Multi-Factor Authentication",
                  recommendation: "Expand MFA coverage to additional systems",
                  impact: "Increase security coverage by 15%"
                },
              ].map((rec, index) => (
                <div key={index} className="flex items-start space-x-4 p-4 border rounded-lg">
                  <Badge variant={rec.priority === 'High' ? 'destructive' : rec.priority === 'Medium' ? 'default' : 'secondary'}>
                    {rec.priority}
                  </Badge>
                  <div className="flex-1">
                    <div className="font-medium">{rec.control}</div>
                    <div className="text-sm text-muted-foreground mt-1">{rec.recommendation}</div>
                    <div className="text-xs text-green-600 mt-1">Expected Impact: {rec.impact}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
