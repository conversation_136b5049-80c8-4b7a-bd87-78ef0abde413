"use client"

import React from "react"
import Link from "next/link"
import { Plus, Search, FileText, Shield, Database, Activity, Eye, Clock } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

import { InvestigationDashboard } from "@/components/remediation/investigation-dashboard"
import { InvestigationTable } from "@/components/remediation/investigation-table"
import { EvidenceVault } from "@/components/remediation/evidence-vault"
import { IOCDatabase } from "@/components/remediation/ioc-database"
import { sampleInvestigations, sampleEvidence, sampleIOCs, remediationMetrics } from "@/lib/remediation-data"

export default function RemediationInvestigationPage() {
  const [activeTab, setActiveTab] = React.useState("overview")

  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Digital Forensics & Investigation</h1>
            <p className="text-muted-foreground">
              DFIR-IRIS case management, evidence repository, timeline reconstruction, and IOC tracking
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" asChild>
              <Link href="/remediation/incidents">
                <Activity className="mr-2 h-4 w-4" />
                Incidents
              </Link>
            </Button>
            <Button variant="outline">
              <Search className="mr-2 h-4 w-4" />
              Search Evidence
            </Button>
            <Button asChild>
              <Link href="/remediation/investigation/create">
                <Plus className="mr-2 h-4 w-4" />
                New Investigation
              </Link>
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-4 mt-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Cases</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{remediationMetrics.investigations.active}</div>
              <p className="text-xs text-muted-foreground">
                {remediationMetrics.investigations.total} total cases
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Evidence Items</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{remediationMetrics.investigations.evidenceCollected}</div>
              <p className="text-xs text-muted-foreground">
                +12 this week
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">IOCs Identified</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{remediationMetrics.investigations.iocsIdentified}</div>
              <p className="text-xs text-muted-foreground">
                +5 new indicators
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Case Duration</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2.1d</div>
              <p className="text-xs text-muted-foreground">
                -0.3d from last month
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="flex-1 space-y-4">
        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="cases">DFIR Cases</TabsTrigger>
            <TabsTrigger value="evidence">Evidence Vault</TabsTrigger>
            <TabsTrigger value="iocs">IOC Database</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <InvestigationDashboard />
          </TabsContent>

          <TabsContent value="cases" className="space-y-4">
            <InvestigationTable investigations={sampleInvestigations} />
          </TabsContent>

          <TabsContent value="evidence" className="space-y-4">
            <EvidenceVault evidence={sampleEvidence} />
          </TabsContent>

          <TabsContent value="iocs" className="space-y-4">
            <IOCDatabase iocs={sampleIOCs} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
