"use client"

import React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Database, Search, Filter, Download, Shield } from "lucide-react"
import { Evidence } from "@/types/remediation"

interface EvidenceVaultProps {
  evidence: Evidence[]
}

export function EvidenceVault({ evidence }: EvidenceVaultProps) {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Digital Evidence Vault
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input placeholder="Search evidence..." className="pl-8 w-64" />
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
              <Button variant="outline" size="sm">
                <Shield className="mr-2 h-4 w-4" />
                Chain of Custody
              </Button>
            </div>
          </div>
          
          <div className="space-y-4">
            {evidence.map((item) => (
              <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{item.name}</h4>
                    <Badge variant="outline">{item.type}</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{item.description}</p>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span>Size: {(item.size / 1024 / 1024 / 1024).toFixed(1)}GB</span>
                    <span>Collected: {new Date(item.collectedAt).toLocaleDateString()}</span>
                    <span>By: {item.collectedBy}</span>
                  </div>
                  <div className="text-xs font-mono text-muted-foreground">
                    Hash: {item.hash.substring(0, 32)}...
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">View Details</Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
