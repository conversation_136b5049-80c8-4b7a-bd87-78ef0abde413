export default function AnomalyDetectionPage() {
  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Anomaly Detection</h1>
        <p className="text-muted-foreground">
          AI-powered anomaly detection and behavioral analysis
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Anomalies Detected</h3>
          <p className="text-2xl font-bold text-orange-600">3</p>
          <p className="text-sm text-muted-foreground">Last 24 hours</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">AI Confidence</h3>
          <p className="text-2xl font-bold text-green-600">94.7%</p>
          <p className="text-sm text-muted-foreground">Detection accuracy</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Behavioral Analysis</h3>
          <p className="text-2xl font-bold text-blue-600">Active</p>
          <p className="text-sm text-muted-foreground">Continuous monitoring</p>
        </div>
      </div>
    </div>
  )
}
